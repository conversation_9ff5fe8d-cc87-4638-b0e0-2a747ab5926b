"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.RPAProcessManager = void 0;
const process_executor_1 = require("./process-executor");
const logger_1 = require("../../utils/logger");
class RPAProcessManager {
    constructor(connectionManager) {
        this.logger = (0, logger_1.createLogger)('RPAProcessManager');
        this.processes = new Map();
        this.processDefinitions = new Map();
        this.executor = new process_executor_1.RPAProcessExecutor();
        this.connectionManager = connectionManager;
        // Listen to execution events
        this.executor.on('execution', this.handleExecutionEvent.bind(this));
    }
    async createProcess(definition) {
        try {
            // Validate process definition
            this.validateProcessDefinition(definition);
            const process = {
                id: definition.id,
                name: definition.name,
                description: definition.description,
                status: 'idle',
                variables: {},
                logs: []
            };
            this.processes.set(definition.id, process);
            this.processDefinitions.set(definition.id, definition);
            this.logger.info(`Created process: ${definition.name} (${definition.id})`);
            return process;
        }
        catch (error) {
            this.logger.error('Failed to create process:', error);
            throw error;
        }
    }
    async executeProcess(processId, config = {}) {
        try {
            const process = this.processes.get(processId);
            if (!process) {
                throw new Error(`Process not found: ${processId}`);
            }
            const definition = this.processDefinitions.get(processId);
            if (!definition) {
                throw new Error(`Process definition not found: ${processId}`);
            }
            // Check if engine is available
            const availableEngines = this.connectionManager.getAvailableEngines();
            if (availableEngines.length === 0) {
                throw new Error('No RPA engines available');
            }
            // Prepare execution configuration
            const executionConfig = {
                engineId: availableEngines[0].id,
                processId,
                parameters: config.parameters || {},
                timeout: config.timeout || 300000,
                retryCount: config.retryCount || 0,
                debugMode: config.debugMode || false
            };
            this.logger.info(`Executing process: ${processId}`);
            // Execute the process
            const result = await this.executor.executeProcess(executionConfig, process);
            return result;
        }
        catch (error) {
            this.logger.error(`Failed to execute process ${processId}:`, error);
            throw error;
        }
    }
    async stopProcess(processId) {
        try {
            const activeExecutions = this.executor.getActiveExecutions();
            const execution = activeExecutions.find(exec => exec.processId === processId);
            if (execution) {
                return await this.executor.cancelExecution(execution.id);
            }
            return false;
        }
        catch (error) {
            this.logger.error(`Failed to stop process ${processId}:`, error);
            return false;
        }
    }
    getProcess(processId) {
        return this.processes.get(processId);
    }
    getProcessDefinition(processId) {
        return this.processDefinitions.get(processId);
    }
    getAllProcesses() {
        return Array.from(this.processes.values());
    }
    getActiveProcesses() {
        return this.getAllProcesses().filter(process => process.status === 'running');
    }
    async loadProcessFromFile(filePath) {
        try {
            const fs = require('fs');
            const path = require('path');
            const content = fs.readFileSync(filePath, 'utf8');
            const definition = JSON.parse(content);
            return await this.createProcess(definition);
        }
        catch (error) {
            this.logger.error(`Failed to load process from ${filePath}:`, error);
            throw error;
        }
    }
    async saveProcessToFile(processId, filePath) {
        try {
            const definition = this.processDefinitions.get(processId);
            if (!definition) {
                throw new Error(`Process definition not found: ${processId}`);
            }
            const fs = require('fs');
            const path = require('path');
            fs.writeFileSync(filePath, JSON.stringify(definition, null, 2));
            this.logger.info(`Saved process ${processId} to ${filePath}`);
        }
        catch (error) {
            this.logger.error(`Failed to save process ${processId} to ${filePath}:`, error);
            throw error;
        }
    }
    async deleteProcess(processId) {
        try {
            const process = this.processes.get(processId);
            if (!process) {
                return false;
            }
            // Check if process is running
            if (process.status === 'running') {
                throw new Error('Cannot delete running process');
            }
            this.processes.delete(processId);
            this.processDefinitions.delete(processId);
            this.logger.info(`Deleted process: ${processId}`);
            return true;
        }
        catch (error) {
            this.logger.error(`Failed to delete process ${processId}:`, error);
            return false;
        }
    }
    getExecutionHistory(processId) {
        return this.executor.getExecutionHistory(processId);
    }
    getActiveExecutions() {
        return this.executor.getActiveExecutions();
    }
    validateProcessDefinition(definition) {
        if (!definition.id || !definition.name) {
            throw new Error('Process definition must have id and name');
        }
        if (!definition.steps || definition.steps.length === 0) {
            throw new Error('Process must have at least one step');
        }
        // Validate step structure
        for (const step of definition.steps) {
            if (!step.id || !step.name || !step.type) {
                throw new Error('Each step must have id, name, and type');
            }
        }
    }
    handleExecutionEvent(event) {
        this.logger.debug('Execution event:', event);
        // Handle execution events
        switch (event.type) {
            case 'started':
                this.handleExecutionStarted(event);
                break;
            case 'completed':
                this.handleExecutionCompleted(event);
                break;
            case 'error':
                this.handleExecutionError(event);
                break;
            case 'cancelled':
                this.handleExecutionCancelled(event);
                break;
        }
    }
    handleExecutionStarted(event) {
        const process = this.processes.get(event.processId);
        if (process) {
            process.status = 'running';
            process.startTime = event.timestamp;
        }
    }
    handleExecutionCompleted(event) {
        const process = this.processes.get(event.processId);
        if (process) {
            process.status = 'completed';
            process.endTime = event.timestamp;
        }
    }
    handleExecutionError(event) {
        const process = this.processes.get(event.processId);
        if (process) {
            process.status = 'error';
            process.endTime = event.timestamp;
        }
    }
    handleExecutionCancelled(event) {
        const process = this.processes.get(event.processId);
        if (process) {
            process.status = 'idle';
            process.endTime = event.timestamp;
        }
    }
}
exports.RPAProcessManager = RPAProcessManager;
//# sourceMappingURL=process-manager.js.map