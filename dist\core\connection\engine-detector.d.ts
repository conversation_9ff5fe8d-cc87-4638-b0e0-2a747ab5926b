import { RPAEngine, ConnectionConfig } from '../../types';
export declare class RPAEngineDetector {
    private logger;
    detectLocalEngines(): Promise<RPAEngine[]>;
    private detectYingdaoEngine;
    validateEngine(engine: RPAEngine): Promise<boolean>;
}
export declare class RPAConnection {
    private config;
    private logger;
    private axios;
    constructor(config: ConnectionConfig);
    testConnection(): Promise<boolean>;
    getEngineInfo(): Promise<any>;
    executeRequest(endpoint: string, data: any): Promise<any>;
    private getBaseUrl;
    private delay;
}
//# sourceMappingURL=engine-detector.d.ts.map