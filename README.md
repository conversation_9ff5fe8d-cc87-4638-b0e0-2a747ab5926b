# Cursor RPA 集成使用教程

## 🚀 快速开始

### 1. 环境要求

- **Node.js**: >= 16.0.0
- **npm**: >= 8.0.0
- **操作系统**: Windows, macOS, Linux

### 2. 安装步骤

```bash
# 克隆项目
git clone <repository-url>
cd cursor-rpa-integration

# 安装依赖
npm install

# 构建项目
npm run build
```

### 3. 启动服务

#### 方式一：完整集成（推荐）
```bash
# 启动完整系统（包含引擎服务和Cursor插件）
npm run dev
```

#### 方式二：仅启动引擎服务
```bash
# 仅启动影刀RPA引擎服务
npm run dev:engine
```

#### 方式三：生产环境
```bash
# 构建并启动生产版本
npm run build
npm run start
```

### 4. 验证安装

打开浏览器访问：`http://localhost:8080/health`

应该看到类似响应：
```json
{
  "status": "healthy",
  "timestamp": "2025-07-31T00:00:00.000Z",
  "uptime": 123.456
}
```

## 🔧 配置说明

### 基础配置

编辑 `config.json` 文件：

```json
{
  "id": "cursor-rpa-integration",
  "name": "Cursor RPA Integration",
  "version": "1.0.0",
  "enabled": true,
  "settings": {
    "rpaEngine": {
      "host": "localhost",
      "port": 8080,
      "timeout": 30000,
      "retryAttempts": 3
    },
    "ai": {
      "model": "gpt-3.5-turbo",
      "temperature": 0.7,
      "maxTokens": 1000,
      "timeout": 30000
    },
    "debug": {
      "enabled": false,
      "logLevel": "info"
    }
  }
}
```

### 环境变量

创建 `.env` 文件：

```bash
# 引擎服务配置
RPA_ENGINE_HOST=localhost
RPA_ENGINE_PORT=8080
RPA_ENGINE_TIMEOUT=30000

# AI配置（可选）
OPENAI_API_KEY=your_openai_api_key
AI_MODEL=gpt-3.5-turbo

# 日志配置
LOG_LEVEL=info
DEBUG_MODE=false
```

## 📚 API 使用指南

### 基础API调用

#### 1. 检查引擎状态
```bash
curl http://localhost:8080/health
```

#### 2. 获取引擎信息
```bash
curl http://localhost:8080/info
```

#### 3. 获取所有流程
```bash
curl http://localhost:8080/processes
```

### 流程管理

#### 创建流程
```bash
curl -X POST http://localhost:8080/processes \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Web数据提取",
    "description": "从网页提取数据",
    "variables": {
      "url": "https://example.com",
      "selectors": ["title", "content"]
    }
  }'
```

#### 获取特定流程
```bash
curl http://localhost:8080/processes/{processId}
```

#### 更新流程
```bash
curl -X PUT http://localhost:8080/processes/{processId} \
  -H "Content-Type: application/json" \
  -d '{
    "name": "更新后的流程名称",
    "variables": {
      "newVar": "newValue"
    }
  }'
```

#### 删除流程
```bash
curl -X DELETE http://localhost:8080/processes/{processId}
```

### 执行控制

#### 启动流程执行
```bash
curl -X POST http://localhost:8080/processes/{processId}/execute \
  -H "Content-Type: application/json" \
  -d '{
    "parameters": {
      "inputData": "测试数据"
    },
    "debugMode": true
  }'
```

#### 获取执行状态
```bash
curl http://localhost:8080/executions/{executionId}
```

#### 停止执行
```bash
curl -X POST http://localhost:8080/executions/{executionId}/stop
```

### 变量管理

#### 获取流程变量
```bash
curl http://localhost:8080/processes/{processId}/variables
```

#### 更新流程变量
```bash
curl -X PUT http://localhost:8080/processes/{processId}/variables \
  -H "Content-Type: application/json" \
  -d '{
    "variables": {
      "updatedVar": "updatedValue"
    }
  }'
```

## 💻 代码示例

### JavaScript/Node.js

```javascript
// 基础API调用
async function basicAPIExample() {
  try {
    // 检查引擎状态
    const healthResponse = await fetch('http://localhost:8080/health');
    const healthData = await healthResponse.json();
    console.log('引擎状态:', healthData);

    // 创建流程
    const createResponse = await fetch('http://localhost:8080/processes', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        name: '测试流程',
        description: '一个简单的测试流程',
        variables: { input: '测试数据' }
      })
    });
    
    const process = await createResponse.json();
    console.log('创建的流程:', process);

    // 执行流程
    const executeResponse = await fetch(`http://localhost:8080/processes/${process.id}/execute`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        parameters: { data: '执行参数' },
        debugMode: true
      })
    });

    const execution = await executeResponse.json();
    console.log('执行ID:', execution.executionId);

  } catch (error) {
    console.error('API调用失败:', error);
  }
}

// WebSocket 实时监控
function websocketExample() {
  const ws = new WebSocket('ws://localhost:8080');
  
  ws.onopen = () => {
    console.log('WebSocket连接已建立');
    // 订阅执行事件
    ws.send(JSON.stringify({ type: 'subscribe' }));
  };

  ws.onmessage = (event) => {
    const data = JSON.parse(event.data);
    console.log('收到消息:', data);
    
    if (data.type === 'execution_progress') {
      console.log(`执行进度: ${data.progress.percentage}%`);
    }
  };

  ws.onclose = () => {
    console.log('WebSocket连接已关闭');
  };
}
```

### Python 示例

```python
import requests
import json
import time

# 基础配置
BASE_URL = "http://localhost:8080"

def basic_api_example():
    # 检查引擎状态
    response = requests.get(f"{BASE_URL}/health")
    print("引擎状态:", response.json())
    
    # 创建流程
    process_data = {
        "name": "Python测试流程",
        "description": "使用Python创建的测试流程",
        "variables": {"python_var": "python_value"}
    }
    
    response = requests.post(f"{BASE_URL}/processes", json=process_data)
    process = response.json()
    print("创建的流程:", process)
    
    # 执行流程
    execution_data = {
        "parameters": {"input": "Python参数"},
        "debugMode": True
    }
    
    response = requests.post(f"{BASE_URL}/processes/{process['id']}/execute", json=execution_data)
    execution = response.json()
    print("执行信息:", execution)
    
    # 监控执行状态
    execution_id = execution['executionId']
    for i in range(10):
        response = requests.get(f"{BASE_URL}/executions/{execution_id}")
        status = response.json()
        print(f"执行状态: {status['status']}, 进度: {status.get('progress', {}).get('percentage', 0)}%")
        
        if status['status'] in ['completed', 'error', 'stopped']:
            break
        time.sleep(2)

if __name__ == "__main__":
    basic_api_example()
```

### curl 示例脚本

```bash
#!/bin/bash

# 设置基础URL
BASE_URL="http://localhost:8080"

echo "=== 影刀RPA引擎API测试脚本 ==="

# 1. 健康检查
echo "1. 检查引擎健康状态..."
curl -s "$BASE_URL/health" | jq .

# 2. 获取引擎信息
echo -e "\n2. 获取引擎信息..."
curl -s "$BASE_URL/info" | jq .

# 3. 创建流程
echo -e "\n3. 创建新流程..."
PROCESS_RESPONSE=$(curl -s -X POST "$BASE_URL/processes" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Shell脚本测试流程",
    "description": "通过Shell脚本创建的测试流程",
    "variables": {
      "shell_var": "shell_value",
      "counter": 42
    }
  }')

echo "$PROCESS_RESPONSE" | jq .
PROCESS_ID=$(echo "$PROCESS_RESPONSE" | jq -r '.id')

# 4. 执行流程
echo -e "\n4. 执行流程..."
EXECUTION_RESPONSE=$(curl -s -X POST "$BASE_URL/processes/$PROCESS_ID/execute" \
  -H "Content-Type: application/json" \
  -d '{
    "parameters": {
      "input_data": "来自Shell脚本的测试数据"
    },
    "debugMode": true
  }')

echo "$EXECUTION_RESPONSE" | jq .
EXECUTION_ID=$(echo "$EXECUTION_RESPONSE" | jq -r '.executionId')

# 5. 监控执行状态
echo -e "\n5. 监控执行状态..."
for i in {1..10}; do
  STATUS_RESPONSE=$(curl -s "$BASE_URL/executions/$EXECUTION_ID")
  STATUS=$(echo "$STATUS_RESPONSE" | jq -r '.status')
  PROGRESS=$(echo "$STATUS_RESPONSE" | jq -r '.progress.percentage // 0')
  
  echo "执行状态: $STATUS, 进度: $PROGRESS%"
  
  if [[ "$STATUS" == "completed" || "$STATUS" == "error" || "$STATUS" == "stopped" ]]; then
    break
  fi
  
  sleep 2
done

# 6. 获取流程变量
echo -e "\n6. 获取流程变量..."
curl -s "$BASE_URL/processes/$PROCESS_ID/variables" | jq .

# 7. 更新流程变量
echo -e "\n7. 更新流程变量..."
curl -s -X PUT "$BASE_URL/processes/$PROCESS_ID/variables" \
  -H "Content-Type: application/json" \
  -d '{
    "variables": {
      "updated_var": "updated_by_shell",
      "timestamp": "'$(date -Iseconds)'"
    }
  }' | jq .

# 8. 删除流程
echo -e "\n8. 删除流程..."
curl -s -X DELETE "$BASE_URL/processes/$PROCESS_ID"
echo "流程已删除"

echo -e "\n=== 测试完成 ==="
```

## 🎯 实际应用场景

### 场景1：网页数据提取

```javascript
// 创建网页数据提取流程
const webScrapingProcess = {
  name: "电商价格监控",
  description: "监控电商网站商品价格变化",
  variables: {
    targetUrl: "https://example.com/product",
    priceSelector: ".price",
    nameSelector: ".product-name",
    outputFormat: "json"
  }
};

// 执行并获取结果
const result = await executeProcess(processId, {
  parameters: {
    action: "scrape",
    saveToDatabase: true
  }
});
```

### 场景2：文件批量处理

```javascript
// 文件处理流程
const fileProcessingProcess = {
  name: "发票处理",
  description: "批量处理发票文件",
  variables: {
    inputFolder: "./invoices",
    outputFolder: "./processed",
    fileTypes: ["pdf", "jpg", "png"],
    ocrEnabled: true
  }
};
```

### 场景3：API数据同步

```javascript
// API数据同步流程
const apiSyncProcess = {
  name: "CRM数据同步",
  description: "同步CRM系统数据",
  variables: {
    sourceApi: "https://api.crm.com/v1/contacts",
    targetApi: "https://internal-api.company.com/contacts",
    syncInterval: 3600,
    apiKey: "your_api_key"
  }
};
```

## 🔍 故障排除

### 常见问题

#### 1. 端口占用
```bash
# 检查端口占用
netstat -an | grep 8080

# 更改端口（在config.json中）
{
  "settings": {
    "rpaEngine": {
      "port": 8081
    }
  }
}
```

#### 2. 连接失败
```bash
# 检查引擎状态
curl http://localhost:8080/health

# 查看日志
tail -f logs/combined.log
```

#### 3. 权限问题
```bash
# 检查文件权限
ls -la logs/ data/

# 设置权限
chmod 755 logs/ data/
```

### 调试模式

启用调试模式：
```json
{
  "settings": {
    "debug": {
      "enabled": true,
      "logLevel": "debug"
    }
  }
}
```

查看详细日志：
```bash
tail -f logs/combined.log | grep DEBUG
```

## 📚 下一步

- 阅读 [API参考文档](./API_REFERENCE.md)
- 查看 [最佳实践指南](./BEST_PRACTICES.md)
- 探索 [高级功能](./ADVANCED_FEATURES.md)
- 加入 [社区讨论](./COMMUNITY.md)

---

## 🤝 技术支持

如果您在使用过程中遇到问题：

1. 查看 [故障排除指南](./TROUBLESHOOTING.md)
2. 检查 [已知问题](./KNOWN_ISSUES.md)
3. 提交 [Issue](https://github.com/your-repo/issues)
4. 联系技术支持：<EMAIL>

---

祝您使用愉快！🎉