# CAD标准制定

## 6.1.1 图层标准

**图层标准化的重要性：**
图层标准是CAD标准化的核心，它确保了图纸的一致性、可读性和可维护性。良好的图层标准可以显著提高团队协作效率和图纸质量。

**完整图层标准实现：**
```csharp
using Autodesk.AutoCAD.Runtime;
using Autodesk.AutoCAD.ApplicationServices;
using Autodesk.AutoCAD.DatabaseServices;
using Autodesk.AutoCAD.Geometry;
using Autodesk.AutoCAD.EditorInput;
using System;
using System.Collections.Generic;
using System.IO;
using System.Xml.Serialization;

namespace AutoCAD.Standards
{
    public class LayerStandards
    {
        // 预定义的图层标准
        private static readonly List<LayerStandard> StandardLayers = new List<LayerStandard>
        {
            new LayerStandard
            {
                Name = "0",
                Color = 7,
                LineWeight = LineWeight.LineWeight025,
                LineType = "Continuous",
                Description = "默认图层",
                Plot = true,
                CanDelete = false
            },
            new LayerStandard
            {
                Name = "DEFPOINTS",
                Color = 7,
                LineWeight = LineWeight.LineWeight000,
                LineType = "Continuous",
                Description = "定义点图层",
                Plot = false,
                CanDelete = false
            },
            // 建筑图层
            new LayerStandard
            {
                Name = "A-WALL",
                Color = 1,
                LineWeight = LineWeight.LineWeight050,
                LineType = "Continuous",
                Description = "建筑墙体",
                Plot = true,
                CanDelete = true
            },
            new LayerStandard
            {
                Name = "A-DOOR",
                Color = 2,
                LineWeight = LineWeight.LineWeight025,
                LineType = "Continuous",
                Description = "建筑门",
                Plot = true,
                CanDelete = true
            },
            new LayerStandard
            {
                Name = "A-WINDOW",
                Color = 3,
                LineWeight = LineWeight.LineWeight025,
                LineType = "Continuous",
                Description = "建筑窗",
                Plot = true,
                CanDelete = true
            },
            new LayerStandard
            {
                Name = "A-FLOR",
                Color = 4,
                LineWeight = LineWeight.LineWeight025,
                LineType = "Continuous",
                Description = "建筑地板",
                Plot = true,
                CanDelete = true
            },
            new LayerStandard
            {
                Name = "A-ROOF",
                Color = 5,
                LineWeight = LineWeight.LineWeight035,
                LineType = "Continuous",
                Description = "建筑屋顶",
                Plot = true,
                CanDelete = true
            },
            // 结构图层
            new LayerStandard
            {
                Name = "S-COLU",
                Color = 6,
                LineWeight = LineWeight.LineWeight050,
                LineType = "Continuous",
                Description = "结构柱",
                Plot = true,
                CanDelete = true
            },
            new LayerStandard
            {
                Name = "S-BEAM",
                Color = 6,
                LineWeight = LineWeight.LineWeight040,
                LineType = "Continuous",
                Description = "结构梁",
                Plot = true,
                CanDelete = true
            },
            new LayerStandard
            {
                Name = "S-SLAB",
                Color = 6,
                LineWeight = LineWeight.LineWeight035,
                LineType = "Continuous",
                Description = "结构板",
                Plot = true,
                CanDelete = true
            },
            // 机械图层
            new LayerStandard
            {
                Name = "M-EQPM",
                Color = 8,
                LineWeight = LineWeight.LineWeight025,
                LineType = "Continuous",
                Description = "机械设备",
                Plot = true,
                CanDelete = true
            },
            new LayerStandard
            {
                Name = "M-PIPE",
                Color = 9,
                LineWeight = LineWeight.LineWeight025,
                LineType = "Continuous",
                Description = "管道",
                Plot = true,
                CanDelete = true
            },
            // 电气图层
            new LayerStandard
            {
                Name = "E-LITE",
                Color = 11,
                LineWeight = LineWeight.LineWeight025,
                LineType = "Continuous",
                Description = "照明",
                Plot = true,
                CanDelete = true
            },
            new LayerStandard
            {
                Name = "E-POWER",
                Color = 12,
                LineWeight = LineWeight.LineWeight025,
                LineType = "Continuous",
                Description = "电力",
                Plot = true,
                CanDelete = true
            },
            // 文字和标注
            new LayerStandard
            {
                Name = "A-ANNO-TEXT",
                Color = 7,
                LineWeight = LineWeight.LineWeight025,
                LineType = "Continuous",
                Description = "文字标注",
                Plot = true,
                CanDelete = true
            },
            new LayerStandard
            {
                Name = "A-ANNO-DIMS",
                Color = 7,
                LineWeight = LineWeight.LineWeight025,
                LineType = "Continuous",
                Description = "尺寸标注",
                Plot = true,
                CanDelete = true
            },
            new LayerStandard
            {
                Name = "A-ANNO-SYMB",
                Color = 7,
                LineWeight = LineWeight.LineWeight025,
                LineType = "Continuous",
                Description = "符号标注",
                Plot = true,
                CanDelete = true
            },
            // 图框和标题栏
            new LayerStandard
            {
                Name = "A-ANNO-TTLB",
                Color = 7,
                LineWeight = LineWeight.LineWeight050,
                LineType = "Continuous",
                Description = "标题栏",
                Plot = true,
                CanDelete = true
            },
            new LayerStandard
            {
                Name = "A-ANNO-VIEW",
                Color = 7,
                LineWeight = LineWeight.LineWeight025,
                LineType = "Continuous",
                Description = "视口",
                Plot = false,
                CanDelete = true
            }
        };

        [CommandMethod("CREATESTANDARDLAYERS")]
        public void CreateStandardLayers()
        {
            Document doc = Application.DocumentManager.MdiActiveDocument;
            Database db = doc.Database;
            Editor ed = doc.Editor;

            try
            {
                int createdCount = 0;
                int updatedCount = 0;

                using (Transaction trans = db.TransactionManager.StartTransaction())
                {
                    LayerTable lt = trans.GetObject(db.LayerTableId, OpenMode.ForWrite) as LayerTable;

                    foreach (var standard in StandardLayers)
                    {
                        if (!lt.Has(standard.Name))
                        {
                            // 创建新图层
                            LayerTableRecord layer = new LayerTableRecord();
                            layer.Name = standard.Name;
                            layer.Color = Color.FromColorIndex(ColorMethod.ByColor, standard.Color);
                            layer.LineWeight = standard.LineWeight;
                            
                            // 设置线型
                            if (!string.IsNullOrEmpty(standard.LineType))
                            {
                                // 加载线型
                                if (!lt.Has(standard.LineType))
                                {
                                    db.LoadLineTypeFile(standard.LineType, "acad.lin");
                                }
                                layer.LinetypeObjectId = db.LinetypeTableId;
                            }
                            
                            layer.IsPlottable = standard.Plot;
                            layer.Description = standard.Description;
                            
                            lt.Add(layer);
                            trans.AddNewlyCreatedDBObject(layer, true);
                            createdCount++;
                        }
                        else
                        {
                            // 更新现有图层
                            LayerTableRecord existingLayer = trans.GetObject(lt[standard.Name], OpenMode.ForWrite) as LayerTableRecord;
                            
                            // 检查是否需要更新
                            bool needsUpdate = false;
                            
                            if (existingLayer.Color.ColorIndex != standard.Color)
                            {
                                existingLayer.Color = Color.FromColorIndex(ColorMethod.ByColor, standard.Color);
                                needsUpdate = true;
                            }
                            
                            if (existingLayer.LineWeight != standard.LineWeight)
                            {
                                existingLayer.LineWeight = standard.LineWeight;
                                needsUpdate = true;
                            }
                            
                            if (existingLayer.IsPlottable != standard.Plot)
                            {
                                existingLayer.IsPlottable = standard.Plot;
                                needsUpdate = true;
                            }
                            
                            if (needsUpdate)
                            {
                                updatedCount++;
                            }
                        }
                    }

                    trans.Commit();
                }

                ed.WriteMessage($"\n图层标准化完成！");
                ed.WriteMessage($"\n创建新图层：{createdCount}个");
                ed.WriteMessage($"\n更新现有图层：{updatedCount}个");
            }
            catch (System.Exception ex)
            {
                ed.WriteMessage($"\n创建标准图层时出错：{ex.Message}");
            }
        }

        [CommandMethod("VALIDATELAYERS")]
        public void ValidateLayers()
        {
            Document doc = Application.DocumentManager.MdiActiveDocument;
            Database db = doc.Database;
            Editor ed = doc.Editor;

            try
            {
                var validationResult = ValidateLayerStandards(db);
                
                ed.WriteMessage("\n=== 图层标准验证报告 ===");
                ed.WriteMessage($"\n总图层数：{validationResult.TotalLayers}");
                ed.WriteMessage($"\n标准图层数：{validationResult.StandardLayers}");
                ed.WriteMessage($"\n非标准图层数：{validationResult.NonStandardLayers}");
                ed.WriteMessage($"\n错误图层数：{validationResult.ErrorLayers}");
                
                if (validationResult.NonStandardLayers > 0)
                {
                    ed.WriteMessage("\n--- 非标准图层列表 ---");
                    foreach (var layer in validationResult.NonStandardLayerList)
                    {
                        ed.WriteMessage($"\n{layer}");
                    }
                }
                
                if (validationResult.ErrorLayers > 0)
                {
                    ed.WriteMessage("\n--- 错误图层列表 ---");
                    foreach (var error in validationResult.ErrorLayerList)
                    {
                        ed.WriteMessage($"\n{error.LayerName}: {error.ErrorMessage}");
                    }
                }
                
                // 提供修复建议
                if (validationResult.HasIssues)
                {
                    ed.WriteMessage("\n--- 修复建议 ---");
                    ed.WriteMessage("\n1. 使用 FIXLAYERSTANDARD 命令自动修复");
                    ed.WriteMessage("\n2. 手动重命名非标准图层");
                    ed.WriteMessage("\n3. 删除未使用的错误图层");
                }
                else
                {
                    ed.WriteMessage("\n✅ 所有图层都符合标准！");
                }
            }
            catch (System.Exception ex)
            {
                ed.WriteMessage($"\n验证图层标准时出错：{ex.Message}");
            }
        }

        private LayerValidationResult ValidateLayerStandards(Database db)
        {
            var result = new LayerValidationResult();
            
            using (Transaction trans = db.TransactionManager.StartTransaction())
            {
                LayerTable lt = trans.GetObject(db.LayerTableId, OpenMode.ForRead) as LayerTable;
                
                result.TotalLayers = 0;
                result.StandardLayers = 0;
                result.NonStandardLayers = 0;
                result.ErrorLayers = 0;
                result.NonStandardLayerList = new List<string>();
                result.ErrorLayerList = new List<LayerError>();
                
                foreach (ObjectId layerId in lt)
                {
                    result.TotalLayers++;
                    
                    LayerTableRecord layer = trans.GetObject(layerId, OpenMode.ForRead) as LayerTableRecord;
                    
                    // 检查是否为标准图层
                    var standardLayer = StandardLayers.Find(s => s.Name == layer.Name);
                    
                    if (standardLayer != null)
                    {
                        result.StandardLayers++;
                        
                        // 检查图层属性是否符合标准
                        var layerErrors = ValidateLayerProperties(layer, standardLayer);
                        if (layerErrors.Count > 0)
                        {
                            result.ErrorLayers++;
                            result.ErrorLayerList.AddRange(layerErrors);
                        }
                    }
                    else
                    {
                        // 非标准图层
                        result.NonStandardLayers++;
                        result.NonStandardLayerList.Add(layer.Name);
                        
                        // 检查是否有明显的错误
                        var errors = CheckLayerErrors(layer);
                        if (errors.Count > 0)
                        {
                            result.ErrorLayers++;
                            result.ErrorLayerList.AddRange(errors);
                        }
                    }
                }
                
                trans.Commit();
            }
            
            result.HasIssues = result.NonStandardLayers > 0 || result.ErrorLayers > 0;
            return result;
        }

        private List<LayerError> ValidateLayerProperties(LayerTableRecord layer, LayerStandard standard)
        {
            var errors = new List<LayerError>();
            
            if (layer.Color.ColorIndex != standard.Color)
            {
                errors.Add(new LayerError
                {
                    LayerName = layer.Name,
                    ErrorMessage = $"颜色不正确：应为{standard.Color}，当前为{layer.Color.ColorIndex}"
                });
            }
            
            if (layer.LineWeight != standard.LineWeight)
            {
                errors.Add(new LayerError
                {
                    LayerName = layer.Name,
                    ErrorMessage = $"线宽不正确：应为{standard.LineWeight}，当前为{layer.LineWeight}"
                });
            }
            
            if (layer.IsPlottable != standard.Plot)
            {
                errors.Add(new LayerError
                {
                    LayerName = layer.Name,
                    ErrorMessage = $"打印属性不正确：应为{standard.Plot}，当前为{layer.IsPlottable}"
                });
            }
            
            return errors;
        }

        private List<LayerError> CheckLayerErrors(LayerTableRecord layer)
        {
            var errors = new List<LayerError>();
            
            // 检查图层名称是否符合命名规范
            if (!IsValidLayerName(layer.Name))
            {
                errors.Add(new LayerError
                {
                    LayerName = layer.Name,
                    ErrorMessage = "图层名称不符合命名规范"
                });
            }
            
            // 检查颜色是否有效
            if (layer.Color.ColorIndex < 1 || layer.Color.ColorIndex > 255)
            {
                errors.Add(new LayerError
                {
                    LayerName = layer.Name,
                    ErrorMessage = "图层颜色无效"
                });
            }
            
            return errors;
        }

        private bool IsValidLayerName(string layerName)
        {
            // 检查图层名称长度
            if (string.IsNullOrEmpty(layerName) || layerName.Length > 255)
            {
                return false;
            }
            
            // 检查是否包含非法字符
            char[] invalidChars = { '<', '>', '/', '\\', ':', '*', '?', '"', '|', '=', '`' };
            if (layerName.IndexOfAny(invalidChars) >= 0)
            {
                return false;
            }
            
            // 检查是否以空格开头或结尾
            if (layerName.Trim() != layerName)
            {
                return false;
            }
            
            return true;
        }

        [CommandMethod("FIXLAYERSTANDARD")]
        public void FixLayerStandard()
        {
            Document doc = Application.DocumentManager.MdiActiveDocument;
            Database db = doc.Database;
            Editor ed = doc.Editor;

            try
            {
                // 首先验证图层
                var validationResult = ValidateLayerStandards(db);
                
                if (!validationResult.HasIssues)
                {
                    ed.WriteMessage("\n所有图层都符合标准，无需修复！");
                    return;
                }
                
                // 询问用户是否要继续修复
                PromptKeywordOptions continueOpt = new PromptKeywordOptions("\n发现图层问题，是否继续修复？");
                continueOpt.Keywords.Add("Yes");
                continueOpt.Keywords.Add("No");
                continueOpt.Keywords.Default = "Yes";
                
                PromptResult continueResult = ed.GetKeywords(continueOpt);
                if (continueResult.Status != PromptStatus.OK || continueResult.StringResult == "No")
                {
                    return;
                }
                
                // 修复图层
                int fixedCount = FixLayerIssues(db, validationResult);
                
                ed.WriteMessage($"\n图层修复完成！共修复{fixedCount}个问题。");
            }
            catch (System.Exception ex)
            {
                ed.WriteMessage($"\n修复图层标准时出错：{ex.Message}");
            }
        }

        private int FixLayerIssues(Database db, LayerValidationResult validationResult)
        {
            int fixedCount = 0;
            
            using (Transaction trans = db.TransactionManager.StartTransaction())
            {
                LayerTable lt = trans.GetObject(db.LayerTableId, OpenMode.ForWrite) as LayerTable;
                
                foreach (ObjectId layerId in lt)
                {
                    LayerTableRecord layer = trans.GetObject(layerId, OpenMode.ForWrite) as LayerTableRecord;
                    
                    // 检查是否为标准图层
                    var standardLayer = StandardLayers.Find(s => s.Name == layer.Name);
                    
                    if (standardLayer != null)
                    {
                        // 修复标准图层
                        if (FixLayerProperties(layer, standardLayer))
                        {
                            fixedCount++;
                        }
                    }
                }
                
                trans.Commit();
            }
            
            return fixedCount;
        }

        private bool FixLayerProperties(LayerTableRecord layer, LayerStandard standard)
        {
            bool fixed = false;
            
            if (layer.Color.ColorIndex != standard.Color)
            {
                layer.Color = Color.FromColorIndex(ColorMethod.ByColor, standard.Color);
                fixed = true;
            }
            
            if (layer.LineWeight != standard.LineWeight)
            {
                layer.LineWeight = standard.LineWeight;
                fixed = true;
            }
            
            if (layer.IsPlottable != standard.Plot)
            {
                layer.IsPlottable = standard.Plot;
                fixed = true;
            }
            
            return fixed;
        }

        [CommandMethod("EXPORTLAYERSTANDARD")]
        public void ExportLayerStandard()
        {
            Document doc = Application.DocumentManager.MdiActiveDocument;
            Editor ed = doc.Editor;

            try
            {
                // 选择保存路径
                string filePath = GetSaveFilePath("图层标准文件", "*.xml", "LayerStandards.xml");
                if (string.IsNullOrEmpty(filePath))
                    return;
                
                // 导出标准
                ExportLayerStandards(filePath);
                
                ed.WriteMessage($"\n图层标准已导出到：{filePath}");
            }
            catch (System.Exception ex)
            {
                ed.WriteMessage($"\n导出图层标准时出错：{ex.Message}");
            }
        }

        [CommandMethod("IMPORTLAYERSTANDARD")]
        public void ImportLayerStandard()
        {
            Document doc = Application.DocumentManager.MdiActiveDocument;
            Editor ed = doc.Editor;

            try
            {
                // 选择文件路径
                string filePath = GetOpenFilePath("图层标准文件", "*.xml");
                if (string.IsNullOrEmpty(filePath))
                    return;
                
                // 导入标准
                ImportLayerStandards(filePath);
                
                ed.WriteMessage($"\n图层标准已从{filePath}导入");
            }
            catch (System.Exception ex)
            {
                ed.WriteMessage($"\n导入图层标准时出错：{ex.Message}");
            }
        }

        private void ExportLayerStandards(string filePath)
        {
            var serializer = new XmlSerializer(typeof(List<LayerStandard>));
            
            using (var writer = new StreamWriter(filePath))
            {
                serializer.Serialize(writer, StandardLayers);
            }
        }

        private void ImportLayerStandards(string filePath)
        {
            var serializer = new XmlSerializer(typeof(List<LayerStandard>));
            
            using (var reader = new StreamReader(filePath))
            {
                var importedStandards = (List<LayerStandard>)serializer.Deserialize(reader);
                
                // 更新标准列表
                StandardLayers.Clear();
                StandardLayers.AddRange(importedStandards);
            }
        }

        private string GetSaveFilePath(string title, string filter, string defaultFileName)
        {
            using (var dialog = new System.Windows.Forms.SaveFileDialog())
            {
                dialog.Title = title;
                dialog.Filter = filter;
                dialog.FileName = defaultFileName;
                
                if (dialog.ShowDialog() == System.Windows.Forms.DialogResult.OK)
                {
                    return dialog.FileName;
                }
            }
            
            return null;
        }

        private string GetOpenFilePath(string title, string filter)
        {
            using (var dialog = new System.Windows.Forms.OpenFileDialog())
            {
                dialog.Title = title;
                dialog.Filter = filter;
                
                if (dialog.ShowDialog() == System.Windows.Forms.DialogResult.OK)
                {
                    return dialog.FileName;
                }
            }
            
            return null;
        }

        [CommandMethod("LAYERSTANDARDREPORT")]
        public void LayerStandardReport()
        {
            Document doc = Application.DocumentManager.MdiActiveDocument;
            Database db = doc.Database;
            Editor ed = doc.Editor;

            try
            {
                // 生成详细报告
                var report = GenerateLayerStandardReport(db);
                
                // 显示报告
                ed.WriteMessage("\n=== 图层标准详细报告 ===");
                ed.WriteMessage($"\n生成时间：{DateTime.Now:yyyy-MM-dd HH:mm:ss}");
                ed.WriteMessage($"\n文档名称：{doc.Name}");
                ed.WriteMessage($"\n数据库版本：{db.Version}");
                
                ed.WriteMessage("\n--- 标准图层统计 ---");
                ed.WriteMessage($"\n总标准图层数：{StandardLayers.Count}");
                ed.WriteMessage($"\n建筑图层数：{StandardLayers.Count(l => l.Name.StartsWith("A-"))}");
                ed.WriteMessage($"\n结构图层数：{StandardLayers.Count(l => l.Name.StartsWith("S-"))}");
                ed.WriteMessage($"\n机械图层数：{StandardLayers.Count(l => l.Name.StartsWith("M-"))}");
                ed.WriteMessage($"\n电气图层数：{StandardLayers.Count(l => l.Name.StartsWith("E-"))}");
                ed.WriteMessage($"\n标注图层数：{StandardLayers.Count(l => l.Name.Contains("ANNO"))}");
                
                ed.WriteMessage("\n--- 当前文档图层状态 ---");
                var validationResult = ValidateLayerStandards(db);
                ed.WriteMessage($"\n符合标准的图层数：{validationResult.StandardLayers}");
                ed.WriteMessage($"\n需要修复的图层数：{validationResult.ErrorLayers}");
                ed.WriteMessage($"\n非标准图层数：{validationResult.NonStandardLayers}");
                
                // 保存报告到文件
                string reportPath = Path.Combine(
                    Environment.GetFolderPath(Environment.SpecialFolder.Desktop),
                    $"LayerStandardReport_{DateTime.Now:yyyyMMdd_HHmmss}.txt");
                
                File.WriteAllText(reportPath, report);
                
                ed.WriteMessage($"\n详细报告已保存到：{reportPath}");
            }
            catch (System.Exception ex)
            {
                ed.WriteMessage($"\n生成图层标准报告时出错：{ex.Message}");
            }
        }

        private string GenerateLayerStandardReport(Database db)
        {
            var report = new System.Text.StringBuilder();
            
            report.AppendLine("=== 图层标准详细报告 ===");
            report.AppendLine($"生成时间：{DateTime.Now:yyyy-MM-dd HH:mm:ss}");
            report.AppendLine($"文档名称：{Application.DocumentManager.MdiActiveDocument.Name}");
            report.AppendLine($"数据库版本：{db.Version}");
            report.AppendLine();
            
            report.AppendLine("--- 标准图层列表 ---");
            foreach (var standard in StandardLayers)
            {
                report.AppendLine($"图层名称：{standard.Name}");
                report.AppendLine($"  颜色：{standard.Color}");
                report.AppendLine($"  线宽：{standard.LineWeight}");
                report.AppendLine($"  线型：{standard.LineType}");
                report.AppendLine($"  描述：{standard.Description}");
                report.AppendLine($"  可打印：{standard.Plot}");
                report.AppendLine();
            }
            
            report.AppendLine("--- 当前文档图层状态 ---");
            var validationResult = ValidateLayerStandards(db);
            report.AppendLine($"符合标准的图层数：{validationResult.StandardLayers}");
            report.AppendLine($"需要修复的图层数：{validationResult.ErrorLayers}");
            report.AppendLine($"非标准图层数：{validationResult.NonStandardLayers}");
            
            if (validationResult.NonStandardLayerList.Count > 0)
            {
                report.AppendLine("\n非标准图层：");
                foreach (var layer in validationResult.NonStandardLayerList)
                {
                    report.AppendLine($"  - {layer}");
                }
            }
            
            if (validationResult.ErrorLayerList.Count > 0)
            {
                report.AppendLine("\n错误图层：");
                foreach (var error in validationResult.ErrorLayerList)
                {
                    report.AppendLine($"  - {error.LayerName}: {error.ErrorMessage}");
                }
            }
            
            return report.ToString();
        }
    }

    // 数据结构
    public class LayerStandard
    {
        public string Name { get; set; }
        public short Color { get; set; }
        public LineWeight LineWeight { get; set; }
        public string LineType { get; set; }
        public string Description { get; set; }
        public bool Plot { get; set; }
        public bool CanDelete { get; set; }
    }

    public class LayerValidationResult
    {
        public int TotalLayers { get; set; }
        public int StandardLayers { get; set; }
        public int NonStandardLayers { get; set; }
        public int ErrorLayers { get; set; }
        public bool HasIssues { get; set; }
        public List<string> NonStandardLayerList { get; set; }
        public List<LayerError> ErrorLayerList { get; set; }
    }

    public class LayerError
    {
        public string LayerName { get; set; }
        public string ErrorMessage { get; set; }
    }
}
```

---

**CURSOR提示词模板：**
```
请为CAD图层标准化提供完整的解决方案，包括：
1. 标准图层定义和管理
2. 图层验证和修复功能
3. 图层标准导入导出
4. 详细报告生成
5. 符合行业标准的命名规范
```