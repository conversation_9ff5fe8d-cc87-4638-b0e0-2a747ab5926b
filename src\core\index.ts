import { RPAConnectionManager } from './connection';
import { RPAProcessManager } from './execution';
import { createLogger } from '../utils/logger';

export class RPAEngine {
  private connectionManager: RPAConnectionManager;
  private processManager: RPAProcessManager;
  private logger = createLogger('RPAEngine');
  private initialized = false;

  constructor() {
    this.connectionManager = new RPAConnectionManager();
    this.processManager = new RPAProcessManager(this.connectionManager);
  }

  async initialize(): Promise<void> {
    if (this.initialized) {
      return;
    }

    try {
      this.logger.info('Initializing RPA Engine...');
      
      await this.connectionManager.initialize();
      
      this.initialized = true;
      this.logger.info('RPA Engine initialized successfully');
      
    } catch (error) {
      this.logger.error('Failed to initialize RPA Engine:', error);
      throw error;
    }
  }

  async shutdown(): Promise<void> {
    try {
      this.logger.info('Shutting down RPA Engine...');
      
      this.connectionManager.dispose();
      
      this.initialized = false;
      this.logger.info('RPA Engine shutdown completed');
      
    } catch (error) {
      this.logger.error('Failed to shutdown RPA Engine:', error);
      throw error;
    }
  }

  getConnectionManager(): RPAConnectionManager {
    return this.connectionManager;
  }

  getProcessManager(): RPAProcessManager {
    return this.processManager;
  }

  isInitialized(): boolean {
    return this.initialized;
  }
}

export const rpaEngine = new RPAEngine();