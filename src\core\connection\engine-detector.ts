import { RPAEngine, ConnectionConfig } from '../../types';
import { validateConnectionConfig, validateRPAEngine } from '../../utils';
import { createLogger } from '../../utils/logger';

export class RPAEngineDetector {
  private logger = createLogger('RPAEngineDetector');

  async detectLocalEngines(): Promise<RPAEngine[]> {
    this.logger.info('Detecting local RPA engines...');
    
    const engines: RPAEngine[] = [];
    
    try {
      const yingdaoEngine = await this.detectYingdaoEngine();
      if (yingdaoEngine) {
        engines.push(yingdaoEngine);
      }
    } catch (error) {
      this.logger.error('Error detecting Yingdao engine:', error);
    }
    
    return engines;
  }

  private async detectYingdaoEngine(): Promise<RPAEngine | null> {
    const defaultPorts = [8080, 8081, 9090, 9091];
    
    for (const port of defaultPorts) {
      try {
        const config: ConnectionConfig = {
          host: 'localhost',
          port,
          timeout: 5000,
          retryAttempts: 1
        };
        
        const connection = new RPAConnection(config);
        const isConnected = await connection.testConnection();
        
        if (isConnected) {
          const engineInfo = await connection.getEngineInfo();
          return {
            id: `yingdao-${port}`,
            name: 'Yingdao RPA Engine',
            version: engineInfo.version || 'unknown',
            status: 'running',
            endpoint: `http://localhost:${port}`,
            capabilities: engineInfo.capabilities || []
          };
        }
      } catch (error) {
        this.logger.debug(`Failed to connect to port ${port}:`, error);
      }
    }
    
    return null;
  }

  async validateEngine(engine: RPAEngine): Promise<boolean> {
    try {
      validateRPAEngine(engine);
      
      const config: ConnectionConfig = {
        host: new URL(engine.endpoint).hostname,
        port: parseInt(new URL(engine.endpoint).port) || 80,
        timeout: 10000,
        retryAttempts: 2
      };
      
      const connection = new RPAConnection(config);
      return await connection.testConnection();
    } catch (error) {
      this.logger.error('Engine validation failed:', error);
      return false;
    }
  }
}

export class RPAConnection {
  private config: ConnectionConfig;
  private logger = createLogger('RPAConnection');
  private axios: any;

  constructor(config: ConnectionConfig) {
    this.config = validateConnectionConfig(config);
    this.axios = require('axios').default;
  }

  async testConnection(): Promise<boolean> {
    try {
      const response = await this.axios.get(`${this.getBaseUrl()}/health`, {
        timeout: this.config.timeout
      });
      
      return response.status === 200;
    } catch (error) {
      this.logger.error('Connection test failed:', error);
      return false;
    }
  }

  async getEngineInfo(): Promise<any> {
    try {
      const response = await this.axios.get(`${this.getBaseUrl()}/info`, {
        timeout: this.config.timeout
      });
      
      return response.data;
    } catch (error) {
      this.logger.error('Failed to get engine info:', error);
      throw new Error('Unable to get engine information');
    }
  }

  async executeRequest(endpoint: string, data: any): Promise<any> {
    let lastError: any;
    
    for (let attempt = 0; attempt < this.config.retryAttempts; attempt++) {
      try {
        const response = await this.axios.post(
          `${this.getBaseUrl()}/${endpoint}`,
          data,
          {
            timeout: this.config.timeout,
            headers: {
              'Content-Type': 'application/json',
              ...(this.config.apiKey && { 'Authorization': `Bearer ${this.config.apiKey}` })
            }
          }
        );
        
        return response.data;
      } catch (error: any) {
        lastError = error;
        this.logger.warn(`Request attempt ${attempt + 1} failed:`, error.message);
        
        if (attempt < this.config.retryAttempts - 1) {
          await this.delay(1000 * (attempt + 1));
        }
      }
    }
    
    throw lastError;
  }

  private getBaseUrl(): string {
    const auth = this.config.username && this.config.password 
      ? `${this.config.username}:${this.config.password}@`
      : '';
    
    return `http://${auth}${this.config.host}:${this.config.port}`;
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}