import { pluginActivator } from './plugin/activation';
import { rpaEngine } from './core';
import { engineServiceManager } from './engine';
import { createLogger } from './utils/logger';

const logger = createLogger('main');

async function main() {
  try {
    logger.info('Starting Cursor RPA Integration...');
    
    // Start Yingdao RPA Engine service first
    logger.info('Starting Yingdao RPA Engine service...');
    await engineServiceManager.start();
    
    // Wait a moment for engine to start
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Initialize RPA Engine
    await rpaEngine.initialize();
    
    // Activate plugin
    await pluginActivator.activate();
    
    logger.info('Cursor RPA Integration started successfully');
    logger.info(`Yingdao RPA Engine running on port ${engineServiceManager.getPort()}`);
    
    // Keep the process running
    process.on('SIGINT', async () => {
      logger.info('Received SIGINT, shutting down...');
      await shutdown();
      process.exit(0);
    });
    
    process.on('SIGTERM', async () => {
      logger.info('Received SIGTERM, shutting down...');
      await shutdown();
      process.exit(0);
    });
    
    // For development, let it run longer to test the engine
    setTimeout(async () => {
      logger.info('Development timeout reached, shutting down...');
      await shutdown();
      process.exit(0);
    }, 30000); // Run for 30 seconds
    
  } catch (error) {
    logger.error('Failed to start application:', error);
    process.exit(1);
  }
}

async function shutdown() {
  try {
    logger.info('Shutting down...');
    
    // Deactivate plugin
    await pluginActivator.deactivate();
    
    // Shutdown RPA Engine
    await rpaEngine.shutdown();
    
    // Stop engine service
    if (engineServiceManager.isServiceRunning()) {
      await engineServiceManager.stop();
    }
    
    logger.info('Shutdown completed');
  } catch (error) {
    logger.error('Error during shutdown:', error);
  }
}

// Start the application
main().catch(error => {
  logger.error('Unhandled error:', error);
  process.exit(1);
});

export { main, shutdown };