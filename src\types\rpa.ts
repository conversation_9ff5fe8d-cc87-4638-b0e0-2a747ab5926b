export interface RPAEngine {
  id: string;
  name: string;
  version: string;
  status: 'running' | 'stopped' | 'error';
  endpoint: string;
  capabilities: string[];
}

export interface RPAProcess {
  id: string;
  name: string;
  description: string;
  status: 'idle' | 'running' | 'completed' | 'error';
  startTime?: Date;
  endTime?: Date;
  variables: Record<string, any>;
  logs: RPAProcessLog[];
}

export interface RPAProcessLog {
  id: string;
  timestamp: Date;
  level: 'info' | 'warn' | 'error' | 'debug';
  message: string;
  details?: any;
}

export interface RPAExecutionConfig {
  engineId: string;
  processId: string;
  parameters: Record<string, any>;
  timeout?: number;
  retryCount?: number;
  debugMode?: boolean;
}

export interface RPAExecutionResult {
  success: boolean;
  output?: any;
  error?: string;
  executionTime: number;
  logs: RPAProcessLog[];
}

export interface ConnectionConfig {
  host: string;
  port: number;
  username?: string;
  password?: string;
  apiKey?: string;
  timeout: number;
  retryAttempts: number;
}