# 常用命令参考手册

## A.1 AutoCAD .NET API 常用命令

### A.1.1 基础绘图命令

**绘制基本图形**
```csharp
// 绘制直线
[CommandMethod("DRAWLINE")]
public void DrawLine()
{
    Document doc = Application.DocumentManager.MdiActiveDocument;
    Database db = doc.Database;
    Editor ed = doc.Editor;
    
    PromptPointOptions startOpt = new PromptPointOptions("\n选择起点: ");
    PromptPointResult startResult = ed.GetPoint(startOpt);
    if (startResult.Status != PromptStatus.OK) return;
    
    PromptPointOptions endOpt = new PromptPointOptions("\n选择终点: ");
    endOpt.UseBasePoint = true;
    endOpt.BasePoint = startResult.Value;
    PromptPointResult endResult = ed.GetPoint(endOpt);
    if (endResult.Status != PromptStatus.OK) return;
    
    using (Transaction trans = db.TransactionManager.StartTransaction())
    {
        BlockTable bt = trans.GetObject(db.BlockTableId, OpenMode.ForRead) as BlockTable;
        BlockTableRecord btr = trans.GetObject(bt[BlockTableRecord.ModelSpace], OpenMode.ForWrite) as BlockTableRecord;
        
        Line line = new Line(startResult.Value, endResult.Value);
        btr.AppendEntity(line);
        trans.AddNewlyCreatedDBObject(line, true);
        
        trans.Commit();
    }
}

// 绘制圆
[CommandMethod("DRAWCIRCLE")]
public void DrawCircle()
{
    Document doc = Application.DocumentManager.MdiActiveDocument;
    Database db = doc.Database;
    Editor ed = doc.Editor;
    
    PromptPointOptions centerOpt = new PromptPointOptions("\n选择圆心: ");
    PromptPointResult centerResult = ed.GetPoint(centerOpt);
    if (centerResult.Status != PromptStatus.OK) return;
    
    PromptDistanceOptions radiusOpt = new PromptDistanceOptions("\n输入半径: ");
    radiusOpt.UseBasePoint = true;
    radiusOpt.BasePoint = centerResult.Value;
    PromptDoubleResult radiusResult = ed.GetDouble(radiusOpt);
    if (radiusResult.Status != PromptStatus.OK) return;
    
    using (Transaction trans = db.TransactionManager.StartTransaction())
    {
        BlockTable bt = trans.GetObject(db.BlockTableId, OpenMode.ForRead) as BlockTable;
        BlockTableRecord btr = trans.GetObject(bt[BlockTableRecord.ModelSpace], OpenMode.ForWrite) as BlockTableRecord;
        
        Circle circle = new Circle(centerResult.Value, Vector3d.ZAxis, radiusResult.Value);
        btr.AppendEntity(circle);
        trans.AddNewlyCreatedDBObject(circle, true);
        
        trans.Commit();
    }
}

// 绘制矩形
[CommandMethod("DRAWRECTANGLE")]
public void DrawRectangle()
{
    Document doc = Application.DocumentManager.MdiActiveDocument;
    Database db = doc.Database;
    Editor ed = doc.Editor;
    
    PromptPointOptions corner1Opt = new PromptPointOptions("\n选择第一角点: ");
    PromptPointResult corner1Result = ed.GetPoint(corner1Opt);
    if (corner1Result.Status != PromptStatus.OK) return;
    
    PromptCornerOptions corner2Opt = new PromptCornerOptions("\n选择对角点: ");
    corner2Opt.UseBasePoint = true;
    corner2Opt.BasePoint = corner1Result.Value;
    PromptPointResult corner2Result = ed.GetPoint(corner2Opt);
    if (corner2Result.Status != PromptStatus.OK) return;
    
    using (Transaction trans = db.TransactionManager.StartTransaction())
    {
        BlockTable bt = trans.GetObject(db.BlockTableId, OpenMode.ForRead) as BlockTable;
        BlockTableRecord btr = trans.GetObject(bt[BlockTableRecord.ModelSpace], OpenMode.ForWrite) as BlockTableRecord;
        
        Polyline rect = new Polyline();
        rect.AddVertexAt(0, new Point2d(corner1Result.Value.X, corner1Result.Value.Y), 0, 0, 0);
        rect.AddVertexAt(1, new Point2d(corner2Result.Value.X, corner1Result.Value.Y), 0, 0, 0);
        rect.AddVertexAt(2, new Point2d(corner2Result.Value.X, corner2Result.Value.Y), 0, 0, 0);
        rect.AddVertexAt(3, new Point2d(corner1Result.Value.X, corner2Result.Value.Y), 0, 0, 0);
        rect.Closed = true;
        
        btr.AppendEntity(rect);
        trans.AddNewlyCreatedDBObject(rect, true);
        
        trans.Commit();
    }
}
```

### A.1.2 文字和标注命令

**创建文字**
```csharp
// 创建单行文字
[CommandMethod("CREATESINGLELINETEXT")]
public void CreateSingleLineText()
{
    Document doc = Application.DocumentManager.MdiActiveDocument;
    Database db = doc.Database;
    Editor ed = doc.Editor;
    
    PromptPointOptions insertOpt = new PromptPointOptions("\n选择插入点: ");
    PromptPointResult insertResult = ed.GetPoint(insertOpt);
    if (insertResult.Status != PromptStatus.OK) return;
    
    PromptStringOptions textOpt = new PromptStringOptions("\n输入文字内容: ");
    PromptResult textResult = ed.GetString(textOpt);
    if (textResult.Status != PromptStatus.OK) return;
    
    PromptDoubleOptions heightOpt = new PromptDoubleOptions("\n输入文字高度: ");
    heightOpt.DefaultValue = 3.5;
    PromptDoubleResult heightResult = ed.GetDouble(heightOpt);
    if (heightResult.Status != PromptStatus.OK) return;
    
    using (Transaction trans = db.TransactionManager.StartTransaction())
    {
        BlockTable bt = trans.GetObject(db.BlockTableId, OpenMode.ForRead) as BlockTable;
        BlockTableRecord btr = trans.GetObject(bt[BlockTableRecord.ModelSpace], OpenMode.ForWrite) as BlockTableRecord;
        
        DBText text = new DBText();
        text.Position = insertResult.Value;
        text.TextString = textResult.StringResult;
        text.Height = heightResult.Value;
        
        btr.AppendEntity(text);
        trans.AddNewlyCreatedDBObject(text, true);
        
        trans.Commit();
    }
}

// 创建多行文字
[CommandMethod("CREATEMULTILINETEXT")]
public void CreateMultiLineText()
{
    Document doc = Application.DocumentManager.MdiActiveDocument;
    Database db = doc.Database;
    Editor ed = doc.Editor;
    
    PromptPointOptions insertOpt = new PromptPointOptions("\n选择插入点: ");
    PromptPointResult insertResult = ed.GetPoint(insertOpt);
    if (insertResult.Status != PromptStatus.OK) return;
    
    PromptStringOptions textOpt = new PromptStringOptions("\n输入文字内容: ");
    PromptResult textResult = ed.GetString(textOpt);
    if (textResult.Status != PromptStatus.OK) return;
    
    PromptDoubleOptions widthOpt = new PromptDoubleOptions("\n输入文字宽度: ");
    widthOpt.DefaultValue = 50.0;
    PromptDoubleResult widthResult = ed.GetDouble(widthOpt);
    if (widthResult.Status != PromptStatus.OK) return;
    
    using (Transaction trans = db.TransactionManager.StartTransaction())
    {
        BlockTable bt = trans.GetObject(db.BlockTableId, OpenMode.ForRead) as BlockTable;
        BlockTableRecord btr = trans.GetObject(bt[BlockTableRecord.ModelSpace], OpenMode.ForWrite) as BlockTableRecord;
        
        MText mtext = new MText();
        mtext.Location = insertResult.Value;
        mtext.Contents = textResult.StringResult;
        mtext.Width = widthResult.Value;
        mtext.Height = 3.5;
        
        btr.AppendEntity(mtext);
        trans.AddNewlyCreatedDBObject(mtext, true);
        
        trans.Commit();
    }
}
```

**创建标注**
```csharp
// 创建线性标注
[CommandMethod("CREATELINEARDIMENSION")]
public void CreateLinearDimension()
{
    Document doc = Application.DocumentManager.MdiActiveDocument;
    Database db = doc.Database;
    Editor ed = doc.Editor;
    
    PromptPointOptions startOpt = new PromptPointOptions("\n选择标注起点: ");
    PromptPointResult startResult = ed.GetPoint(startOpt);
    if (startResult.Status != PromptStatus.OK) return;
    
    PromptPointOptions endOpt = new PromptPointOptions("\n选择标注终点: ");
    endOpt.UseBasePoint = true;
    endOpt.BasePoint = startResult.Value;
    PromptPointResult endResult = ed.GetPoint(endOpt);
    if (endResult.Status != PromptStatus.OK) return;
    
    PromptPointOptions dimOpt = new PromptPointOptions("\n选择标注位置: ");
    PromptPointResult dimResult = ed.GetPoint(dimOpt);
    if (dimResult.Status != PromptStatus.OK) return;
    
    using (Transaction trans = db.TransactionManager.StartTransaction())
    {
        BlockTable bt = trans.GetObject(db.BlockTableId, OpenMode.ForRead) as BlockTable;
        BlockTableRecord btr = trans.GetObject(bt[BlockTableRecord.ModelSpace], OpenMode.ForWrite) as BlockTableRecord;
        
        AlignedDimension dim = new AlignedDimension(startResult.Value, endResult.Value, dimResult.Value, "", db.Dimstyle);
        
        btr.AppendEntity(dim);
        trans.AddNewlyCreatedDBObject(dim, true);
        
        trans.Commit();
    }
}
```

### A.1.3 图层管理命令

**图层操作**
```csharp
// 创建图层
[CommandMethod("CREATELAYER")]
public void CreateLayer()
{
    Document doc = Application.DocumentManager.MdiActiveDocument;
    Database db = doc.Database;
    Editor ed = doc.Editor;
    
    PromptStringOptions nameOpt = new PromptStringOptions("\n输入图层名称: ");
    PromptResult nameResult = ed.GetString(nameOpt);
    if (nameResult.Status != PromptStatus.OK) return;
    
    PromptIntegerOptions colorOpt = new PromptIntegerOptions("\n输入图层颜色(1-255): ");
    colorOpt.DefaultValue = 7;
    colorOpt.LowerLimit = 1;
    colorOpt.UpperLimit = 255;
    PromptIntegerResult colorResult = ed.GetInteger(colorOpt);
    if (colorResult.Status != PromptStatus.OK) return;
    
    using (Transaction trans = db.TransactionManager.StartTransaction())
    {
        LayerTable lt = trans.GetObject(db.LayerTableId, OpenMode.ForWrite) as LayerTable;
        
        if (!lt.Has(nameResult.StringResult))
        {
            LayerTableRecord layer = new LayerTableRecord();
            layer.Name = nameResult.StringResult;
            layer.Color = Color.FromColorIndex(ColorMethod.ByColor, (short)colorResult.Value);
            
            lt.Add(layer);
            trans.AddNewlyCreatedDBObject(layer, true);
            
            ed.WriteMessage($"\n图层 '{nameResult.StringResult}' 创建成功");
        }
        else
        {
            ed.WriteMessage($"\n图层 '{nameResult.StringResult}' 已存在");
        }
        
        trans.Commit();
    }
}

// 设置当前图层
[CommandMethod("SETCURRENTLAYER")]
public void SetCurrentLayer()
{
    Document doc = Application.DocumentManager.MdiActiveDocument;
    Database db = doc.Database;
    Editor ed = doc.Editor;
    
    PromptStringOptions nameOpt = new PromptStringOptions("\n输入图层名称: ");
    PromptResult nameResult = ed.GetString(nameOpt);
    if (nameResult.Status != PromptStatus.OK) return;
    
    using (Transaction trans = db.TransactionManager.StartTransaction())
    {
        LayerTable lt = trans.GetObject(db.LayerTableId, OpenMode.ForRead) as LayerTable;
        
        if (lt.Has(nameResult.StringResult))
        {
            db.Clayer = lt[nameResult.StringResult];
            ed.WriteMessage($"\n当前图层设置为 '{nameResult.StringResult}'");
        }
        else
        {
            ed.WriteMessage($"\n图层 '{nameResult.StringResult}' 不存在");
        }
        
        trans.Commit();
    }
}
```

### A.1.4 块操作命令

**创建和插入块**
```csharp
// 创建块
[CommandMethod("CREATEBLOCK")]
public void CreateBlock()
{
    Document doc = Application.DocumentManager.MdiActiveDocument;
    Database db = doc.Database;
    Editor ed = doc.Editor;
    
    PromptStringOptions nameOpt = new PromptStringOptions("\n输入块名称: ");
    PromptResult nameResult = ed.GetString(nameOpt);
    if (nameResult.Status != PromptStatus.OK) return;
    
    PromptPointOptions baseOpt = new PromptPointOptions("\n选择基点: ");
    PromptPointResult baseResult = ed.GetPoint(baseOpt);
    if (baseResult.Status != PromptStatus.OK) return;
    
    PromptSelectionOptions selOpt = new PromptSelectionOptions();
    PromptSelectionResult selResult = ed.GetSelection(selOpt);
    if (selResult.Status != PromptStatus.OK) return;
    
    using (Transaction trans = db.TransactionManager.StartTransaction())
    {
        BlockTable bt = trans.GetObject(db.BlockTableId, OpenMode.ForWrite) as BlockTable;
        
        if (!bt.Has(nameResult.StringResult))
        {
            BlockTableRecord block = new BlockTableRecord();
            block.Name = nameResult.StringResult;
            block.Origin = baseResult.Value;
            
            bt.Add(block);
            trans.AddNewlyCreatedDBObject(block, true);
            
            // 复制选中的对象到块定义
            ObjectIdCollection ids = selResult.Value.GetObjectIds();
            foreach (ObjectId id in ids)
            {
                Entity ent = trans.GetObject(id, OpenMode.ForRead) as Entity;
                if (ent != null)
                {
                    Entity clone = ent.Clone() as Entity;
                    block.AppendEntity(clone);
                    trans.AddNewlyCreatedDBObject(clone, true);
                }
            }
            
            ed.WriteMessage($"\n块 '{nameResult.StringResult}' 创建成功");
        }
        else
        {
            ed.WriteMessage($"\n块 '{nameResult.StringResult}' 已存在");
        }
        
        trans.Commit();
    }
}

// 插入块
[CommandMethod("INSERTBLOCK")]
public void InsertBlock()
{
    Document doc = Application.DocumentManager.MdiActiveDocument;
    Database db = doc.Database;
    Editor ed = doc.Editor;
    
    PromptStringOptions nameOpt = new PromptStringOptions("\n输入块名称: ");
    PromptResult nameResult = ed.GetString(nameOpt);
    if (nameResult.Status != PromptStatus.OK) return;
    
    PromptPointOptions insertOpt = new PromptPointOptions("\n选择插入点: ");
    PromptPointResult insertResult = ed.GetPoint(insertOpt);
    if (insertResult.Status != PromptStatus.OK) return;
    
    PromptDoubleOptions scaleOpt = new PromptDoubleOptions("\n输入缩放比例: ");
    scaleOpt.DefaultValue = 1.0;
    scaleOpt.AllowZero = false;
    scaleOpt.AllowNegative = false;
    PromptDoubleResult scaleResult = ed.GetDouble(scaleOpt);
    if (scaleResult.Status != PromptStatus.OK) return;
    
    PromptDoubleOptions rotateOpt = new PromptDoubleOptions("\n输入旋转角度: ");
    rotateOpt.DefaultValue = 0.0;
    PromptDoubleResult rotateResult = ed.GetDouble(rotateOpt);
    if (rotateResult.Status != PromptStatus.OK) return;
    
    using (Transaction trans = db.TransactionManager.StartTransaction())
    {
        BlockTable bt = trans.GetObject(db.BlockTableId, OpenMode.ForRead) as BlockTable;
        
        if (bt.Has(nameResult.StringResult))
        {
            BlockTableRecord btr = trans.GetObject(bt[BlockTableRecord.ModelSpace], OpenMode.ForWrite) as BlockTableRecord;
            
            BlockReference blockRef = new BlockReference(insertResult.Value, bt[nameResult.StringResult]);
            blockRef.ScaleFactors = new Scale3d(scaleResult.Value);
            blockRef.Rotation = rotateResult.Value * Math.PI / 180.0;
            
            btr.AppendEntity(blockRef);
            trans.AddNewlyCreatedDBObject(blockRef, true);
            
            ed.WriteMessage($"\n块 '{nameResult.StringResult}' 插入成功");
        }
        else
        {
            ed.WriteMessage($"\n块 '{nameResult.StringResult}' 不存在");
        }
        
        trans.Commit();
    }
}
```

### A.1.5 选择和过滤命令

**对象选择**
```csharp
// 选择指定类型的对象
[CommandMethod("SELECTBYTYPE")]
public void SelectByType()
{
    Document doc = Application.DocumentManager.MdiActiveDocument;
    Database db = doc.Database;
    Editor ed = doc.Editor;
    
    PromptKeywordOptions typeOpt = new PromptKeywordOptions("\n选择对象类型: ");
    typeOpt.Keywords.Add("LINE");
    typeOpt.Keywords.Add("CIRCLE");
    typeOpt.Keywords.Add("ARC");
    typeOpt.Keywords.Add("POLYLINE");
    typeOpt.Keywords.Add("TEXT");
    typeOpt.Keywords.Add("DIMENSION");
    typeOpt.Keywords.Add("BLOCK");
    typeOpt.Default = "LINE";
    
    PromptResult typeResult = ed.GetKeywords(typeOpt);
    if (typeResult.Status != PromptStatus.OK) return;
    
    TypedValue[] filterValues = new TypedValue[1];
    switch (typeResult.StringResult)
    {
        case "LINE":
            filterValues[0] = new TypedValue((int)DxfCode.Start, "LINE");
            break;
        case "CIRCLE":
            filterValues[0] = new TypedValue((int)DxfCode.Start, "CIRCLE");
            break;
        case "ARC":
            filterValues[0] = new TypedValue((int)DxfCode.Start, "ARC");
            break;
        case "POLYLINE":
            filterValues[0] = new TypedValue((int)DxfCode.Start, "LWPOLYLINE");
            break;
        case "TEXT":
            filterValues[0] = new TypedValue((int)DxfCode.Start, "TEXT");
            break;
        case "DIMENSION":
            filterValues[0] = new TypedValue((int)DxfCode.Start, "DIMENSION");
            break;
        case "BLOCK":
            filterValues[0] = new TypedValue((int)DxfCode.Start, "INSERT");
            break;
    }
    
    SelectionFilter filter = new SelectionFilter(filterValues);
    PromptSelectionResult selResult = ed.SelectAll(filter);
    
    if (selResult.Status == PromptStatus.OK)
    {
        ed.WriteMessage($"\n找到 {selResult.Value.Count} 个 {typeResult.StringResult} 对象");
        
        // 高亮显示选中的对象
        ObjectId[] ids = selResult.Value.GetObjectIds();
        foreach (ObjectId id in ids)
        {
            Entity ent = id.GetObject(OpenMode.ForRead) as Entity;
            if (ent != null)
            {
                ent.Highlight();
            }
        }
    }
    else
    {
        ed.WriteMessage($"\n未找到 {typeResult.StringResult} 对象");
    }
}

// 选择指定图层上的对象
[CommandMethod("SELECTBYLAYER")]
public void SelectByLayer()
{
    Document doc = Application.DocumentManager.MdiActiveDocument;
    Database db = doc.Database;
    Editor ed = doc.Editor;
    
    PromptStringOptions layerOpt = new PromptStringOptions("\n输入图层名称: ");
    PromptResult layerResult = ed.GetString(layerOpt);
    if (layerResult.Status != PromptStatus.OK) return;
    
    TypedValue[] filterValues = new TypedValue[1];
    filterValues[0] = new TypedValue((int)DxfCode.LayerName, layerResult.StringResult);
    
    SelectionFilter filter = new SelectionFilter(filterValues);
    PromptSelectionResult selResult = ed.SelectAll(filter);
    
    if (selResult.Status == PromptStatus.OK)
    {
        ed.WriteMessage($"\n找到 {selResult.Value.Count} 个在图层 '{layerResult.StringResult}' 上的对象");
        
        // 高亮显示选中的对象
        ObjectId[] ids = selResult.Value.GetObjectIds();
        foreach (ObjectId id in ids)
        {
            Entity ent = id.GetObject(OpenMode.ForRead) as Entity;
            if (ent != null)
            {
                ent.Highlight();
            }
        }
    }
    else
    {
        ed.WriteMessage($"\n未找到在图层 '{layerResult.StringResult}' 上的对象");
    }
}
```

### A.1.6 文件操作命令

**文件处理**
```csharp
// 保存当前文件
[CommandMethod("QUICKSAVE")]
public void QuickSave()
{
    Document doc = Application.DocumentManager.MdiActiveDocument;
    Database db = doc.Database;
    Editor ed = doc.Editor;
    
    try
    {
        if (string.IsNullOrEmpty(doc.Name))
        {
            // 新文件，需要选择保存路径
            string filePath = SelectSaveFilePath();
            if (!string.IsNullOrEmpty(filePath))
            {
                db.SaveAs(filePath, DwgVersion.Current);
                ed.WriteMessage($"\n文件已保存到: {filePath}");
            }
        }
        else
        {
            // 已有文件，直接保存
            doc.Database.Save();
            ed.WriteMessage($"\n文件已保存: {doc.Name}");
        }
    }
    catch (System.Exception ex)
    {
        ed.WriteMessage($"\n保存文件失败: {ex.Message}");
    }
}

// 另存为
[CommandMethod("SAVEAS")]
public void SaveAs()
{
    Document doc = Application.DocumentManager.MdiActiveDocument;
    Database db = doc.Database;
    Editor ed = doc.Editor;
    
    try
    {
        string filePath = SelectSaveFilePath();
        if (!string.IsNullOrEmpty(filePath))
        {
            db.SaveAs(filePath, DwgVersion.Current);
            ed.WriteMessage($"\n文件已另存为: {filePath}");
        }
    }
    catch (System.Exception ex)
    {
        ed.WriteMessage($"\n另存为文件失败: {ex.Message}");
    }
}

// 打开文件
[CommandMethod("OPENFILE")]
public void OpenFile()
{
    Editor ed = Application.DocumentManager.MdiActiveDocument.Editor;
    
    try
    {
        string filePath = SelectOpenFilePath();
        if (!string.IsNullOrEmpty(filePath))
        {
            DocumentCollection docCollection = Application.DocumentManager;
            docCollection.Open(filePath, false);
            ed.WriteMessage($"\n文件已打开: {filePath}");
        }
    }
    catch (System.Exception ex)
    {
        ed.WriteMessage($"\n打开文件失败: {ex.Message}");
    }
}

// 辅助方法
private string SelectSaveFilePath()
{
    using (var dialog = new System.Windows.Forms.SaveFileDialog())
    {
        dialog.Filter = "AutoCAD文件 (*.dwg)|*.dwg|所有文件 (*.*)|*.*";
        dialog.Title = "选择保存路径";
        
        if (dialog.ShowDialog() == System.Windows.Forms.DialogResult.OK)
        {
            return dialog.FileName;
        }
    }
    
    return null;
}

private string SelectOpenFilePath()
{
    using (var dialog = new System.Windows.Forms.OpenFileDialog())
    {
        dialog.Filter = "AutoCAD文件 (*.dwg)|*.dwg|所有文件 (*.*)|*.*";
        dialog.Title = "选择要打开的文件";
        
        if (dialog.ShowDialog() == System.Windows.Forms.DialogResult.OK)
        {
            return dialog.FileName;
        }
    }
    
    return null;
}
```

### A.1.7 实用工具命令

**实用功能**
```csharp
// 计算对象数量
[CommandMethod("COUNTOBJECTS")]
public void CountObjects()
{
    Document doc = Application.DocumentManager.MdiActiveDocument;
    Database db = doc.Database;
    Editor ed = doc.Editor;
    
    using (Transaction trans = db.TransactionManager.StartTransaction())
    {
        BlockTable bt = trans.GetObject(db.BlockTableId, OpenMode.ForRead) as BlockTable;
        BlockTableRecord modelSpace = trans.GetObject(bt[BlockTableRecord.ModelSpace], OpenMode.ForRead) as BlockTableRecord;
        
        var objectCounts = new Dictionary<string, int>();
        
        foreach (ObjectId objId in modelSpace)
        {
            Entity ent = trans.GetObject(objId, OpenMode.ForRead) as Entity;
            if (ent != null)
            {
                string typeName = ent.GetType().Name;
                if (!objectCounts.ContainsKey(typeName))
                    objectCounts[typeName] = 0;
                objectCounts[typeName]++;
            }
        }
        
        ed.WriteMessage("\n=== 对象统计 ===");
        foreach (var kvp in objectCounts)
        {
            ed.WriteMessage($"\n{kvp.Key}: {kvp.Value} 个");
        }
        
        trans.Commit();
    }
}

// 清理未使用的对象
[CommandMethod("PURGEUNUSED")]
public void PurgeUnused()
{
    Document doc = Application.DocumentManager.MdiActiveDocument;
    Database db = doc.Database;
    Editor ed = doc.Editor;
    
    try
    {
        int purgedCount = db.Purge();
        ed.WriteMessage($"\n清理完成，共清理 {purgedCount} 个未使用的对象");
    }
    catch (System.Exception ex)
    {
        ed.WriteMessage($"\n清理失败: {ex.Message}");
    }
}

// 显示对象属性
[CommandMethod("SHOWPROPERTIES")]
public void ShowProperties()
{
    Document doc = Application.DocumentManager.MdiActiveDocument;
    Database db = doc.Database;
    Editor ed = doc.Editor;
    
    PromptEntityOptions entOpt = new PromptEntityOptions("\n选择对象: ");
    PromptEntityResult entResult = ed.GetEntity(entOpt);
    if (entResult.Status != PromptStatus.OK) return;
    
    using (Transaction trans = db.TransactionManager.StartTransaction())
    {
        Entity ent = trans.GetObject(entResult.ObjectId, OpenMode.ForRead) as Entity;
        if (ent != null)
        {
            ed.WriteMessage($"\n=== 对象属性 ===");
            ed.WriteMessage($"\n类型: {ent.GetType().Name}");
            ed.WriteMessage($"\n图层: {ent.Layer}");
            ed.WriteMessage($"\n颜色: {ent.Color.ColorIndex}");
            ed.WriteMessage($"\n线型: {ent.Linetype}");
            ed.WriteMessage($"\n线宽: {ent.LineWeight}");
            ed.WriteMessage($"\n句柄: {ent.Handle}");
            
            if (ent is BlockReference blockRef)
            {
                ed.WriteMessage($"\n块名: {blockRef.Name}");
                ed.WriteMessage($"\n位置: {blockRef.Position}");
                ed.WriteMessage($"\n比例: {blockRef.ScaleFactors}");
                ed.WriteMessage($"\n旋转: {blockRef.Rotation * 180 / Math.PI}°");
            }
            else if (ent is Circle circle)
            {
                ed.WriteMessage($"\n圆心: {circle.Center}");
                ed.WriteMessage($"\n半径: {circle.Radius}");
                ed.WriteMessage($"\n面积: {Math.PI * circle.Radius * circle.Radius:F2}");
            }
            else if (ent is Line line)
            {
                ed.WriteMessage($"\n起点: {line.StartPoint}");
                ed.WriteMessage($"\n终点: {line.EndPoint}");
                ed.WriteMessage($"\n长度: {line.Length:F2}");
            }
        }
        
        trans.Commit();
    }
}
```

---

## A.2 CURSOR 提示词模板

### A.2.1 基础开发提示词

**通用开发模板**
```
请生成一个AutoCAD .NET C#类，实现[具体功能]，要求：
1. 命名空间：AutoCAD.[模块名称]
2. 类名：[类名]
3. 命令方法：[命令列表]
4. 功能：[详细功能描述]
5. 异常处理：[异常处理要求]
6. 性能优化：[性能要求]
```

**绘图功能模板**
```
请生成一个AutoCAD绘图功能，要求：
1. 命令名称：[命令名称]
2. 绘制对象：[对象类型]
3. 用户交互：[交互方式]
4. 参数设置：[参数要求]
5. 图层管理：[图层设置]
6. 错误处理：[错误处理]
```

### A.2.2 项目开发提示词

**项目架构模板**
```
请为AutoCAD项目设计完整的架构，包括：
1. 项目概述和目标
2. 模块划分和职责
3. 数据结构设计
4. 接口定义
5. 异常处理机制
6. 性能优化策略
```

**标准化模板**
```
请为AutoCAD标准化提供解决方案，包括：
1. 图层标准定义
2. 命名规范制定
3. 数据结构标准化
4. 代码规范制定
5. 质量控制流程
6. 文档规范
```

### A.2.3 优化和调试提示词

**代码优化模板**
```
请优化以下AutoCAD .NET代码，要求：
1. 性能优化：减少内存使用，提高执行速度
2. 代码重构：提高可读性和可维护性
3. 异常处理：增强错误处理能力
4. 事务优化：优化事务处理逻辑
5. 资源管理：确保资源正确释放

[原始代码]
```

**调试模板**
```
请为以下AutoCAD代码添加调试功能，要求：
1. 日志记录：添加详细的调试信息
2. 错误追踪：提供错误定位和诊断
3. 性能监控：添加性能统计信息
4. 状态报告：生成执行状态报告
5. 异常恢复：提供异常恢复机制

[原始代码]
```

---

## A.3 常见问题解决方案

### A.3.1 编译和运行问题

**问题1：引用缺失**
```csharp
// 确保添加了必要的引用
using Autodesk.AutoCAD.Runtime;
using Autodesk.AutoCAD.ApplicationServices;
using Autodesk.AutoCAD.DatabaseServices;
using Autodesk.AutoCAD.Geometry;
using Autodesk.AutoCAD.EditorInput;
```

**问题2：版本兼容性**
```csharp
// 检查AutoCAD版本兼容性
public class VersionChecker
{
    public static bool IsCompatibleVersion()
    {
        try
        {
            var version = Application.Version;
            // 检查版本号
            return version.Major >= 20; // AutoCAD 2020+
        }
        catch
        {
            return false;
        }
    }
}
```

### A.3.2 性能问题

**问题1：大文件处理慢**
```csharp
// 使用分批处理优化大文件
public class LargeFileProcessor
{
    public void ProcessLargeFile(Database db)
    {
        const int batchSize = 1000;
        int totalProcessed = 0;
        
        using (Transaction trans = db.TransactionManager.StartTransaction())
        {
            BlockTable bt = trans.GetObject(db.BlockTableId, OpenMode.ForRead) as BlockTable;
            BlockTableRecord modelSpace = trans.GetObject(bt[BlockTableRecord.ModelSpace], OpenMode.ForWrite) as BlockTableRecord;
            
            var objectIds = new List<ObjectId>();
            foreach (ObjectId id in modelSpace)
            {
                objectIds.Add(id);
            }
            
            for (int i = 0; i < objectIds.Count; i += batchSize)
            {
                int currentBatchSize = Math.Min(batchSize, objectIds.Count - i);
                var batchIds = objectIds.GetRange(i, currentBatchSize);
                
                ProcessBatch(trans, batchIds);
                totalProcessed += currentBatchSize;
                
                // 定期提交事务
                if (i % (batchSize * 10) == 0)
                {
                    trans.Commit();
                    trans = db.TransactionManager.StartTransaction();
                }
            }
            
            trans.Commit();
        }
    }
    
    private void ProcessBatch(Transaction trans, List<ObjectId> objectIds)
    {
        // 处理批次对象
    }
}
```

**问题2：内存泄漏**
```csharp
// 正确的资源管理
public class ResourceManager
{
    public void ProcessWithProperResourceManagement()
    {
        Document doc = Application.DocumentManager.MdiActiveDocument;
        Database db = doc.Database;
        
        // 使用using语句确保资源正确释放
        using (Transaction trans = db.TransactionManager.StartTransaction())
        {
            // 处理逻辑
            trans.Commit();
        }
    }
}
```

### A.3.3 事务处理问题

**问题1：事务嵌套**
```csharp
// 正确的事务嵌套处理
public class TransactionManager
{
    public void ProcessWithNestedTransactions()
    {
        Document doc = Application.DocumentManager.MdiActiveDocument;
        Database db = doc.Database;
        
        // 外层事务
        using (Transaction outerTrans = db.TransactionManager.StartTransaction())
        {
            try
            {
                // 内层事务
                using (Transaction innerTrans = db.TransactionManager.StartTransaction())
                {
                    // 内层处理逻辑
                    innerTrans.Commit();
                }
                
                // 外层处理逻辑
                outerTrans.Commit();
            }
            catch
            {
                outerTrans.Abort();
                throw;
            }
        }
    }
}
```

### A.3.4 用户界面问题

**问题1：交互式输入**
```csharp
// 改进的用户交互
public class UserInputManager
{
    public Point3d GetPointWithPreview(Editor ed, string prompt, Point3d basePoint)
    {
        PromptPointOptions opt = new PromptPointOptions(prompt);
        opt.UseBasePoint = true;
        opt.BasePoint = basePoint;
        
        // 添加动态输入
        opt.UseDashedLine = true;
        opt.AllowNone = true;
        
        PromptPointResult result = ed.GetPoint(opt);
        
        if (result.Status == PromptStatus.OK)
        {
            return result.Value;
        }
        else
        {
            return basePoint;
        }
    }
}
```

---

这个命令参考手册提供了AutoCAD .NET API中最常用的命令和功能示例，可以作为开发过程中的快速参考。每个命令都包含了完整的实现代码和详细的注释说明。