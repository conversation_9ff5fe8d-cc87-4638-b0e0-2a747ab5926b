/**
 * 影刀RPA引擎 JavaScript/Node.js 客户端示例
 * 
 * 这个示例展示了如何使用JavaScript与影刀RPA引擎进行交互
 */

class RPAEngineClient {
    constructor(baseUrl = 'http://localhost:8080') {
        this.baseUrl = baseUrl;
        this.ws = null;
        this.eventHandlers = new Map();
    }

    // 基础API调用方法
    async request(endpoint, options = {}) {
        const url = `${this.baseUrl}${endpoint}`;
        const config = {
            headers: {
                'Content-Type': 'application/json',
                ...options.headers
            },
            ...options
        };

        try {
            const response = await fetch(url, config);
            
            if (!response.ok) {
                const error = await response.json();
                throw new Error(`API Error: ${error.error?.message || response.statusText}`);
            }

            return await response.json();
        } catch (error) {
            console.error('Request failed:', error);
            throw error;
        }
    }

    // 健康检查
    async healthCheck() {
        return await this.request('/health');
    }

    // 获取引擎信息
    async getEngineInfo() {
        return await this.request('/info');
    }

    // 获取所有流程
    async getProcesses(filters = {}) {
        const params = new URLSearchParams(filters);
        return await this.request(`/processes?${params}`);
    }

    // 创建流程
    async createProcess(processData) {
        return await this.request('/processes', {
            method: 'POST',
            body: JSON.stringify(processData)
        });
    }

    // 获取特定流程
    async getProcess(processId) {
        return await this.request(`/processes/${processId}`);
    }

    // 更新流程
    async updateProcess(processId, updateData) {
        return await this.request(`/processes/${processId}`, {
            method: 'PUT',
            body: JSON.stringify(updateData)
        });
    }

    // 删除流程
    async deleteProcess(processId) {
        return await this.request(`/processes/${processId}`, {
            method: 'DELETE'
        });
    }

    // 执行流程
    async executeProcess(processId, options = {}) {
        const executionData = {
            parameters: options.parameters || {},
            debugMode: options.debugMode || false,
            timeout: options.timeout || 300000
        };

        return await this.request(`/processes/${processId}/execute`, {
            method: 'POST',
            body: JSON.stringify(executionData)
        });
    }

    // 获取执行状态
    async getExecutionStatus(executionId) {
        return await this.request(`/executions/${executionId}`);
    }

    // 停止执行
    async stopExecution(executionId) {
        return await this.request(`/executions/${executionId}/stop`, {
            method: 'POST'
        });
    }

    // 获取流程变量
    async getProcessVariables(processId) {
        return await this.request(`/processes/${processId}/variables`);
    }

    // 更新流程变量
    async updateProcessVariables(processId, variables) {
        return await this.request(`/processes/${processId}/variables`, {
            method: 'PUT',
            body: JSON.stringify({ variables })
        });
    }

    // WebSocket 连接
    connectWebSocket() {
        return new Promise((resolve, reject) => {
            this.ws = new WebSocket(`ws://${this.baseUrl.replace('http://', '')}`);

            this.ws.onopen = () => {
                console.log('WebSocket 连接已建立');
                resolve();
            };

            this.ws.onmessage = (event) => {
                try {
                    const data = JSON.parse(event.data);
                    this.handleWebSocketMessage(data);
                } catch (error) {
                    console.error('解析WebSocket消息失败:', error);
                }
            };

            this.ws.onclose = () => {
                console.log('WebSocket 连接已关闭');
                this.ws = null;
            };

            this.ws.onerror = (error) => {
                console.error('WebSocket 错误:', error);
                reject(error);
            };
        });
    }

    // 处理WebSocket消息
    handleWebSocketMessage(data) {
        const eventType = data.type;
        const handlers = this.eventHandlers.get(eventType) || [];
        
        handlers.forEach(handler => {
            try {
                handler(data);
            } catch (error) {
                console.error('事件处理器错误:', error);
            }
        });
    }

    // 注册事件处理器
    on(eventType, handler) {
        if (!this.eventHandlers.has(eventType)) {
            this.eventHandlers.set(eventType, []);
        }
        this.eventHandlers.get(eventType).push(handler);
    }

    // 发送WebSocket消息
    sendWebSocketMessage(message) {
        if (this.ws && this.ws.readyState === WebSocket.OPEN) {
            this.ws.send(JSON.stringify(message));
        } else {
            console.warn('WebSocket 未连接');
        }
    }

    // 关闭WebSocket连接
    disconnectWebSocket() {
        if (this.ws) {
            this.ws.close();
            this.ws = null;
        }
    }
}

// 使用示例
async function runExamples() {
    const client = new RPAEngineClient();

    try {
        console.log('=== 影刀RPA引擎 JavaScript 客户端示例 ===\n');

        // 1. 健康检查
        console.log('1. 健康检查...');
        const health = await client.healthCheck();
        console.log('✅ 引擎状态:', health.status, '运行时间:', Math.round(health.uptime), '秒\n');

        // 2. 获取引擎信息
        console.log('2. 获取引擎信息...');
        const info = await client.getEngineInfo();
        console.log('✅ 引擎名称:', info.name);
        console.log('✅ 引擎版本:', info.version);
        console.log('✅ 支持功能:', info.capabilities.join(', '), '\n');

        // 3. 创建流程
        console.log('3. 创建新流程...');
        const process1 = await client.createProcess({
            name: 'Web数据提取示例',
            description: '从目标网站提取数据的示例流程',
            variables: {
                targetUrl: 'https://example.com',
                selectors: ['title', 'content', 'price'],
                outputFormat: 'json'
            }
        });
        console.log('✅ 流程创建成功:', process1.id, process1.name);

        const process2 = await client.createProcess({
            name: '文件处理示例',
            description: '批量处理文件的示例流程',
            variables: {
                inputPath: './input',
                outputPath: './output',
                fileTypes: ['pdf', 'xlsx', 'csv']
            }
        });
        console.log('✅ 流程创建成功:', process2.id, process2.name, '\n');

        // 4. 获取所有流程
        console.log('4. 获取所有流程...');
        const processes = await client.getProcesses();
        console.log('✅ 当前流程数量:', processes.processes.length);
        processes.processes.forEach(p => {
            console.log(`   - ${p.name} (${p.id}) - 状态: ${p.status}`);
        });
        console.log('');

        // 5. 更新流程
        console.log('5. 更新流程变量...');
        await client.updateProcessVariables(process1.id, {
            maxItems: 100,
            timeout: 30000,
            retryCount: 3
        });
        console.log('✅ 流程变量更新成功');

        const updatedVariables = await client.getProcessVariables(process1.id);
        console.log('✅ 更新后的变量:', updatedVariables, '\n');

        // 6. 连接WebSocket
        console.log('6. 连接WebSocket...');
        await client.connectWebSocket();

        // 设置事件监听器
        client.on('execution_started', (data) => {
            console.log(`🚀 执行开始: ${data.executionId}`);
        });

        client.on('execution_progress', (data) => {
            const progress = data.progress.percentage;
            const activity = data.progress.currentActivity;
            console.log(`📊 执行进度: ${progress}% - ${activity}`);
        });

        client.on('execution_completed', (data) => {
            const result = data.result;
            console.log('✅ 执行完成!');
            console.log(`   执行时间: ${result.executionTime}ms`);
            console.log(`   处理项目: ${result.output.processedItems}`);
        });

        client.on('execution_error', (data) => {
            console.log('❌ 执行错误:', data.error);
        });

        // 7. 执行流程
        console.log('7. 执行流程...');
        const execution = await client.executeProcess(process1.id, {
            parameters: {
                action: 'scrape',
                saveToDatabase: true
            },
            debugMode: true
        });
        console.log('✅ 流程执行启动:', execution.executionId);

        // 8. 监控执行状态
        console.log('8. 监控执行状态...');
        let executionStatus;
        let attempts = 0;
        const maxAttempts = 15; // 最多等待30秒

        do {
            await new Promise(resolve => setTimeout(resolve, 2000));
            executionStatus = await client.getExecutionStatus(execution.executionId);
            console.log(`   状态: ${executionStatus.status}, 进度: ${executionStatus.progress?.percentage || 0}%`);
            attempts++;
        } while (
            executionStatus.status === 'running' && 
            attempts < maxAttempts
        );

        console.log('');

        // 9. 清理资源
        console.log('9. 清理测试流程...');
        await client.deleteProcess(process1.id);
        await client.deleteProcess(process2.id);
        console.log('✅ 测试流程已删除');

        // 关闭WebSocket连接
        client.disconnectWebSocket();
        console.log('✅ WebSocket连接已关闭\n');

        console.log('🎉 所有示例执行完成!');

    } catch (error) {
        console.error('❌ 示例执行失败:', error.message);
    }
}

// 监控流程执行的辅助函数
async function monitorExecution(client, executionId, timeout = 30000) {
    return new Promise((resolve, reject) => {
        const startTime = Date.now();
        const interval = setInterval(async () => {
            try {
                const status = await client.getExecutionStatus(executionId);
                
                if (status.status === 'completed') {
                    clearInterval(interval);
                    resolve(status);
                } else if (status.status === 'error' || status.status === 'stopped') {
                    clearInterval(interval);
                    reject(new Error(`执行失败: ${status.status}`));
                } else if (Date.now() - startTime > timeout) {
                    clearInterval(interval);
                    reject(new Error('执行超时'));
                }
            } catch (error) {
                clearInterval(interval);
                reject(error);
            }
        }, 2000);
    });
}

// 批量创建流程示例
async function batchCreateProcesses() {
    const client = new RPAEngineClient();
    const templates = [
        {
            name: '电商价格监控',
            description: '监控电商网站商品价格变化',
            variables: {
                urls: ['https://example.com/product1', 'https://example.com/product2'],
                priceSelector: '.price',
                notifyThreshold: 0.1
            }
        },
        {
            name: '社交媒体数据收集',
            description: '收集社交媒体平台数据',
            variables: {
                platforms: ['twitter', 'facebook', 'instagram'],
                keywords: ['AI', 'automation', 'RPA'],
                maxPosts: 100
            }
        },
        {
            name: '财务报表处理',
            description: '自动化处理财务报表',
            variables: {
                inputFolder: './financial_reports',
                outputFolder: './processed_reports',
                template: 'standard_template'
            }
        }
    ];

    try {
        console.log('批量创建流程...');
        const createdProcesses = [];

        for (const template of templates) {
            const process = await client.createProcess(template);
            createdProcesses.push(process);
            console.log(`✅ 创建流程: ${process.name}`);
        }

        console.log(`✅ 批量创建完成，共创建 ${createdProcesses.length} 个流程`);
        return createdProcesses;
    } catch (error) {
        console.error('批量创建失败:', error);
        throw error;
    }
}

// 如果直接运行此文件，执行示例
if (require.main === module) {
    runExamples().catch(console.error);
}

module.exports = {
    RPAEngineClient,
    runExamples,
    batchCreateProcesses,
    monitorExecution
};