# 建筑图纸自动化

## 4.1.1 标准图框生成

**项目概述：**
建筑图纸自动化是CURSOR+AUTOCAD自动化的重要应用场景。本节将介绍如何使用AutoCAD .NET API实现标准建筑图框的自动化生成。

**完整实现代码：**
```csharp
using Autodesk.AutoCAD.Runtime;
using Autodesk.AutoCAD.ApplicationServices;
using Autodesk.AutoCAD.DatabaseServices;
using Autodesk.AutoCAD.Geometry;
using Autodesk.AutoCAD.EditorInput;
using System;
using System.Collections.Generic;

namespace AutoCAD.Architecture
{
    public class TitleBlockGenerator
    {
        // 图框标准尺寸
        private static readonly Dictionary<string, Tuple<double, double>> PaperSizes = new Dictionary<string, Tuple<double, double>>
        {
            { "A0", Tuple.Create(1189.0, 841.0) },
            { "A1", Tuple.Create(841.0, 594.0) },
            { "A2", Tuple.Create(594.0, 420.0) },
            { "A3", Tuple.Create(420.0, 297.0) },
            { "A4", Tuple.Create(297.0, 210.0) }
        };

        [CommandMethod("CREATETITLEBLOCK")]
        public void CreateTitleBlock()
        {
            Document doc = Application.DocumentManager.MdiActiveDocument;
            Database db = doc.Database;
            Editor ed = doc.Editor;

            try
            {
                // 获取图框参数
                var parameters = GetTitleBlockParameters(ed);
                if (parameters == null)
                    return;

                // 生成图框
                GenerateTitleBlock(db, parameters);

                ed.WriteMessage($"\n{parameters.PaperSize}图框创建完成！");
            }
            catch (System.Exception ex)
            {
                ed.WriteMessage($"\n创建图框时出错：{ex.Message}");
            }
        }

        private TitleBlockParameters GetTitleBlockParameters(Editor ed)
        {
            // 选择图纸尺寸
            PromptKeywordOptions sizeOpt = new PromptKeywordOptions("\n选择图纸尺寸: ");
            sizeOpt.Keywords.Add("A0");
            sizeOpt.Keywords.Add("A1");
            sizeOpt.Keywords.Add("A2");
            sizeOpt.Keywords.Add("A3");
            sizeOpt.Keywords.Add("A4");
            sizeOpt.Keywords.Default = "A3";

            PromptResult sizeResult = ed.GetKeywords(sizeOpt);
            if (sizeResult.Status != PromptStatus.OK)
                return null;

            string paperSize = sizeResult.StringResult;

            // 获取插入点
            PromptPointOptions insertOpt = new PromptPointOptions("\n选择图框插入点: ");
            PromptPointResult insertResult = ed.GetPoint(insertOpt);
            if (insertResult.Status != PromptStatus.OK)
                return null;

            Point3d insertPoint = insertResult.Value;

            // 获取图纸信息
            PromptStringOptions titleOpt = new PromptStringOptions("\n输入图纸标题: ");
            titleOpt.DefaultValue = "建筑平面图";
            PromptResult titleResult = ed.GetString(titleOpt);
            string title = titleResult.StringResult;

            PromptStringOptions drawingOpt = new PromptStringOptions("\n输入图号: ");
            drawingOpt.DefaultValue = "A-001";
            PromptResult drawingResult = ed.GetString(drawingOpt);
            string drawingNumber = drawingResult.StringResult;

            PromptStringOptions designerOpt = new PromptStringOptions("\n输入设计人: ");
            designerOpt.DefaultValue = "设计师";
            PromptResult designerResult = ed.GetString(designerOpt);
            string designer = designerResult.StringResult;

            PromptStringOptions dateOpt = new PromptStringOptions("\n输入日期: ");
            dateOpt.DefaultValue = DateTime.Now.ToString("yyyy-MM-dd");
            PromptResult dateResult = ed.GetString(dateOpt);
            string date = dateResult.StringResult;

            return new TitleBlockParameters
            {
                PaperSize = paperSize,
                InsertPoint = insertPoint,
                Title = title,
                DrawingNumber = drawingNumber,
                Designer = designer,
                Date = date
            };
        }

        private void GenerateTitleBlock(Database db, TitleBlockParameters parameters)
        {
            using (Transaction trans = db.TransactionManager.StartTransaction())
            {
                // 创建图层
                CreateLayers(trans, db);

                // 获取模型空间
                BlockTable bt = trans.GetObject(db.BlockTableId, OpenMode.ForRead) as BlockTable;
                BlockTableRecord btr = trans.GetObject(bt[BlockTableRecord.ModelSpace], OpenMode.ForWrite) as BlockTableRecord;

                // 获取图纸尺寸
                var paperSize = PaperSizes[parameters.PaperSize];
                double width = paperSize.Item1;
                double height = paperSize.Item2;

                // 绘制外边框
                DrawOuterFrame(btr, trans, parameters.InsertPoint, width, height);

                // 绘制内边框
                DrawInnerFrame(btr, trans, parameters.InsertPoint, width, height);

                // 绘制标题栏
                DrawTitleBar(btr, trans, parameters.InsertPoint, width, height, parameters);

                // 绘制会签栏
                DrawSignatureBlock(btr, trans, parameters.InsertPoint, width, height);

                trans.Commit();
            }
        }

        private void CreateLayers(Transaction trans, Database db)
        {
            LayerTable lt = trans.GetObject(db.LayerTableId, OpenMode.ForWrite) as LayerTable;

            // 图框图层
            if (!lt.Has("TITLEBLOCK"))
            {
                LayerTableRecord titleblockLayer = new LayerTableRecord();
                titleblockLayer.Name = "TITLEBLOCK";
                titleblockLayer.Color = Color.FromColorIndex(ColorMethod.ByColor, 7); // 白色
                titleblockLayer.LineWeight = LineWeight.LineWeight050;
                lt.Add(titleblockLayer);
                trans.AddNewlyCreatedDBObject(titleblockLayer, true);
            }

            // 标题栏图层
            if (!lt.Has("TITLEBAR"))
            {
                LayerTableRecord titlebarLayer = new LayerTableRecord();
                titlebarLayer.Name = "TITLEBAR";
                titlebarLayer.Color = Color.FromColorIndex(ColorMethod.ByColor, 7); // 白色
                titlebarLayer.LineWeight = LineWeight.LineWeight025;
                lt.Add(titlebarLayer);
                trans.AddNewlyCreatedDBObject(titlebarLayer, true);
            }

            // 文字图层
            if (!lt.Has("TEXT"))
            {
                LayerTableRecord textLayer = new LayerTableRecord();
                textLayer.Name = "TEXT";
                textLayer.Color = Color.FromColorIndex(ColorMethod.ByColor, 7); // 白色
                lt.Add(textLayer);
                trans.AddNewlyCreatedDBObject(textLayer, true);
            }
        }

        private void DrawOuterFrame(BlockTableRecord btr, Transaction trans, Point3d insertPoint, double width, double height)
        {
            // 外边框
            Polyline outerFrame = new Polyline();
            outerFrame.AddVertexAt(0, new Point2d(insertPoint.X, insertPoint.Y), 0, 0, 0);
            outerFrame.AddVertexAt(1, new Point2d(insertPoint.X + width, insertPoint.Y), 0, 0, 0);
            outerFrame.AddVertexAt(2, new Point2d(insertPoint.X + width, insertPoint.Y + height), 0, 0, 0);
            outerFrame.AddVertexAt(3, new Point2d(insertPoint.X, insertPoint.Y + height), 0, 0, 0);
            outerFrame.Closed = true;
            outerFrame.Layer = "TITLEBLOCK";
            outerFrame.LineWeight = LineWeight.LineWeight070;

            btr.AppendEntity(outerFrame);
            trans.AddNewlyCreatedDBObject(outerFrame, true);
        }

        private void DrawInnerFrame(BlockTableRecord btr, Transaction trans, Point3d insertPoint, double width, double height)
        {
            double margin = 25; // 内边距
            double titleBarHeight = 56; // 标题栏高度

            // 内边框
            Polyline innerFrame = new Polyline();
            innerFrame.AddVertexAt(0, new Point2d(insertPoint.X + margin, insertPoint.Y + margin), 0, 0, 0);
            innerFrame.AddVertexAt(1, new Point2d(insertPoint.X + width - margin, insertPoint.Y + margin), 0, 0, 0);
            innerFrame.AddVertexAt(2, new Point2d(insertPoint.X + width - margin, insertPoint.Y + height - margin), 0, 0, 0);
            innerFrame.AddVertexAt(3, new Point2d(insertPoint.X + margin, insertPoint.Y + height - margin), 0, 0, 0);
            innerFrame.Closed = true;
            innerFrame.Layer = "TITLEBLOCK";
            innerFrame.LineWeight = LineWeight.LineWeight050;

            btr.AppendEntity(innerFrame);
            trans.AddNewlyCreatedDBObject(innerFrame, true);

            // 标题栏分割线
            Line titleBarLine = new Line(
                new Point3d(insertPoint.X + margin, insertPoint.Y + margin + titleBarHeight),
                new Point3d(insertPoint.X + width - margin, insertPoint.Y + margin + titleBarHeight));
            titleBarLine.Layer = "TITLEBAR";
            titleBarLine.LineWeight = LineWeight.LineWeight025;

            btr.AppendEntity(titleBarLine);
            trans.AddNewlyCreatedDBObject(titleBarLine, true);
        }

        private void DrawTitleBar(BlockTableRecord btr, Transaction trans, Point3d insertPoint, double width, double height, TitleBlockParameters parameters)
        {
            double margin = 25;
            double titleBarHeight = 56;
            
            // 标题栏区域
            double titleBarY = insertPoint.Y + margin;
            double titleBarTop = titleBarY + titleBarHeight;

            // 绘制标题栏网格
            DrawTitleBarGrid(btr, trans, insertPoint, width, height);

            // 添加标题栏文字
            AddTitleBarText(btr, trans, insertPoint, width, height, parameters);
        }

        private void DrawTitleBarGrid(BlockTableRecord btr, Transaction trans, Point3d insertPoint, double width, double height)
        {
            double margin = 25;
            double titleBarHeight = 56;
            
            // 标题栏垂直分割线
            double[] verticalLines = { 180, 240, 300, 360, 420, 480 };
            foreach (double x in verticalLines)
            {
                Line line = new Line(
                    new Point3d(insertPoint.X + margin + x, insertPoint.Y + margin),
                    new Point3d(insertPoint.X + margin + x, insertPoint.Y + margin + titleBarHeight));
                line.Layer = "TITLEBAR";
                line.LineWeight = LineWeight.LineWeight025;
                btr.AppendEntity(line);
                trans.AddNewlyCreatedDBObject(line, true);
            }

            // 标题栏水平分割线
            double[] horizontalLines = { 14, 28, 42 };
            foreach (double y in horizontalLines)
            {
                Line line = new Line(
                    new Point3d(insertPoint.X + margin, insertPoint.Y + margin + y),
                    new Point3d(insertPoint.X + width - margin, insertPoint.Y + margin + y));
                line.Layer = "TITLEBAR";
                line.LineWeight = LineWeight.LineWeight025;
                btr.AppendEntity(line);
                trans.AddNewlyCreatedDBObject(line, true);
            }
        }

        private void AddTitleBarText(BlockTableRecord btr, Transaction trans, Point3d insertPoint, double width, double height, TitleBlockParameters parameters)
        {
            double margin = 25;
            double textHeight = 3.5;

            // 添加标题
            DBText titleText = new DBText();
            titleText.Position = new Point3d(insertPoint.X + margin + 10, insertPoint.Y + margin + 35, 0);
            titleText.TextString = parameters.Title;
            titleText.Height = textHeight * 1.5;
            titleText.Layer = "TEXT";
            btr.AppendEntity(titleText);
            trans.AddNewlyCreatedDBObject(titleText, true);

            // 添加图号
            DBText drawingNumberText = new DBText();
            drawingNumberText.Position = new Point3d(insertPoint.X + margin + 460, insertPoint.Y + margin + 35, 0);
            drawingNumberText.TextString = parameters.DrawingNumber;
            drawingNumberText.Height = textHeight;
            drawingNumberText.Layer = "TEXT";
            btr.AppendEntity(drawingNumberText);
            trans.AddNewlyCreatedDBObject(drawingNumberText, true);

            // 添加设计人
            DBText designerText = new DBText();
            designerText.Position = new Point3d(insertPoint.X + margin + 50, insertPoint.Y + margin + 10, 0);
            designerText.TextString = parameters.Designer;
            designerText.Height = textHeight * 0.8;
            designerText.Layer = "TEXT";
            btr.AppendEntity(designerText);
            trans.AddNewlyCreatedDBObject(designerText, true);

            // 添加日期
            DBText dateText = new DBText();
            dateText.Position = new Point3d(insertPoint.X + margin + 200, insertPoint.Y + margin + 10, 0);
            dateText.TextString = parameters.Date;
            dateText.Height = textHeight * 0.8;
            dateText.Layer = "TEXT";
            btr.AppendEntity(dateText);
            trans.AddNewlyCreatedDBObject(dateText, true);

            // 添加标签
            AddLabelText(btr, trans, insertPoint, margin);
        }

        private void AddLabelText(BlockTableRecord btr, Transaction trans, Point3d insertPoint, double margin)
        {
            double textHeight = 2.5;

            // 标签文字
            string[] labels = { "设计", "审核", "批准", "日期", "图号", "比例", "版本" };
            double[] labelX = { 10, 50, 90, 150, 410, 460, 510 };
            double labelY = 25;

            for (int i = 0; i < labels.Length; i++)
            {
                DBText labelText = new DBText();
                labelText.Position = new Point3d(insertPoint.X + margin + labelX[i], insertPoint.Y + margin + labelY, 0);
                labelText.TextString = labels[i];
                labelText.Height = textHeight;
                labelText.Layer = "TEXT";
                btr.AppendEntity(labelText);
                trans.AddNewlyCreatedDBObject(labelText, true);
            }
        }

        private void DrawSignatureBlock(BlockTableRecord btr, Transaction trans, Point3d insertPoint, double width, double height)
        {
            double margin = 25;
            double signatureWidth = 100;
            double signatureHeight = 30;
            
            // 会签栏位置（右上角）
            Point3d signatureInsert = new Point3d(
                insertPoint.X + width - margin - signatureWidth,
                insertPoint.Y + height - margin - signatureHeight, 0);

            // 绘制会签栏外框
            Polyline signatureFrame = new Polyline();
            signatureFrame.AddVertexAt(0, new Point2d(signatureInsert.X, signatureInsert.Y), 0, 0, 0);
            signatureFrame.AddVertexAt(1, new Point2d(signatureInsert.X + signatureWidth, signatureInsert.Y), 0, 0, 0);
            signatureFrame.AddVertexAt(2, new Point2d(signatureInsert.X + signatureWidth, signatureInsert.Y + signatureHeight), 0, 0, 0);
            signatureFrame.AddVertexAt(3, new Point2d(signatureInsert.X, signatureInsert.Y + signatureHeight), 0, 0, 0);
            signatureFrame.Closed = true;
            signatureFrame.Layer = "TITLEBAR";
            signatureFrame.LineWeight = LineWeight.LineWeight025;

            btr.AppendEntity(signatureFrame);
            trans.AddNewlyCreatedDBObject(signatureFrame, true);

            // 添加会签栏文字
            DBText signatureText = new DBText();
            signatureText.Position = new Point3d(signatureInsert.X + signatureWidth / 2, signatureInsert.Y + signatureHeight / 2, 0);
            signatureText.TextString = "会签栏";
            signatureText.Height = 4;
            signatureText.HorizontalMode = TextHorizontalMode.TextCenter;
            signatureText.VerticalMode = TextVerticalMode.TextVerticalMid;
            signatureText.AlignmentPoint = signatureText.Position;
            signatureText.Layer = "TEXT";
            btr.AppendEntity(signatureText);
            trans.AddNewlyCreatedDBObject(signatureText, true);
        }

        private class TitleBlockParameters
        {
            public string PaperSize { get; set; }
            public Point3d InsertPoint { get; set; }
            public string Title { get; set; }
            public string DrawingNumber { get; set; }
            public string Designer { get; set; }
            public string Date { get; set; }
        }

        [CommandMethod("BATCHCREATETITLEBLOCKS")]
        public void BatchCreateTitleBlocks()
        {
            Document doc = Application.DocumentManager.MdiActiveDocument;
            Database db = doc.Database;
            Editor ed = doc.Editor;

            try
            {
                // 获取批量参数
                var batchParams = GetBatchParameters(ed);
                if (batchParams == null)
                    return;

                // 批量创建图框
                int successCount = BatchGenerateTitleBlocks(db, batchParams);

                ed.WriteMessage($"\n批量创建完成！成功创建{successCount}个图框。");
            }
            catch (System.Exception ex)
            {
                ed.WriteMessage($"\n批量创建图框时出错：{ex.Message}");
            }
        }

        private BatchParameters GetBatchParameters(Editor ed)
        {
            // 选择图纸尺寸
            PromptKeywordOptions sizeOpt = new PromptKeywordOptions("\n选择图纸尺寸: ");
            sizeOpt.Keywords.Add("A3");
            sizeOpt.Keywords.Add("A4");
            sizeOpt.Keywords.Default = "A3";

            PromptResult sizeResult = ed.GetKeywords(sizeOpt);
            if (sizeResult.Status != PromptStatus.OK)
                return null;

            // 获取数量
            PromptIntegerOptions countOpt = new PromptIntegerOptions("\n输入创建数量: ");
            countOpt.DefaultValue = 5;
            countOpt.LowerLimit = 1;
            countOpt.UpperLimit = 100;

            PromptIntegerResult countResult = ed.GetInteger(countOpt);
            if (countResult.Status != PromptStatus.OK)
                return null;

            // 获取间距
            PromptDistanceOptions spacingOpt = new PromptDistanceOptions("\n输入图框间距: ");
            spacingOpt.DefaultValue = 500;
            spacingOpt.LowerLimit = 100;

            PromptDoubleResult spacingResult = ed.GetDistance(spacingOpt);
            if (spacingResult.Status != PromptStatus.OK)
                return null;

            return new BatchParameters
            {
                PaperSize = sizeResult.StringResult,
                Count = countResult.Value,
                Spacing = spacingResult.Value
            };
        }

        private int BatchGenerateTitleBlocks(Database db, BatchParameters batchParams)
        {
            int successCount = 0;
            var paperSize = PaperSizes[batchParams.PaperSize];
            double width = paperSize.Item1;
            double height = paperSize.Item2;

            using (Transaction trans = db.TransactionManager.StartTransaction())
            {
                // 创建图层
                CreateLayers(trans, db);

                // 获取模型空间
                BlockTable bt = trans.GetObject(db.BlockTableId, OpenMode.ForRead) as BlockTable;
                BlockTableRecord btr = trans.GetObject(bt[BlockTableRecord.ModelSpace], OpenMode.ForWrite) as BlockTableRecord;

                // 批量创建图框
                for (int i = 0; i < batchParams.Count; i++)
                {
                    try
                    {
                        Point3d insertPoint = new Point3d(
                            (i % 5) * (width + batchParams.Spacing),
                            (i / 5) * (height + batchParams.Spacing), 0);

                        var parameters = new TitleBlockParameters
                        {
                            PaperSize = batchParams.PaperSize,
                            InsertPoint = insertPoint,
                            Title = $"图纸{i + 1}",
                            DrawingNumber = $"A-{(i + 1).ToString("D3")}",
                            Designer = "设计师",
                            Date = DateTime.Now.ToString("yyyy-MM-dd")
                        };

                        // 绘制外边框
                        DrawOuterFrame(btr, trans, parameters.InsertPoint, width, height);

                        // 绘制内边框
                        DrawInnerFrame(btr, trans, parameters.InsertPoint, width, height);

                        // 绘制标题栏
                        DrawTitleBar(btr, trans, parameters.InsertPoint, width, height, parameters);

                        // 绘制会签栏
                        DrawSignatureBlock(btr, trans, parameters.InsertPoint, width, height);

                        successCount++;
                    }
                    catch
                    {
                        // 继续处理下一个
                    }
                }

                trans.Commit();
            }

            return successCount;
        }

        private class BatchParameters
        {
            public string PaperSize { get; set; }
            public int Count { get; set; }
            public double Spacing { get; set; }
        }
    }
}
```

## 4.1.2 批量图纸处理

**批量处理功能实现：**
```csharp
[CommandMethod("BATCHPROCESSDRAWINGS")]
public void BatchProcessDrawings()
{
    Document doc = Application.DocumentManager.MdiActiveDocument;
    Editor ed = doc.Editor;
    
    try
    {
        // 选择处理模式
        PromptKeywordOptions modeOpt = new PromptKeywordOptions("\n选择处理模式: ");
        modeOpt.Keywords.Add("LAYER");
        modeOpt.Keywords.Add("BLOCK");
        modeOpt.Keywords.Add("TEXT");
        modeOpt.Keywords.Add("DIMENSION");
        modeOpt.Keywords.Default = "LAYER";
        
        PromptResult modeResult = ed.GetKeywords(modeOpt);
        if (modeResult.Status != PromptStatus.OK)
            return;
            
        string mode = modeResult.StringResult;
        
        // 选择文件
        var filePaths = SelectDrawingFiles(ed);
        if (filePaths.Count == 0)
            return;
            
        // 处理文件
        ProcessFiles(filePaths, mode, ed);
        
        ed.WriteMessage($"\n批量处理完成！共处理{filePaths.Count}个文件。");
    }
    catch (System.Exception ex)
    {
        ed.WriteMessage($"\n批量处理时出错：{ex.Message}");
    }
}

private List<string> SelectDrawingFiles(Editor ed)
{
    var filePaths = new List<string>();
    
    // 使用OpenFileDialog选择文件
    using (var dialog = new System.Windows.Forms.OpenFileDialog())
    {
        dialog.Filter = "AutoCAD文件 (*.dwg)|*.dwg|所有文件 (*.*)|*.*";
        dialog.Multiselect = true;
        dialog.Title = "选择要处理的DWG文件";
        
        if (dialog.ShowDialog() == System.Windows.Forms.DialogResult.OK)
        {
            filePaths.AddRange(dialog.FileNames);
        }
    }
    
    return filePaths;
}

private void ProcessFiles(List<string> filePaths, string mode, Editor ed)
{
    int successCount = 0;
    int failCount = 0;
    
    foreach (string filePath in filePaths)
    {
        try
        {
            ed.WriteMessage($"\n正在处理文件：{Path.GetFileName(filePath)}");
            
            // 打开数据库
            using (var db = new Database(false, true))
            {
                db.ReadDwgFile(filePath, FileOpenMode.OpenForReadAndWriteNoShare, false, null);
                
                // 根据模式处理
                switch (mode)
                {
                    case "LAYER":
                        ProcessLayers(db);
                        break;
                    case "BLOCK":
                        ProcessBlocks(db);
                        break;
                    case "TEXT":
                        ProcessText(db);
                        break;
                    case "DIMENSION":
                        ProcessDimensions(db);
                        break;
                }
                
                // 保存文件
                db.SaveAs(filePath, DwgVersion.Current);
                
                successCount++;
                ed.WriteMessage(" - 成功");
            }
        }
        catch (System.Exception ex)
        {
            failCount++;
            ed.WriteMessage($" - 失败：{ex.Message}");
        }
    }
    
    ed.WriteMessage($"\n处理结果：成功{successCount}个，失败{failCount}个");
}

private void ProcessLayers(Database db)
{
    using (Transaction trans = db.TransactionManager.StartTransaction())
    {
        LayerTable lt = trans.GetObject(db.LayerTableId, OpenMode.ForWrite) as LayerTable;
        
        // 创建标准图层
        string[] standardLayers = { "WALLS", "DOORS", "WINDOWS", "TEXT", "DIMENSIONS", "TITLEBLOCK" };
        Color[] layerColors = { 
            Color.FromColorIndex(ColorMethod.ByColor, 1),  // 红色
            Color.FromColorIndex(ColorMethod.ByColor, 2),  // 黄色
            Color.FromColorIndex(ColorMethod.ByColor, 3),  // 绿色
            Color.FromColorIndex(ColorMethod.ByColor, 7),  // 白色
            Color.FromColorIndex(ColorMethod.ByColor, 4),  // 青色
            Color.FromColorIndex(ColorMethod.ByColor, 7)   // 白色
        };
        
        for (int i = 0; i < standardLayers.Length; i++)
        {
            if (!lt.Has(standardLayers[i]))
            {
                LayerTableRecord layer = new LayerTableRecord();
                layer.Name = standardLayers[i];
                layer.Color = layerColors[i];
                layer.LineWeight = LineWeight.LineWeight025;
                
                lt.Add(layer);
                trans.AddNewlyCreatedDBObject(layer, true);
            }
        }
        
        trans.Commit();
    }
}

private void ProcessBlocks(Database db)
{
    using (Transaction trans = db.TransactionManager.StartTransaction())
    {
        BlockTable bt = trans.GetObject(db.BlockTableId, OpenMode.ForRead) as BlockTable;
        
        // 处理模型空间
        BlockTableRecord modelSpace = trans.GetObject(bt[BlockTableRecord.ModelSpace], OpenMode.ForWrite) as BlockTableRecord;
        
        // 清理未使用的块
        foreach (ObjectId blockId in bt)
        {
            BlockTableRecord block = trans.GetObject(blockId, OpenMode.ForRead) as BlockTableRecord;
            
            if (block.Name.StartsWith("*") || block.IsLayout || block.IsAnonymous)
                continue;
                
            // 检查块是否被使用
            if (!IsBlockUsed(db, block.Name))
            {
                // 标记为可删除
                block.UpgradeOpen();
                block.Erase();
            }
        }
        
        trans.Commit();
    }
}

private bool IsBlockUsed(Database db, string blockName)
{
    using (Transaction trans = db.TransactionManager.StartTransaction())
    {
        BlockTable bt = trans.GetObject(db.BlockTableId, OpenMode.ForRead) as BlockTable;
        BlockTableRecord modelSpace = trans.GetObject(bt[BlockTableRecord.ModelSpace], OpenMode.ForRead) as BlockTableRecord;
        
        foreach (ObjectId objId in modelSpace)
        {
            Entity ent = trans.GetObject(objId, OpenMode.ForRead) as Entity;
            if (ent is BlockReference blockRef)
            {
                BlockTableRecord block = trans.GetObject(blockRef.BlockTableRecord, OpenMode.ForRead) as BlockTableRecord;
                if (block.Name == blockName)
                    return true;
            }
        }
        
        return false;
    }
}

private void ProcessText(Database db)
{
    using (Transaction trans = db.TransactionManager.StartTransaction())
    {
        BlockTable bt = trans.GetObject(db.BlockTableId, OpenMode.ForRead) as BlockTable;
        BlockTableRecord modelSpace = trans.GetObject(bt[BlockTableRecord.ModelSpace], OpenMode.ForWrite) as BlockTableRecord;
        
        // 处理文字样式
        TextStyleTable tst = trans.GetObject(db.TextStyleTableId, OpenMode.ForWrite) as TextStyleTable;
        
        if (!tst.Has("Standard"))
        {
            TextStyleTableRecord textStyle = new TextStyleTableRecord();
            textStyle.Name = "Standard";
            textStyle.FileName = "simplex.shx";
            textStyle.TextSize = 3.5;
            
            tst.Add(textStyle);
            trans.AddNewlyCreatedDBObject(textStyle, true);
        }
        
        // 统一文字高度
        foreach (ObjectId objId in modelSpace)
        {
            Entity ent = trans.GetObject(objId, OpenMode.ForWrite) as Entity;
            
            if (ent is DBText text)
            {
                if (text.Height <= 0)
                    text.Height = 3.5;
                    
                if (string.IsNullOrEmpty(text.TextStyle))
                    text.TextStyle = "Standard";
            }
            else if (ent is MText mtext)
            {
                if (mtext.Height <= 0)
                    mtext.Height = 3.5;
                    
                if (string.IsNullOrEmpty(mtext.TextStyle))
                    mtext.TextStyle = "Standard";
            }
        }
        
        trans.Commit();
    }
}

private void ProcessDimensions(Database db)
{
    using (Transaction trans = db.TransactionManager.StartTransaction())
    {
        // 处理标注样式
        DimStyleTable dst = trans.GetObject(db.DimStyleTableId, OpenMode.ForWrite) as DimStyleTable;
        
        if (!dst.Has("Standard"))
        {
            DimStyleTableRecord dimStyle = new DimStyleTableRecord();
            dimStyle.Name = "Standard";
            dimStyle.DimScale = 1.0;
            dimStyle.Dimtxt = 3.5;
            dimStyle.Dimasz = 2.5;
            dimStyle.Dimexe = 3.0;
            dimStyle.Dimexo = 1.0;
            dimStyle.Dimgap = 1.0;
            
            dst.Add(dimStyle);
            trans.AddNewlyCreatedDBObject(dimStyle, true);
            
            db.Dimstyle = dst["Standard"];
        }
        
        trans.Commit();
    }
}
```

---

**CURSOR提示词模板：**
```
请为建筑图纸自动化生成完整的解决方案，包括：
1. 标准图框生成功能
2. 批量图纸处理功能
3. 参数化设计功能
4. 自动化标注功能
5. 符合建筑行业规范
```