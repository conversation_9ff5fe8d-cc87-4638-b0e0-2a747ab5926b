import Joi from 'joi';
export declare const rpaEngineSchema: Joi.ObjectSchema<any>;
export declare const connectionConfigSchema: Joi.ObjectSchema<any>;
export declare const executionConfigSchema: Joi.ObjectSchema<any>;
export declare const validate: (schema: Jo<PERSON>.Schema, data: any) => any;
export declare const validateRPAEngine: (data: any) => any;
export declare const validateConnectionConfig: (data: any) => any;
export declare const validateExecutionConfig: (data: any) => any;
//# sourceMappingURL=validation.d.ts.map