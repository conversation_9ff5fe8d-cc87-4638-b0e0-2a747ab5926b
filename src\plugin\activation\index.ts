import { PluginLifecycleManager } from './lifecycle-manager';
import { CursorIntegration, createMockCursorAPI } from './cursor-integration';
import { PluginContext } from '../../types';
import { createLogger } from '../../utils/logger';

export class PluginActivator {
  private lifecycleManager: PluginLifecycleManager;
  private cursorIntegration: CursorIntegration;
  private logger = createLogger('PluginActivator');

  constructor() {
    this.lifecycleManager = new PluginLifecycleManager();
    
    // For development, use mock API
    // In production, this would be the real Cursor API
    const cursorAPI = createMockCursorAPI();
    this.cursorIntegration = new CursorIntegration(cursorAPI);
  }

  async activate(context?: Partial<PluginContext>): Promise<void> {
    try {
      this.logger.info('Starting plugin activation...');
      
      // Activate the plugin lifecycle
      await this.lifecycleManager.activate(context);
      
      // Initialize Cursor integration
      await this.cursorIntegration.initialize();
      
      this.logger.info('Plugin activated successfully');
      
    } catch (error) {
      this.logger.error('Plugin activation failed:', error);
      throw error;
    }
  }

  async deactivate(): Promise<void> {
    try {
      this.logger.info('Starting plugin deactivation...');
      
      // Clean up Cursor integration
      await this.cursorIntegration.dispose();
      
      // Deactivate the plugin lifecycle
      await this.lifecycleManager.deactivate();
      
      this.logger.info('Plugin deactivated successfully');
      
    } catch (error) {
      this.logger.error('Plugin deactivation failed:', error);
      throw error;
    }
  }

  getLifecycleManager(): PluginLifecycleManager {
    return this.lifecycleManager;
  }

  getCursorIntegration(): CursorIntegration {
    return this.cursorIntegration;
  }

  getState() {
    return this.lifecycleManager.getState();
  }

  isActive(): boolean {
    return this.lifecycleManager.isActive();
  }
}

// Export singleton instance
export const pluginActivator = new PluginActivator();

// Export for testing
export { PluginLifecycleManager, CursorIntegration };