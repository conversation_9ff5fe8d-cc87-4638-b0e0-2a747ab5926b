# AutoCAD编程接口介绍

## 1.3.1 AutoLISP基础

**AutoLISP简介：**
AutoLISP是AutoCAD的内置编程语言，基于LISP语言，专门用于AutoCAD自动化。它是学习AutoCAD自动化的最佳起点。

**基本语法：**
```lisp
; 定义函数
(defun functionName (parameters)
  ; 函数体
  (command "commandName" parameters)
  (princ) ; 清空命令行
)

; 调用函数
(functionName arg1 arg2)
```

**常用函数：**
- `(command "LINE" p1 p2 "")` - 绘制直线
- `(entget (entlast))` - 获取最后一个实体
- `(setq variable value)` - 设置变量
- `(list x y z)` - 创建坐标列表

## 1.3.2 VBA编程环境

**VBA编辑器访问：**
```vba
' 在AutoCAD中按ALT+F11打开VBA编辑器
' 或者输入命令：VBAIDE
```

**基本结构：**
```vba
Sub DrawLine()
    Dim acadApp As AcadApplication
    Dim acadDoc As AcadDocument
    Dim startPoint(0 To 2) As Double
    Dim endPoint(0 To 2) As Double
    
    Set acadApp = GetObject(, "AutoCAD.Application")
    Set acadDoc = acadApp.ActiveDocument
    
    startPoint(0) = 0: startPoint(1) = 0: startPoint(2) = 0
    endPoint(0) = 100: endPoint(1) = 100: endPoint(2) = 0
    
    acadDoc.ModelSpace.AddLine startPoint, endPoint
    acadDoc.Regen True
End Sub
```

## 1.3.3 .NET API架构

**核心组件：**
- `AcadApplication` - AutoCAD应用程序对象
- `AcadDocument` - 文档对象
- `ModelSpace` - 模型空间集合
- `PaperSpace` - 图纸空间集合

**C#基础结构：**
```csharp
using Autodesk.AutoCAD.Runtime;
using Autodesk.AutoCAD.ApplicationServices;
using Autodesk.AutoCAD.DatabaseServices;
using Autodesk.AutoCAD.Geometry;

[CommandMethod("DrawLine")]
public void DrawLine()
{
    Document doc = Application.DocumentManager.MdiActiveDocument;
    Database db = doc.Database;
    
    using (Transaction trans = db.TransactionManager.StartTransaction())
    {
        BlockTable bt = trans.GetObject(db.BlockTableId, OpenMode.ForRead) as BlockTable;
        BlockTableRecord btr = trans.GetObject(bt[BlockTableRecord.ModelSpace], OpenMode.ForWrite) as BlockTableRecord;
        
        Line line = new Line(new Point3d(0, 0, 0), new Point3d(100, 100, 0));
        btr.AppendEntity(line);
        trans.AddNewlyCreatedDBObject(line, true);
        
        trans.Commit();
    }
}
```

## 1.3.4 ObjectARX概述

**ObjectARX简介：**
ObjectARX是AutoCAD的C++开发环境，提供了最强大的功能和最佳的性能。

**主要特性：**
- 原生C++性能
- 深度AutoCAD集成
- 自定义实体和对象
- 复杂几何运算

**开发环境：**
- Visual Studio C++
- ObjectARX SDK
- Windows SDK

---

**CURSOR提示词模板：**
```
请解释AutoCAD的[编程接口名称]，并提供：
1. 基本概念和架构
2. 常用对象和方法
3. 代码示例
4. 适用场景和限制
```