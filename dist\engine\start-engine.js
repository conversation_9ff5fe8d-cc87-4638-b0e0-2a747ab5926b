"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.startEngine = startEngine;
exports.shutdownEngine = shutdownEngine;
const index_1 = require("./index");
const logger_1 = require("../utils/logger");
const logger = (0, logger_1.createLogger)('EngineStarter');
async function startEngine() {
    try {
        logger.info('Starting Yingdao RPA Engine service...');
        await index_1.engineServiceManager.start();
        logger.info(`Engine service started successfully on port ${index_1.engineServiceManager.getPort()}`);
        logger.info('Engine is ready to accept connections');
        // Handle graceful shutdown
        process.on('SIGINT', async () => {
            logger.info('Received SIGINT, shutting down engine...');
            await shutdownEngine();
            process.exit(0);
        });
        process.on('SIGTERM', async () => {
            logger.info('Received SIGTERM, shutting down engine...');
            await shutdownEngine();
            process.exit(0);
        });
        // Keep the process running
        logger.info('Engine service is running. Press Ctrl+C to stop.');
    }
    catch (error) {
        logger.error('Failed to start engine service:', error);
        process.exit(1);
    }
}
async function shutdownEngine() {
    try {
        await index_1.engineServiceManager.stop();
        logger.info('Engine service shutdown completed');
    }
    catch (error) {
        logger.error('Error during engine shutdown:', error);
    }
}
// Start the engine service
startEngine().catch(error => {
    logger.error('Unhandled error:', error);
    process.exit(1);
});
//# sourceMappingURL=start-engine.js.map