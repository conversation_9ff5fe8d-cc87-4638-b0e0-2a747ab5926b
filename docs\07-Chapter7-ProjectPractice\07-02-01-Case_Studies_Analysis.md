# 案例分析与最佳实践

## 7.2.1 实际项目案例分析

### 案例一：大型住宅小区设计自动化

**项目背景：**
某大型住宅小区项目，包含50栋建筑，总建筑面积约20万平方米。传统设计方法需要6个月完成，采用自动化方案后缩短至3个月。

**技术方案：**
```csharp
using Autodesk.AutoCAD.Runtime;
using Autodesk.AutoCAD.ApplicationServices;
using Autodesk.AutoCAD.DatabaseServices;
using Autodesk.AutoCAD.Geometry;
using Autodesk.AutoCAD.EditorInput;
using System;
using System.Collections.Generic;
using System.IO;

namespace AutoCAD.CaseStudies.ResidentialComplex
{
    // 住宅小区自动化设计系统
    public class ResidentialComplexAutomation
    {
        [CommandMethod("CREATERESIDENTIALCOMPLEX")]
        public void CreateResidentialComplex()
        {
            Document doc = Application.DocumentManager.MdiActiveDocument;
            Database db = doc.Database;
            Editor ed = doc.Editor;
            
            try
            {
                // 获取项目参数
                var projectParams = GetResidentialProjectParameters(ed);
                if (projectParams == null)
                    return;
                
                // 创建项目
                var project = new ResidentialComplexProject(projectParams);
                
                // 生成总平面图
                GenerateSitePlan(db, project);
                
                // 生成建筑单体
                GenerateBuildings(db, project);
                
                // 生成道路系统
                GenerateRoadSystem(db, project);
                
                // 生成景观绿化
                GenerateLandscape(db, project);
                
                // 生成管网系统
                GenerateUtilityNetwork(db, project);
                
                ed.WriteMessage("\n住宅小区设计完成！");
                ed.WriteMessage($"\n建筑数量：{project.Buildings.Count}栋");
                ed.WriteMessage($"\n总建筑面积：{project.TotalArea:F1}平方米");
            }
            catch (System.Exception ex)
            {
                ed.WriteMessage($"\n创建住宅小区失败：{ex.Message}");
            }
        }
        
        private ResidentialProjectParameters GetResidentialProjectParameters(Editor ed)
        {
            var parameters = new ResidentialProjectParameters();
            
            // 获取项目基本信息
            PromptStringOptions nameOpt = new PromptStringOptions("\n输入项目名称: ");
            nameOpt.DefaultValue = "阳光住宅小区";
            PromptResult nameResult = ed.GetString(nameOpt);
            parameters.ProjectName = nameResult.StringResult;
            
            // 获取项目规模
            PromptIntegerOptions buildingOpt = new PromptIntegerOptions("\n输入建筑数量: ");
            buildingOpt.DefaultValue = 50;
            buildingOpt.LowerLimit = 1;
            buildingOpt.UpperLimit = 200;
            PromptIntegerResult buildingResult = ed.GetInteger(buildingOpt);
            parameters.BuildingCount = buildingResult.Value;
            
            // 获取用地面积
            PromptDoubleOptions areaOpt = new PromptDoubleOptions("\n输入用地面积(平方米): ");
            areaOpt.DefaultValue = 100000;
            areaOpt.LowerLimit = 10000;
            PromptDoubleResult areaResult = ed.GetDouble(areaOpt);
            parameters.SiteArea = areaResult.Value;
            
            // 获取建筑类型
            PromptKeywordOptions typeOpt = new PromptKeywordOptions("\n选择建筑类型: ");
            typeOpt.Keywords.Add("HIGHRISE");
            typeOpt.Keywords.Add("MIDRISE");
            typeOpt.Keywords.Add("LOWRISE");
            typeOpt.Keywords.Add("MIXED");
            typeOpt.Default = "MIXED";
            PromptResult typeResult = ed.GetKeywords(typeOpt);
            parameters.BuildingType = typeResult.StringResult;
            
            // 获取项目边界
            PromptPointOptions cornerOpt = new PromptPointOptions("\n选择项目区域第一角点: ");
            PromptPointResult cornerResult = ed.GetPoint(cornerOpt);
            if (cornerResult.Status != PromptStatus.OK)
                return null;
            
            PromptCornerOptions oppositeOpt = new PromptCornerOptions("\n选择项目区域对角点: ");
            oppositeOpt.UseBasePoint = true;
            oppositeOpt.BasePoint = cornerResult.Value;
            PromptPointResult oppositeResult = ed.GetPoint(oppositeOpt);
            if (oppositeResult.Status != PromptStatus.OK)
                return null;
            
            parameters.SiteBoundary = new Rectangle3d(cornerResult.Value, oppositeResult.Value);
            
            return parameters;
        }
        
        private void GenerateSitePlan(Database db, ResidentialComplexProject project)
        {
            using (Transaction trans = db.TransactionManager.StartTransaction())
            {
                BlockTable bt = trans.GetObject(db.BlockTableId, OpenMode.ForRead) as BlockTable;
                BlockTableRecord modelSpace = trans.GetObject(bt[BlockTableRecord.ModelSpace], OpenMode.ForWrite) as BlockTableRecord;
                
                // 绘制用地边界
                DrawSiteBoundary(modelSpace, trans, project.Parameters.SiteBoundary);
                
                // 绘制道路红线
                DrawRoadLines(modelSpace, trans, project);
                
                // 绘制建筑控制线
                SetbackLines setbackLines = CalculateSetbackLines(project.Parameters.SiteBoundary);
                DrawSetbackLines(modelSpace, trans, setbackLines);
                
                trans.Commit();
            }
        }
        
        private void GenerateBuildings(Database db, ResidentialComplexProject project)
        {
            var buildingLayout = OptimizeBuildingLayout(project);
            
            using (Transaction trans = db.TransactionManager.StartTransaction())
            {
                BlockTable bt = trans.GetObject(db.BlockTableId, OpenMode.ForRead) as BlockTable;
                BlockTableRecord modelSpace = trans.GetObject(bt[BlockTableRecord.ModelSpace], OpenMode.ForWrite) as BlockTableRecord;
                
                foreach (var buildingPos in buildingLayout.BuildingPositions)
                {
                    CreateBuilding(modelSpace, trans, buildingPos, project.Parameters.BuildingType);
                }
                
                trans.Commit();
            }
        }
        
        private void GenerateRoadSystem(Database db, ResidentialComplexProject project)
        {
            var roadSystem = DesignRoadSystem(project);
            
            using (Transaction trans = db.TransactionManager.StartTransaction())
            {
                BlockTable bt = trans.GetObject(db.BlockTableId, OpenMode.ForRead) as BlockTable;
                BlockTableRecord modelSpace = trans.GetObject(bt[BlockTableRecord.ModelSpace], OpenMode.ForWrite) as BlockTableRecord;
                
                // 绘制主干道
                foreach (var road in roadSystem.MainRoads)
                {
                    DrawRoad(modelSpace, trans, road, RoadType.Main);
                }
                
                // 绘制次要道路
                foreach (var road in roadSystem.SecondaryRoads)
                {
                    DrawRoad(modelSpace, trans, road, RoadType.Secondary);
                }
                
                // 绘制小区道路
                foreach (var road in roadSystem.InternalRoads)
                {
                    DrawRoad(modelSpace, trans, road, RoadType.Internal);
                }
                
                trans.Commit();
            }
        }
        
        private void GenerateLandscape(Database db, ResidentialComplexProject project)
        {
            var landscapeDesign = DesignLandscape(project);
            
            using (Transaction trans = db.TransactionManager.StartTransaction())
            {
                BlockTable bt = trans.GetObject(db.BlockTableId, OpenMode.ForRead) as BlockTable;
                BlockTableRecord modelSpace = trans.GetObject(bt[BlockTableRecord.ModelSpace], OpenMode.ForWrite) as BlockTableRecord;
                
                // 绘制绿化区域
                foreach (var greenArea in landscapeDesign.GreenAreas)
                {
                    DrawGreenArea(modelSpace, trans, greenArea);
                }
                
                // 绘制景观小品
                foreach (var landscapeFeature in landscapeDesign.Features)
                {
                    DrawLandscapeFeature(modelSpace, trans, landscapeFeature);
                }
                
                trans.Commit();
            }
        }
        
        private void GenerateUtilityNetwork(Database db, ResidentialComplexProject project)
        {
            var utilityNetwork = DesignUtilityNetwork(project);
            
            using (Transaction trans = db.TransactionManager.StartTransaction())
            {
                BlockTable bt = trans.GetObject(db.BlockTableId, OpenMode.ForRead) as BlockTable;
                BlockTableRecord modelSpace = trans.GetObject(bt[BlockTableRecord.ModelSpace], OpenMode.ForWrite) as BlockTableRecord;
                
                // 绘制给水管网
                foreach (var pipe in utilityNetwork.WaterPipes)
                {
                    DrawUtilityPipe(modelSpace, trans, pipe, UtilityType.Water);
                }
                
                // 绘制排水管网
                foreach (var pipe in utilityNetwork.DrainagePipes)
                {
                    DrawUtilityPipe(modelSpace, trans, pipe, UtilityType.Drainage);
                }
                
                // 绘制电力线路
                foreach (var cable in utilityNetwork.PowerCables)
                {
                    DrawUtilityCable(modelSpace, trans, cable, UtilityType.Power);
                }
                
                trans.Commit();
            }
        }
        
        #region 辅助方法
        
        private void DrawSiteBoundary(BlockTableRecord modelSpace, Transaction trans, Rectangle3d boundary)
        {
            Polyline boundaryLine = new Polyline();
            boundaryLine.AddVertexAt(0, new Point2d(boundary.MinPoint.X, boundary.MinPoint.Y), 0, 0, 0);
            boundaryLine.AddVertexAt(1, new Point2d(boundary.MaxPoint.X, boundary.MinPoint.Y), 0, 0, 0);
            boundaryLine.AddVertexAt(2, new Point2d(boundary.MaxPoint.X, boundary.MaxPoint.Y), 0, 0, 0);
            boundaryLine.AddVertexAt(3, new Point2d(boundary.MinPoint.X, boundary.MaxPoint.Y), 0, 0, 0);
            boundaryLine.Closed = true;
            boundaryLine.Layer = "SITE_BOUNDARY";
            boundaryLine.LineWeight = LineWeight.LineWeight070;
            
            modelSpace.AppendEntity(boundaryLine);
            trans.AddNewlyCreatedDBObject(boundaryLine, true);
        }
        
        private void DrawRoadLines(BlockTableRecord modelSpace, Transaction trans, ResidentialComplexProject project)
        {
            // 绘制道路红线的实现
            // 这里需要根据项目规划要求绘制道路红线
        }
        
        private SetbackLines CalculateSetbackLines(Rectangle3d siteBoundary)
        {
            // 计算建筑退线
            double frontSetback = 20.0;  // 前退线20米
            double sideSetback = 10.0;   // 侧退线10米
            double rearSetback = 15.0;   // 后退线15米
            
            return new SetbackLines
            {
                FrontSetback = frontSetback,
                SideSetback = sideSetback,
                RearSetback = rearSetback,
                InnerBoundary = CalculateInnerBoundary(siteBoundary, frontSetback, sideSetback, rearSetback)
            };
        }
        
        private Rectangle3d CalculateInnerBoundary(Rectangle3d outerBoundary, double front, double side, double rear)
        {
            Point3d minPoint = new Point3d(
                outerBoundary.MinPoint.X + side,
                outerBoundary.MinPoint.Y + front,
                0);
            
            Point3d maxPoint = new Point3d(
                outerBoundary.MaxPoint.X - side,
                outerBoundary.MaxPoint.Y - rear,
                0);
            
            return new Rectangle3d(minPoint, maxPoint);
        }
        
        private void DrawSetbackLines(BlockTableRecord modelSpace, Transaction trans, SetbackLines setbackLines)
        {
            // 绘制建筑退线
            Polyline setbackLine = new Polyline();
            setbackLine.AddVertexAt(0, new Point2d(setbackLines.InnerBoundary.MinPoint.X, setbackLines.InnerBoundary.MinPoint.Y), 0, 0, 0);
            setbackLine.AddVertexAt(1, new Point2d(setbackLines.InnerBoundary.MaxPoint.X, setbackLines.InnerBoundary.MinPoint.Y), 0, 0, 0);
            setbackLine.AddVertexAt(2, new Point2d(setbackLines.InnerBoundary.MaxPoint.X, setbackLines.InnerBoundary.MaxPoint.Y), 0, 0, 0);
            setbackLine.AddVertexAt(3, new Point2d(setbackLines.InnerBoundary.MinPoint.X, setbackLines.InnerBoundary.MaxPoint.Y), 0, 0, 0);
            setbackLine.Closed = true;
            setbackLine.Layer = "SETBACK_LINE";
            setbackLine.LineWeight = LineWeight.LineWeight025;
            setbackLine.Linetype = "DASHED";
            
            modelSpace.AppendEntity(setbackLine);
            trans.AddNewlyCreatedDBObject(setbackLine, true);
        }
        
        private BuildingLayout OptimizeBuildingLayout(ResidentialComplexProject project)
        {
            // 优化建筑布局
            var layout = new BuildingLayout();
            
            // 计算建筑间距和排列
            double minSpacing = 30.0;  // 最小间距30米
            double buildingWidth = 20.0; // 建筑宽度20米
            double buildingDepth = 15.0; // 建筑深度15米
            
            // 简化的网格布局算法
            var availableArea = CalculateAvailableArea(project.Parameters.SiteBoundary);
            var grid = CalculateBuildingGrid(availableArea, buildingWidth, buildingDepth, minSpacing);
            
            layout.BuildingPositions = grid;
            layout.TotalBuildings = grid.Count;
            
            return layout;
        }
        
        private Rectangle3d CalculateAvailableArea(Rectangle3d siteBoundary)
        {
            // 计算可用建筑面积（扣除道路、绿化等）
            double reductionFactor = 0.7;  // 70%可用面积
            
            Point3d center = siteBoundary.Center;
            double width = siteBoundary.Width * reductionFactor;
            double height = siteBoundary.Height * reductionFactor;
            
            Point3d minPoint = new Point3d(center.X - width/2, center.Y - height/2, 0);
            Point3d maxPoint = new Point3d(center.X + width/2, center.Y + height/2, 0);
            
            return new Rectangle3d(minPoint, maxPoint);
        }
        
        private List<Point3d> CalculateBuildingGrid(Rectangle3d availableArea, double buildingWidth, double buildingDepth, double spacing)
        {
            var positions = new List<Point3d>();
            
            double totalWidth = buildingWidth + spacing;
            double totalDepth = buildingDepth + spacing;
            
            int cols = (int)(availableArea.Width / totalWidth);
            int rows = (int)(availableArea.Height / totalDepth);
            
            Point3d startPoint = new Point3d(
                availableArea.MinPoint.X + spacing/2,
                availableArea.MinPoint.Y + spacing/2,
                0);
            
            for (int row = 0; row < rows; row++)
            {
                for (int col = 0; col < cols; col++)
                {
                    Point3d position = new Point3d(
                        startPoint.X + col * totalWidth,
                        startPoint.Y + row * totalDepth,
                        0);
                    
                    positions.Add(position);
                }
            }
            
            return positions;
        }
        
        private void CreateBuilding(BlockTableRecord modelSpace, Transaction trans, Point3d position, string buildingType)
        {
            // 根据建筑类型创建建筑
            BuildingTemplate template = GetBuildingTemplate(buildingType);
            
            // 创建建筑轮廓
            Polyline buildingOutline = new Polyline();
            buildingOutline.AddVertexAt(0, new Point2d(position.X, position.Y), 0, 0, 0);
            buildingOutline.AddVertexAt(1, new Point2d(position.X + template.Width, position.Y), 0, 0, 0);
            buildingOutline.AddVertexAt(2, new Point2d(position.X + template.Width, position.Y + template.Depth), 0, 0, 0);
            buildingOutline.AddVertexAt(3, new Point2d(position.X, position.Y + template.Depth), 0, 0, 0);
            buildingOutline.Closed = true;
            buildingOutline.Layer = "BUILDING";
            buildingOutline.LineWeight = LineWeight.LineWeight050;
            
            modelSpace.AppendEntity(buildingOutline);
            trans.AddNewlyCreatedDBObject(buildingOutline, true);
            
            // 添加建筑标签
            DBText buildingLabel = new DBText();
            buildingLabel.Position = new Point3d(position.X + template.Width/2, position.Y + template.Depth/2, 0);
            buildingLabel.TextString = template.Label;
            buildingLabel.Height = 2.0;
            buildingLabel.HorizontalMode = TextHorizontalMode.TextCenter;
            buildingLabel.VerticalMode = TextVerticalMode.TextVerticalMid;
            buildingLabel.AlignmentPoint = buildingLabel.Position;
            buildingLabel.Layer = "BUILDING_TEXT";
            
            modelSpace.AppendEntity(buildingLabel);
            trans.AddNewlyCreatedDBObject(buildingLabel, true);
        }
        
        private BuildingTemplate GetBuildingTemplate(string buildingType)
        {
            switch (buildingType)
            {
                case "HIGHRISE":
                    return new BuildingTemplate { Width = 25.0, Depth = 20.0, Label = "高层", Floors = 30 };
                case "MIDRISE":
                    return new BuildingTemplate { Width = 20.0, Depth = 15.0, Label = "多层", Floors = 12 };
                case "LOWRISE":
                    return new BuildingTemplate { Width = 15.0, Depth = 12.0, Label = "低层", Floors = 6 };
                default:
                    return new BuildingTemplate { Width = 20.0, Depth = 15.0, Label = "住宅", Floors = 12 };
            }
        }
        
        private RoadSystem DesignRoadSystem(ResidentialComplexProject project)
        {
            var roadSystem = new RoadSystem();
            
            // 简化的道路系统设计
            var boundary = project.Parameters.SiteBoundary;
            
            // 主干道（环绕项目）
            roadSystem.MainRoads.Add(new RoadSegment
            {
                StartPoint = boundary.MinPoint,
                EndPoint = new Point3d(boundary.MaxPoint.X, boundary.MinPoint.Y),
                Width = 12.0
            });
            
            roadSystem.MainRoads.Add(new RoadSegment
            {
                StartPoint = new Point3d(boundary.MaxPoint.X, boundary.MinPoint.Y),
                EndPoint = boundary.MaxPoint,
                Width = 12.0
            });
            
            roadSystem.MainRoads.Add(new RoadSegment
            {
                StartPoint = boundary.MaxPoint,
                EndPoint = new Point3d(boundary.MinPoint.X, boundary.MaxPoint.Y),
                Width = 12.0
            });
            
            roadSystem.MainRoads.Add(new RoadSegment
            {
                StartPoint = new Point3d(boundary.MinPoint.X, boundary.MaxPoint.Y),
                EndPoint = boundary.MinPoint,
                Width = 12.0
            });
            
            return roadSystem;
        }
        
        private void DrawRoad(BlockTableRecord modelSpace, Transaction trans, RoadSegment road, RoadType roadType)
        {
            // 绘制道路中心线
            Line centerLine = new Line(road.StartPoint, road.EndPoint);
            centerLine.Layer = "ROAD_CENTER";
            centerLine.LineWeight = LineWeight.LineWeight025;
            
            modelSpace.AppendEntity(centerLine);
            trans.AddNewlyCreatedDBObject(centerLine, true);
            
            // 绘制道路边界
            Vector3d direction = road.EndPoint - road.StartPoint;
            Vector3d perpendicular = Vector3d.ZAxis.CrossProduct(direction).GetNormal();
            
            Point3d offset1 = road.StartPoint + perpendicular * (road.Width / 2);
            Point3d offset2 = road.StartPoint - perpendicular * (road.Width / 2);
            Point3d offset3 = road.EndPoint - perpendicular * (road.Width / 2);
            Point3d offset4 = road.EndPoint + perpendicular * (road.Width / 2);
            
            Polyline roadBoundary = new Polyline();
            roadBoundary.AddVertexAt(0, new Point2d(offset1.X, offset1.Y), 0, 0, 0);
            roadBoundary.AddVertexAt(1, new Point2d(offset2.X, offset2.Y), 0, 0, 0);
            roadBoundary.AddVertexAt(2, new Point2d(offset3.X, offset3.Y), 0, 0, 0);
            roadBoundary.AddVertexAt(3, new Point2d(offset4.X, offset4.Y), 0, 0, 0);
            roadBoundary.Closed = true;
            roadBoundary.Layer = "ROAD";
            roadBoundary.LineWeight = LineWeight.LineWeight030;
            
            modelSpace.AppendEntity(roadBoundary);
            trans.AddNewlyCreatedDBObject(roadBoundary, true);
        }
        
        private LandscapeDesign DesignLandscape(ResidentialComplexProject project)
        {
            var design = new LandscapeDesign();
            
            // 简化的景观设计
            var boundary = project.Parameters.SiteBoundary;
            
            // 中央绿地
            Point3d center = boundary.Center;
            double greenAreaSize = Math.Min(boundary.Width, boundary.Height) * 0.3;
            
            design.GreenAreas.Add(new GreenArea
            {
                Center = center,
                Size = greenAreaSize,
                Type = GreenAreaType.CentralPark
            });
            
            return design;
        }
        
        private void DrawGreenArea(BlockTableRecord modelSpace, Transaction trans, GreenArea greenArea)
        {
            // 绘制圆形绿地
            Circle greenCircle = new Circle(greenArea.Center, Vector3d.ZAxis, greenArea.Size / 2);
            greenCircle.Layer = "GREEN_AREA";
            greenCircle.LineWeight = LineWeight.LineWeight025;
            
            modelSpace.AppendEntity(greenCircle);
            trans.AddNewlyCreatedDBObject(greenCircle, true);
        }
        
        private void DrawLandscapeFeature(BlockTableRecord modelSpace, Transaction trans, LandscapeFeature feature)
        {
            // 绘制景观小品
            Circle featureCircle = new Circle(feature.Position, Vector3d.ZAxis, feature.Radius);
            featureCircle.Layer = "LANDSCAPE_FEATURE";
            featureCircle.LineWeight = LineWeight.LineWeight025;
            
            modelSpace.AppendEntity(featureCircle);
            trans.AddNewlyCreatedDBObject(featureCircle, true);
        }
        
        private UtilityNetwork DesignUtilityNetwork(ResidentialComplexProject project)
        {
            return new UtilityNetwork
            {
                WaterPipes = new List<PipeSegment>(),
                DrainagePipes = new List<PipeSegment>(),
                PowerCables = new List<CableSegment>()
            };
        }
        
        private void DrawUtilityPipe(BlockTableRecord modelSpace, Transaction trans, PipeSegment pipe, UtilityType utilityType)
        {
            Line pipeLine = new Line(pipe.StartPoint, pipe.EndPoint);
            pipeLine.Layer = $"UTILITY_{utilityType}";
            pipeLine.LineWeight = LineWeight.LineWeight020;
            pipeLine.Linetype = "DASHED";
            
            modelSpace.AppendEntity(pipeLine);
            trans.AddNewlyCreatedDBObject(pipeLine, true);
        }
        
        private void DrawUtilityCable(BlockTableRecord modelSpace, Transaction trans, CableSegment cable, UtilityType utilityType)
        {
            Line cableLine = new Line(cable.StartPoint, cable.EndPoint);
            cableLine.Layer = $"UTILITY_{utilityType}";
            cableLine.LineWeight = LineWeight.LineWeight015;
            cableLine.Linetype = "DOT";
            
            modelSpace.AppendEntity(cableLine);
            trans.AddNewlyCreatedDBObject(cableLine, true);
        }
        
        #endregion
    }
    
    // 项目数据结构
    public class ResidentialProjectParameters
    {
        public string ProjectName { get; set; }
        public int BuildingCount { get; set; }
        public double SiteArea { get; set; }
        public string BuildingType { get; set; }
        public Rectangle3d SiteBoundary { get; set; }
    }
    
    public class ResidentialComplexProject
    {
        public ResidentialProjectParameters Parameters { get; set; }
        public List<Building> Buildings { get; set; } = new List<Building>();
        public double TotalArea { get; set; }
    }
    
    public class Building
    {
        public string Name { get; set; }
        public Point3d Position { get; set; }
        public double Width { get; set; }
        public double Depth { get; set; }
        public int Floors { get; set; }
        public double FloorArea { get; set; }
    }
    
    public class Rectangle3d
    {
        public Point3d MinPoint { get; set; }
        public Point3d MaxPoint { get; set; }
        
        public Rectangle3d(Point3d minPoint, Point3d maxPoint)
        {
            MinPoint = minPoint;
            MaxPoint = maxPoint;
        }
        
        public double Width => Math.Abs(MaxPoint.X - MinPoint.X);
        public double Height => Math.Abs(MaxPoint.Y - MinPoint.Y);
        public Point3d Center => new Point3d((MinPoint.X + MaxPoint.X) / 2, (MinPoint.Y + MaxPoint.Y) / 2, 0);
    }
    
    public class SetbackLines
    {
        public double FrontSetback { get; set; }
        public double SideSetback { get; set; }
        public double RearSetback { get; set; }
        public Rectangle3d InnerBoundary { get; set; }
    }
    
    public class BuildingLayout
    {
        public List<Point3d> BuildingPositions { get; set; } = new List<Point3d>();
        public int TotalBuildings { get; set; }
    }
    
    public class BuildingTemplate
    {
        public double Width { get; set; }
        public double Depth { get; set; }
        public string Label { get; set; }
        public int Floors { get; set; }
    }
    
    public class RoadSystem
    {
        public List<RoadSegment> MainRoads { get; set; } = new List<RoadSegment>();
        public List<RoadSegment> SecondaryRoads { get; set; } = new List<RoadSegment>();
        public List<RoadSegment> InternalRoads { get; set; } = new List<RoadSegment>();
    }
    
    public class RoadSegment
    {
        public Point3d StartPoint { get; set; }
        public Point3d EndPoint { get; set; }
        public double Width { get; set; }
    }
    
    public class LandscapeDesign
    {
        public List<GreenArea> GreenAreas { get; set; } = new List<GreenArea>();
        public List<LandscapeFeature> Features { get; set; } = new List<LandscapeFeature>();
    }
    
    public class GreenArea
    {
        public Point3d Center { get; set; }
        public double Size { get; set; }
        public GreenAreaType Type { get; set; }
    }
    
    public class LandscapeFeature
    {
        public Point3d Position { get; set; }
        public double Radius { get; set; }
        public FeatureType Type { get; set; }
    }
    
    public class UtilityNetwork
    {
        public List<PipeSegment> WaterPipes { get; set; }
        public List<PipeSegment> DrainagePipes { get; set; }
        public List<CableSegment> PowerCables { get; set; }
    }
    
    public class PipeSegment
    {
        public Point3d StartPoint { get; set; }
        public Point3d EndPoint { get; set; }
        public double Diameter { get; set; }
    }
    
    public class CableSegment
    {
        public Point3d StartPoint { get; set; }
        public Point3d EndPoint { get; set; }
        public double Voltage { get; set; }
    }
    
    public enum RoadType { Main, Secondary, Internal }
    public enum GreenAreaType { CentralPark, Garden, Lawn }
    public enum FeatureType { Fountain, Sculpture, Playground }
    public enum UtilityType { Water, Drainage, Power, Gas }
}
```

### 案例二：商业综合体标准化设计

**项目背景：**
某商业综合体项目，包含购物中心、办公楼、酒店等多种业态。通过标准化设计模块，实现了设计效率提升60%，成本降低15%。

**技术方案：**
```csharp
using Autodesk.AutoCAD.Runtime;
using Autodesk.AutoCAD.ApplicationServices;
using Autodesk.AutoCAD.DatabaseServices;
using Autodesk.AutoCAD.Geometry;
using Autodesk.AutoCAD.EditorInput;
using System;
using System.Collections.Generic;

namespace AutoCAD.CaseStudies.CommercialComplex
{
    // 商业综合体标准化设计系统
    public class CommercialComplexStandardization
    {
        [CommandMethod("CREATECOMMERCIALCOMPLEX")]
        public void CreateCommercialComplex()
        {
            Document doc = Application.DocumentManager.MdiActiveDocument;
            Database db = doc.Database;
            Editor ed = doc.Editor;
            
            try
            {
                // 获取商业综合体参数
                var complexParams = GetCommercialComplexParameters(ed);
                if (complexParams == null)
                    return;
                
                // 创建商业综合体
                var complex = new CommercialComplex(complexParams);
                
                // 生成标准化模块
                GenerateStandardModules(db, complex);
                
                // 组合模块
                AssembleModules(db, complex);
                
                // 生成连接系统
                GenerateConnectionSystems(db, complex);
                
                // 生成配套设施
                GenerateSupportingFacilities(db, complex);
                
                ed.WriteMessage("\n商业综合体设计完成！");
                ed.WriteMessage($"\n总建面积：{complex.TotalArea:F1}平方米");
                ed.WriteMessage($"\n标准模块：{complex.StandardModules.Count}个");
            }
            catch (System.Exception ex)
            {
                ed.WriteMessage($"\n创建商业综合体失败：{ex.Message}");
            }
        }
        
        private CommercialComplexParameters GetCommercialComplexParameters(Editor ed)
        {
            var parameters = new CommercialComplexParameters();
            
            // 获取项目基本信息
            PromptStringOptions nameOpt = new PromptStringOptions("\n输入项目名称: ");
            nameOpt.DefaultValue = "中央商业广场";
            PromptResult nameResult = ed.GetString(nameOpt);
            parameters.ProjectName = nameResult.StringResult;
            
            // 获取项目类型组合
            PromptKeywordOptions typeOpt = new PromptKeywordOptions("\n选择项目类型: ");
            typeOpt.Keywords.Add("MALL");
            typeOpt.Keywords.Add("OFFICE");
            typeOpt.Keywords.Add("HOTEL");
            typeOpt.Keywords.Add("MIXED");
            typeOpt.Default = "MIXED";
            PromptResult typeResult = ed.GetKeywords(typeOpt);
            parameters.ComplexType = typeResult.StringResult;
            
            // 获取总建筑面积
            PromptDoubleOptions areaOpt = new PromptDoubleOptions("\n输入总建筑面积(平方米): ");
            areaOpt.DefaultValue = 100000;
            areaOpt.LowerLimit = 50000;
            PromptDoubleResult areaResult = ed.GetDouble(areaOpt);
            parameters.TotalArea = areaResult.Value;
            
            // 获取地块信息
            PromptPointOptions cornerOpt = new PromptPointOptions("\n选择地块第一角点: ");
            PromptPointResult cornerResult = ed.GetPoint(cornerOpt);
            if (cornerResult.Status != PromptStatus.OK)
                return null;
            
            PromptCornerOptions oppositeOpt = new PromptCornerOptions("\n选择地块对角点: ");
            oppositeOpt.UseBasePoint = true;
            oppositeOpt.BasePoint = cornerResult.Value;
            PromptPointResult oppositeResult = ed.GetPoint(oppositeOpt);
            if (oppositeResult.Status != PromptStatus.OK)
                return null;
            
            parameters.SiteBoundary = new Rectangle3d(cornerResult.Value, oppositeResult.Value);
            
            return parameters;
        }
        
        private void GenerateStandardModules(Database db, CommercialComplex complex)
        {
            var moduleLibrary = GetStandardModuleLibrary();
            
            // 根据项目类型选择标准模块
            var selectedModules = SelectModulesForComplex(complex.Parameters, moduleLibrary);
            
            foreach (var moduleType in selectedModules)
            {
                var module = CreateStandardModule(moduleType);
                complex.StandardModules.Add(module);
            }
        }
        
        private StandardModuleLibrary GetStandardModuleLibrary()
        {
            var library = new StandardModuleLibrary();
            
            // 购物中心模块
            library.Modules.Add("RETAIL_STORE", new StandardModule
            {
                Name = "标准零售店",
                Width = 12.0,
                Depth = 15.0,
                Height = 4.5,
                Category = ModuleCategory.Retail,
                StandardElements = new List<string> { "入口", "展示区", "收银台", "仓储" }
            });
            
            library.Modules.Add("FOOD_COURT", new StandardModule
            {
                Name = "美食广场",
                Width = 30.0,
                Depth = 25.0,
                Height = 4.5,
                Category = ModuleCategory.Food,
                StandardElements = new List<string> { "就餐区", "厨房", "卫生间", "服务台" }
            });
            
            // 办公模块
            library.Modules.Add("OFFICE_UNIT", new StandardModule
            {
                Name = "标准办公单元",
                Width = 8.0,
                Depth = 12.0,
                Height = 3.5,
                Category = ModuleCategory.Office,
                StandardElements = new List<string> { "办公区", "会议区", "接待区" }
            });
            
            library.Modules.Add("MEETING_ROOM", new StandardModule
            {
                Name = "会议室",
                Width = 6.0,
                Depth = 8.0,
                Height = 3.5,
                Category = ModuleCategory.Office,
                StandardElements = new List<string> { "会议桌", "投影设备", "白板" }
            });
            
            // 酒店模块
            library.Modules.Add("HOTEL_ROOM", new StandardModule
            {
                Name = "标准客房",
                Width = 4.5,
                Depth = 8.0,
                Height = 3.2,
                Category = ModuleCategory.Hotel,
                StandardElements = new List<string> { "卧室", "卫生间", "衣帽间" }
            });
            
            library.Modules.Add("HOTEL_LOBBY", new StandardModule
            {
                Name = "酒店大堂",
                Width = 20.0,
                Depth = 15.0,
                Height = 6.0,
                Category = ModuleCategory.Hotel,
                StandardElements = new List<string> { "接待区", "休息区", "服务台" }
            });
            
            // 公共设施模块
            library.Modules.Add("RESTROOM", new StandardModule
            {
                Name = "公共卫生间",
                Width = 6.0,
                Depth = 8.0,
                Height = 3.5,
                Category = ModuleCategory.Public,
                StandardElements = new List<string> { "男卫生间", "女卫生间", "无障碍卫生间" }
            });
            
            library.Modules.Add("ELEVATOR", new StandardModule
            {
                Name = "电梯井",
                Width = 3.0,
                Depth = 3.0,
                Height = 3.5,
                Category = ModuleCategory.Public,
                StandardElements = new List<string> { "电梯轿厢", "电梯井道" }
            });
            
            return library;
        }
        
        private List<string> SelectModulesForComplex(CommercialComplexParameters parameters, StandardModuleLibrary library)
        {
            var selectedModules = new List<string>();
            
            switch (parameters.ComplexType)
            {
                case "MALL":
                    selectedModules.AddRange(new[] { "RETAIL_STORE", "FOOD_COURT", "RESTROOM", "ELEVATOR" });
                    break;
                case "OFFICE":
                    selectedModules.AddRange(new[] { "OFFICE_UNIT", "MEETING_ROOM", "RESTROOM", "ELEVATOR" });
                    break;
                case "HOTEL":
                    selectedModules.AddRange(new[] { "HOTEL_ROOM", "HOTEL_LOBBY", "RESTROOM", "ELEVATOR" });
                    break;
                case "MIXED":
                    selectedModules.AddRange(new[] { "RETAIL_STORE", "OFFICE_UNIT", "HOTEL_ROOM", "FOOD_COURT", "RESTROOM", "ELEVATOR" });
                    break;
            }
            
            return selectedModules;
        }
        
        private StandardModule CreateStandardModule(string moduleType)
        {
            var library = GetStandardModuleLibrary();
            return library.Modules[moduleType];
        }
        
        private void AssembleModules(Database db, CommercialComplex complex)
        {
            var layout = OptimizeModuleLayout(complex);
            
            using (Transaction trans = db.TransactionManager.StartTransaction())
            {
                BlockTable bt = trans.GetObject(db.BlockTableId, OpenMode.ForRead) as BlockTable;
                BlockTableRecord modelSpace = trans.GetObject(bt[BlockTableRecord.ModelSpace], OpenMode.ForWrite) as BlockTableRecord;
                
                foreach (var moduleInstance in layout.ModuleInstances)
                {
                    CreateModuleInstance(modelSpace, trans, moduleInstance);
                }
                
                trans.Commit();
            }
        }
        
        private ModuleLayout OptimizeModuleLayout(CommercialComplex complex)
        {
            var layout = new ModuleLayout();
            
            // 简化的模块布局优化算法
            var availableArea = complex.Parameters.SiteBoundary;
            var modules = complex.StandardModules;
            
            // 根据模块类型进行分区
            var zones = CreateFunctionalZones(availableArea, complex.Parameters.ComplexType);
            
            // 在各功能区内布置模块
            foreach (var zone in zones)
            {
                var zoneModules = GetModulesForZone(modules, zone.Type);
                var modulePositions = ArrangeModulesInZone(zone, zoneModules);
                
                foreach (var modulePos in modulePositions)
                {
                    layout.ModuleInstances.Add(modulePos);
                }
            }
            
            return layout;
        }
        
        private List<FunctionalZone> CreateFunctionalZones(Rectangle3d availableArea, string complexType)
        {
            var zones = new List<FunctionalZone>();
            
            switch (complexType)
            {
                case "MIXED":
                    // 混合用途的功能分区
                    double width = availableArea.Width;
                    double height = availableArea.Height;
                    
                    // 零售区（底层）
                    zones.Add(new FunctionalZone
                    {
                        Type = ZoneType.Retail,
                        Boundary = new Rectangle3d(
                            availableArea.MinPoint,
                            new Point3d(availableArea.MaxPoint.X, availableArea.MinPoint.Y + height * 0.3, 0)),
                        Level = 1
                    });
                    
                    // 办公区（中层）
                    zones.Add(new FunctionalZone
                    {
                        Type = ZoneType.Office,
                        Boundary = new Rectangle3d(
                            new Point3d(availableArea.MinPoint.X, availableArea.MinPoint.Y + height * 0.3, 0),
                            new Point3d(availableArea.MaxPoint.X, availableArea.MinPoint.Y + height * 0.7, 0)),
                        Level = 2
                    });
                    
                    // 酒店区（高层）
                    zones.Add(new FunctionalZone
                    {
                        Type = ZoneType.Hotel,
                        Boundary = new Rectangle3d(
                            new Point3d(availableArea.MinPoint.X, availableArea.MinPoint.Y + height * 0.7, 0),
                            availableArea.MaxPoint),
                        Level = 3
                    });
                    break;
                    
                default:
                    // 其他类型的功能分区
                    zones.Add(new FunctionalZone
                    {
                        Type = ZoneType.Public,
                        Boundary = availableArea,
                        Level = 1
                    });
                    break;
            }
            
            return zones;
        }
        
        private List<StandardModule> GetModulesForZone(List<StandardModule> modules, ZoneType zoneType)
        {
            return modules.Where(m => 
            {
                switch (zoneType)
                {
                    case ZoneType.Retail:
                        return m.Category == ModuleCategory.Retail || m.Category == ModuleCategory.Food;
                    case ZoneType.Office:
                        return m.Category == ModuleCategory.Office;
                    case ZoneType.Hotel:
                        return m.Category == ModuleCategory.Hotel;
                    default:
                        return m.Category == ModuleCategory.Public;
                }
            }).ToList();
        }
        
        private List<ModuleInstance> ArrangeModulesInZone(FunctionalZone zone, List<StandardModule> modules)
        {
            var instances = new List<ModuleInstance>();
            
            // 简化的网格布局
            var boundary = zone.Boundary;
            var gridSize = CalculateOptimalGridSize(modules);
            
            int cols = (int)(boundary.Width / gridSize.Width);
            int rows = (int)(boundary.Height / gridSize.Depth);
            
            Point3d startPoint = new Point3d(
                boundary.MinPoint.X + gridSize.Width / 2,
                boundary.MinPoint.Y + gridSize.Depth / 2,
                0);
            
            int moduleIndex = 0;
            for (int row = 0; row < rows; row++)
            {
                for (int col = 0; col < cols; col++)
                {
                    if (moduleIndex >= modules.Count)
                        break;
                    
                    Point3d position = new Point3d(
                        startPoint.X + col * gridSize.Width,
                        startPoint.Y + row * gridSize.Depth,
                        0);
                    
                    instances.Add(new ModuleInstance
                    {
                        Module = modules[moduleIndex],
                        Position = position,
                        Rotation = 0,
                        Level = zone.Level
                    });
                    
                    moduleIndex++;
                }
            }
            
            return instances;
        }
        
        private ModuleSize CalculateOptimalGridSize(List<StandardModule> modules)
        {
            // 计算最优网格尺寸
            double avgWidth = modules.Average(m => m.Width);
            double avgDepth = modules.Average(m => m.Depth);
            
            return new ModuleSize
            {
                Width = avgWidth + 2.0,  // 留出间距
                Depth = avgDepth + 2.0
            };
        }
        
        private void CreateModuleInstance(BlockTableRecord modelSpace, Transaction trans, ModuleInstance instance)
        {
            var module = instance.Module;
            var position = instance.Position;
            
            // 创建模块轮廓
            Polyline moduleOutline = new Polyline();
            moduleOutline.AddVertexAt(0, new Point2d(position.X - module.Width/2, position.Y - module.Depth/2), 0, 0, 0);
            moduleOutline.AddVertexAt(1, new Point2d(position.X + module.Width/2, position.Y - module.Depth/2), 0, 0, 0);
            moduleOutline.AddVertexAt(2, new Point2d(position.X + module.Width/2, position.Y + module.Depth/2), 0, 0, 0);
            moduleOutline.AddVertexAt(3, new Point2d(position.X - module.Width/2, position.Y + module.Depth/2), 0, 0, 0);
            moduleOutline.Closed = true;
            moduleOutline.Layer = $"MODULE_{module.Category}";
            moduleOutline.LineWeight = LineWeight.LineWeight030;
            
            modelSpace.AppendEntity(moduleOutline);
            trans.AddNewlyCreatedDBObject(moduleOutline, true);
            
            // 添加模块标签
            DBText moduleLabel = new DBText();
            moduleLabel.Position = position;
            moduleLabel.TextString = module.Name;
            moduleLabel.Height = 1.5;
            moduleLabel.HorizontalMode = TextHorizontalMode.TextCenter;
            moduleLabel.VerticalMode = TextVerticalMode.TextVerticalMid;
            moduleLabel.AlignmentPoint = position;
            moduleLabel.Layer = "MODULE_TEXT";
            
            modelSpace.AppendEntity(moduleLabel);
            trans.AddNewlyCreatedDBObject(moduleLabel, true);
        }
        
        private void GenerateConnectionSystems(Database db, CommercialComplex complex)
        {
            // 生成连接系统（走廊、电梯、楼梯等）
            var layout = complex.ModuleLayout;
            
            using (Transaction trans = db.TransactionManager.StartTransaction())
            {
                BlockTable bt = trans.GetObject(db.BlockTableId, OpenMode.ForRead) as BlockTable;
                BlockTableRecord modelSpace = trans.GetObject(bt[BlockTableRecord.ModelSpace], OpenMode.ForWrite) as BlockTableRecord;
                
                // 生成走廊系统
                GenerateCorridorSystem(modelSpace, trans, layout);
                
                // 生成垂直交通
                GenerateVerticalTransport(modelSpace, trans, layout);
                
                trans.Commit();
            }
        }
        
        private void GenerateCorridorSystem(BlockTableRecord modelSpace, Transaction trans, ModuleLayout layout)
        {
            // 生成走廊系统的实现
            // 这里需要根据模块布局生成连接走廊
        }
        
        private void GenerateVerticalTransport(BlockTableRecord modelSpace, Transaction trans, ModuleLayout layout)
        {
            // 生成垂直交通系统的实现
            // 这里需要根据布局安排电梯和楼梯
        }
        
        private void GenerateSupportingFacilities(Database db, CommercialComplex complex)
        {
            // 生成配套设施
            // 包括停车场、绿化、外部设施等
        }
        
        // 数据结构
        public class CommercialComplexParameters
        {
            public string ProjectName { get; set; }
            public string ComplexType { get; set; }
            public double TotalArea { get; set; }
            public Rectangle3d SiteBoundary { get; set; }
        }
        
        public class CommercialComplex
        {
            public CommercialComplexParameters Parameters { get; set; }
            public List<StandardModule> StandardModules { get; set; } = new List<StandardModule>();
            public ModuleLayout ModuleLayout { get; set; }
            public double TotalArea { get; set; }
        }
        
        public class StandardModule
        {
            public string Name { get; set; }
            public double Width { get; set; }
            public double Depth { get; set; }
            public double Height { get; set; }
            public ModuleCategory Category { get; set; }
            public List<string> StandardElements { get; set; } = new List<string>();
        }
        
        public class StandardModuleLibrary
        {
            public Dictionary<string, StandardModule> Modules { get; set; } = new Dictionary<string, StandardModule>();
        }
        
        public class ModuleLayout
        {
            public List<ModuleInstance> ModuleInstances { get; set; } = new List<ModuleInstance>();
        }
        
        public class ModuleInstance
        {
            public StandardModule Module { get; set; }
            public Point3d Position { get; set; }
            public double Rotation { get; set; }
            public int Level { get; set; }
        }
        
        public class FunctionalZone
        {
            public ZoneType Type { get; set; }
            public Rectangle3d Boundary { get; set; }
            public int Level { get; set; }
        }
        
        public class ModuleSize
        {
            public double Width { get; set; }
            public double Depth { get; set; }
        }
        
        public enum ModuleCategory { Retail, Office, Hotel, Food, Public }
        public enum ZoneType { Retail, Office, Hotel, Public }
    }
}
```

---

**CURSOR提示词模板：**
```
请为AutoCAD案例分析提供以下内容：
1. 实际项目的技术解决方案
2. 标准化模块的设计方法
3. 项目效率提升的具体数据
4. 常见问题的解决方法
5. 最佳实践和经验总结
```