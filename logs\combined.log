{"level":"info","message":"Initializing RPA Engine...","module":"RPAEngine","timestamp":"2025-07-31T00:53:27.491Z"}
{"level":"info","message":"Detecting local RPA engines...","module":"RPAEngineDetector","timestamp":"2025-07-31T00:53:27.492Z"}
{"level":"info","message":"Initializing RPA connection manager...","module":"RPAConnectionManager","timestamp":"2025-07-31T00:53:27.491Z"}
{"level":"info","message":"Starting Cursor RPA Integration...","module":"main","timestamp":"2025-07-31T00:53:27.487Z"}
{"cause":{"code":"ECONNREFUSED"},"code":"ECONNREFUSED","config":{"adapter":["xhr","http","fetch"],"allowAbsoluteUrls":true,"env":{},"headers":{"Accept":"application/json, text/plain, */*","Accept-Encoding":"gzip, compress, deflate, br","User-Agent":"axios/1.11.0"},"maxBodyLength":-1,"maxContentLength":-1,"method":"get","timeout":5000,"transformRequest":[null],"transformResponse":[null],"transitional":{"clarifyTimeoutError":false,"forcedJSONParsing":true,"silentJSONParsing":true},"url":"http://localhost:8080/health","xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN"},"errors":[{"address":"::1","code":"ECONNREFUSED","errno":-4078,"port":8080,"syscall":"connect"},{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"port":8080,"syscall":"connect"}],"level":"error","message":"Connection test failed:","module":"RPAConnection","name":"AggregateError","request":{"_currentRequest":{"_closed":false,"_contentLength":0,"_defaultKeepAlive":true,"_ended":false,"_events":{},"_eventsCount":7,"_hasBody":true,"_header":"GET /health HTTP/1.1\r\nAccept: application/json, text/plain, */*\r\nUser-Agent: axios/1.11.0\r\nAccept-Encoding: gzip, compress, deflate, br\r\nHost: localhost:8080\r\nConnection: keep-alive\r\n\r\n","_headerSent":true,"_keepAliveTimeout":0,"_last":true,"_redirectable":"[Circular]","_removedConnection":false,"_removedContLen":false,"_removedTE":false,"_trailer":"","aborted":false,"agent":{"_events":{},"_eventsCount":2,"defaultPort":80,"freeSockets":{},"keepAlive":true,"keepAliveMsecs":1000,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"http:","requests":{},"scheduling":"lifo","sockets":{"localhost:8080:":[{"_closeAfterHandlingError":false,"_events":{"close":[null,null,null],"connect":[null,null,null],"timeout":[null,null,null]},"_eventsCount":8,"_hadError":true,"_host":"localhost","_httpMessage":"[Circular]","_parent":null,"_pendingData":"GET /health HTTP/1.1\r\nAccept: application/json, text/plain, */*\r\nUser-Agent: axios/1.11.0\r\nAccept-Encoding: gzip, compress, deflate, br\r\nHost: localhost:8080\r\nConnection: keep-alive\r\n\r\n","_pendingEncoding":"latin1","_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"_server":null,"_sockname":null,"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":185,"pendingcb":1,"writelen":185},"allowHalfOpen":false,"autoSelectFamilyAttemptedAddresses":["::1:8080","127.0.0.1:8080"],"connecting":false,"parser":null,"server":null,"timeout":5000}]},"totalSocketCount":1},"chunkedEncoding":false,"destroyed":false,"finished":true,"host":"localhost","maxHeadersCount":null,"maxRequestsOnConnectionReached":false,"method":"GET","outputData":[],"outputSize":0,"parser":null,"path":"/health","protocol":"http:","res":null,"reusedSocket":false,"sendDate":false,"shouldKeepAlive":true,"strictContentLength":false,"upgradeOrConnect":false,"useChunkedEncodingByDefault":false,"writable":true},"_currentUrl":"http://localhost:8080/health","_ended":true,"_ending":true,"_events":{"socket":[null,null]},"_eventsCount":3,"_options":{"agents":{},"beforeRedirects":{},"headers":{"Accept":"application/json, text/plain, */*","Accept-Encoding":"gzip, compress, deflate, br","User-Agent":"axios/1.11.0"},"hostname":"localhost","maxBodyLength":null,"maxRedirects":21,"method":"GET","nativeProtocols":{"http:":{"METHODS":["ACL","BIND","CHECKOUT","CONNECT","COPY","DELETE","GET","HEAD","LINK","LOCK","M-SEARCH","MERGE","MKACTIVITY","MKCALENDAR","MKCOL","MOVE","NOTIFY","OPTIONS","PATCH","POST","PROPFIND","PROPPATCH","PURGE","PUT","QUERY","REBIND","REPORT","SEARCH","SOURCE","SUBSCRIBE","TRACE","UNBIND","UNLINK","UNLOCK","UNSUBSCRIBE"],"STATUS_CODES":{"100":"Continue","101":"Switching Protocols","102":"Processing","103":"Early Hints","200":"OK","201":"Created","202":"Accepted","203":"Non-Authoritative Information","204":"No Content","205":"Reset Content","206":"Partial Content","207":"Multi-Status","208":"Already Reported","226":"IM Used","300":"Multiple Choices","301":"Moved Permanently","302":"Found","303":"See Other","304":"Not Modified","305":"Use Proxy","307":"Temporary Redirect","308":"Permanent Redirect","400":"Bad Request","401":"Unauthorized","402":"Payment Required","403":"Forbidden","404":"Not Found","405":"Method Not Allowed","406":"Not Acceptable","407":"Proxy Authentication Required","408":"Request Timeout","409":"Conflict","410":"Gone","411":"Length Required","412":"Precondition Failed","413":"Payload Too Large","414":"URI Too Long","415":"Unsupported Media Type","416":"Range Not Satisfiable","417":"Expectation Failed","418":"I'm a Teapot","421":"Misdirected Request","422":"Unprocessable Entity","423":"Locked","424":"Failed Dependency","425":"Too Early","426":"Upgrade Required","428":"Precondition Required","429":"Too Many Requests","431":"Request Header Fields Too Large","451":"Unavailable For Legal Reasons","500":"Internal Server Error","501":"Not Implemented","502":"Bad Gateway","503":"Service Unavailable","504":"Gateway Timeout","505":"HTTP Version Not Supported","506":"Variant Also Negotiates","507":"Insufficient Storage","508":"Loop Detected","509":"Bandwidth Limit Exceeded","510":"Not Extended","511":"Network Authentication Required"},"globalAgent":{"_events":{},"_eventsCount":2,"defaultPort":80,"freeSockets":{},"keepAlive":true,"keepAliveMsecs":1000,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"http:","requests":{},"scheduling":"lifo","sockets":{"localhost:8080:":[{"_closeAfterHandlingError":false,"_events":{"close":[null,null,null],"connect":[null,null,null],"timeout":[null,null,null]},"_eventsCount":8,"_hadError":true,"_host":"localhost","_httpMessage":{"_closed":false,"_contentLength":0,"_defaultKeepAlive":true,"_ended":false,"_events":{},"_eventsCount":7,"_hasBody":true,"_header":"GET /health HTTP/1.1\r\nAccept: application/json, text/plain, */*\r\nUser-Agent: axios/1.11.0\r\nAccept-Encoding: gzip, compress, deflate, br\r\nHost: localhost:8080\r\nConnection: keep-alive\r\n\r\n","_headerSent":true,"_keepAliveTimeout":0,"_last":true,"_redirectable":"[Circular]","_removedConnection":false,"_removedContLen":false,"_removedTE":false,"_trailer":"","aborted":false,"agent":"[Circular]","chunkedEncoding":false,"destroyed":false,"finished":true,"host":"localhost","maxHeadersCount":null,"maxRequestsOnConnectionReached":false,"method":"GET","outputData":[],"outputSize":0,"parser":null,"path":"/health","protocol":"http:","res":null,"reusedSocket":false,"sendDate":false,"shouldKeepAlive":true,"strictContentLength":false,"upgradeOrConnect":false,"useChunkedEncodingByDefault":false,"writable":true},"_parent":null,"_pendingData":"GET /health HTTP/1.1\r\nAccept: application/json, text/plain, */*\r\nUser-Agent: axios/1.11.0\r\nAccept-Encoding: gzip, compress, deflate, br\r\nHost: localhost:8080\r\nConnection: keep-alive\r\n\r\n","_pendingEncoding":"latin1","_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"_server":null,"_sockname":null,"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":185,"pendingcb":1,"writelen":185},"allowHalfOpen":false,"autoSelectFamilyAttemptedAddresses":["::1:8080","127.0.0.1:8080"],"connecting":false,"parser":null,"server":null,"timeout":5000}]},"totalSocketCount":1},"maxHeaderSize":16384},"https:":{"globalAgent":{"_events":{},"_eventsCount":2,"_sessionCache":{"list":[],"map":{}},"defaultPort":443,"freeSockets":{},"keepAlive":true,"keepAliveMsecs":1000,"maxCachedSessions":100,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"https:","requests":{},"scheduling":"lifo","sockets":{},"totalSocketCount":0}}},"path":"/health","pathname":"/health","port":"8080","protocol":"http:"},"_redirectCount":0,"_redirects":[],"_requestBodyBuffers":[],"_requestBodyLength":0,"_timeout":null,"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0}},"stack":"AggregateError\n    at Function.AxiosError.from (D:\\A-CLAUDE-BIGMODEL20250730\\node_modules\\axios\\lib\\core\\AxiosError.js:92:14)\n    at RedirectableRequest.handleRequestError (D:\\A-CLAUDE-BIGMODEL20250730\\node_modules\\axios\\lib\\adapters\\http.js:620:25)\n    at RedirectableRequest.emit (node:events:530:35)\n    at RedirectableRequest.emit (node:domain:489:12)\n    at ClientRequest.eventHandlers.<computed> (D:\\A-CLAUDE-BIGMODEL20250730\\node_modules\\follow-redirects\\index.js:49:24)\n    at ClientRequest.emit (node:events:518:28)\n    at ClientRequest.emit (node:domain:489:12)\n    at emitErrorEvent (node:_http_client:104:11)\n    at Socket.socketErrorListener (node:_http_client:518:5)\n    at Socket.emit (node:events:518:28)\n    at Axios.request (D:\\A-CLAUDE-BIGMODEL20250730\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async RPAConnection.testConnection (D:\\A-CLAUDE-BIGMODEL20250730\\src\\core\\connection\\engine-detector.ts:91:24)\n    at async RPAEngineDetector.detectYingdaoEngine (D:\\A-CLAUDE-BIGMODEL20250730\\src\\core\\connection\\engine-detector.ts:38:29)\n    at async RPAEngineDetector.detectLocalEngines (D:\\A-CLAUDE-BIGMODEL20250730\\src\\core\\connection\\engine-detector.ts:14:29)\n    at async RPAConnectionManager.initialize (D:\\A-CLAUDE-BIGMODEL20250730\\src\\core\\connection\\connection-manager.ts:27:28)\n    at async RPAEngine.initialize (D:\\A-CLAUDE-BIGMODEL20250730\\src\\core\\index.ts:24:7)\n    at async main (D:\\A-CLAUDE-BIGMODEL20250730\\src\\index.ts:12:5)","timestamp":"2025-07-31T00:53:28.129Z"}
{"cause":{"code":"ECONNREFUSED"},"code":"ECONNREFUSED","config":{"adapter":["xhr","http","fetch"],"allowAbsoluteUrls":true,"env":{},"headers":{"Accept":"application/json, text/plain, */*","Accept-Encoding":"gzip, compress, deflate, br","User-Agent":"axios/1.11.0"},"maxBodyLength":-1,"maxContentLength":-1,"method":"get","timeout":5000,"transformRequest":[null],"transformResponse":[null],"transitional":{"clarifyTimeoutError":false,"forcedJSONParsing":true,"silentJSONParsing":true},"url":"http://localhost:8081/health","xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN"},"errors":[{"address":"::1","code":"ECONNREFUSED","errno":-4078,"port":8081,"syscall":"connect"},{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"port":8081,"syscall":"connect"}],"level":"error","message":"Connection test failed:","module":"RPAConnection","name":"AggregateError","request":{"_currentRequest":{"_closed":false,"_contentLength":0,"_defaultKeepAlive":true,"_ended":false,"_events":{},"_eventsCount":7,"_hasBody":true,"_header":"GET /health HTTP/1.1\r\nAccept: application/json, text/plain, */*\r\nUser-Agent: axios/1.11.0\r\nAccept-Encoding: gzip, compress, deflate, br\r\nHost: localhost:8081\r\nConnection: keep-alive\r\n\r\n","_headerSent":true,"_keepAliveTimeout":0,"_last":true,"_redirectable":"[Circular]","_removedConnection":false,"_removedContLen":false,"_removedTE":false,"_trailer":"","aborted":false,"agent":{"_events":{},"_eventsCount":2,"defaultPort":80,"freeSockets":{},"keepAlive":true,"keepAliveMsecs":1000,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"http:","requests":{},"scheduling":"lifo","sockets":{"localhost:8081:":[{"_closeAfterHandlingError":false,"_events":{"close":[null,null,null],"connect":[null,null,null],"timeout":[null,null,null]},"_eventsCount":8,"_hadError":true,"_host":"localhost","_httpMessage":"[Circular]","_parent":null,"_pendingData":"GET /health HTTP/1.1\r\nAccept: application/json, text/plain, */*\r\nUser-Agent: axios/1.11.0\r\nAccept-Encoding: gzip, compress, deflate, br\r\nHost: localhost:8081\r\nConnection: keep-alive\r\n\r\n","_pendingEncoding":"latin1","_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"_server":null,"_sockname":null,"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":185,"pendingcb":1,"writelen":185},"allowHalfOpen":false,"autoSelectFamilyAttemptedAddresses":["::1:8081","127.0.0.1:8081"],"connecting":false,"parser":null,"server":null,"timeout":5000}]},"totalSocketCount":1},"chunkedEncoding":false,"destroyed":false,"finished":true,"host":"localhost","maxHeadersCount":null,"maxRequestsOnConnectionReached":false,"method":"GET","outputData":[],"outputSize":0,"parser":null,"path":"/health","protocol":"http:","res":null,"reusedSocket":false,"sendDate":false,"shouldKeepAlive":true,"strictContentLength":false,"upgradeOrConnect":false,"useChunkedEncodingByDefault":false,"writable":true},"_currentUrl":"http://localhost:8081/health","_ended":true,"_ending":true,"_events":{"socket":[null,null]},"_eventsCount":3,"_options":{"agents":{},"beforeRedirects":{},"headers":{"Accept":"application/json, text/plain, */*","Accept-Encoding":"gzip, compress, deflate, br","User-Agent":"axios/1.11.0"},"hostname":"localhost","maxBodyLength":null,"maxRedirects":21,"method":"GET","nativeProtocols":{"http:":{"METHODS":["ACL","BIND","CHECKOUT","CONNECT","COPY","DELETE","GET","HEAD","LINK","LOCK","M-SEARCH","MERGE","MKACTIVITY","MKCALENDAR","MKCOL","MOVE","NOTIFY","OPTIONS","PATCH","POST","PROPFIND","PROPPATCH","PURGE","PUT","QUERY","REBIND","REPORT","SEARCH","SOURCE","SUBSCRIBE","TRACE","UNBIND","UNLINK","UNLOCK","UNSUBSCRIBE"],"STATUS_CODES":{"100":"Continue","101":"Switching Protocols","102":"Processing","103":"Early Hints","200":"OK","201":"Created","202":"Accepted","203":"Non-Authoritative Information","204":"No Content","205":"Reset Content","206":"Partial Content","207":"Multi-Status","208":"Already Reported","226":"IM Used","300":"Multiple Choices","301":"Moved Permanently","302":"Found","303":"See Other","304":"Not Modified","305":"Use Proxy","307":"Temporary Redirect","308":"Permanent Redirect","400":"Bad Request","401":"Unauthorized","402":"Payment Required","403":"Forbidden","404":"Not Found","405":"Method Not Allowed","406":"Not Acceptable","407":"Proxy Authentication Required","408":"Request Timeout","409":"Conflict","410":"Gone","411":"Length Required","412":"Precondition Failed","413":"Payload Too Large","414":"URI Too Long","415":"Unsupported Media Type","416":"Range Not Satisfiable","417":"Expectation Failed","418":"I'm a Teapot","421":"Misdirected Request","422":"Unprocessable Entity","423":"Locked","424":"Failed Dependency","425":"Too Early","426":"Upgrade Required","428":"Precondition Required","429":"Too Many Requests","431":"Request Header Fields Too Large","451":"Unavailable For Legal Reasons","500":"Internal Server Error","501":"Not Implemented","502":"Bad Gateway","503":"Service Unavailable","504":"Gateway Timeout","505":"HTTP Version Not Supported","506":"Variant Also Negotiates","507":"Insufficient Storage","508":"Loop Detected","509":"Bandwidth Limit Exceeded","510":"Not Extended","511":"Network Authentication Required"},"globalAgent":{"_events":{},"_eventsCount":2,"defaultPort":80,"freeSockets":{},"keepAlive":true,"keepAliveMsecs":1000,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"http:","requests":{},"scheduling":"lifo","sockets":{"localhost:8081:":[{"_closeAfterHandlingError":false,"_events":{"close":[null,null,null],"connect":[null,null,null],"timeout":[null,null,null]},"_eventsCount":8,"_hadError":true,"_host":"localhost","_httpMessage":{"_closed":false,"_contentLength":0,"_defaultKeepAlive":true,"_ended":false,"_events":{},"_eventsCount":7,"_hasBody":true,"_header":"GET /health HTTP/1.1\r\nAccept: application/json, text/plain, */*\r\nUser-Agent: axios/1.11.0\r\nAccept-Encoding: gzip, compress, deflate, br\r\nHost: localhost:8081\r\nConnection: keep-alive\r\n\r\n","_headerSent":true,"_keepAliveTimeout":0,"_last":true,"_redirectable":"[Circular]","_removedConnection":false,"_removedContLen":false,"_removedTE":false,"_trailer":"","aborted":false,"agent":"[Circular]","chunkedEncoding":false,"destroyed":false,"finished":true,"host":"localhost","maxHeadersCount":null,"maxRequestsOnConnectionReached":false,"method":"GET","outputData":[],"outputSize":0,"parser":null,"path":"/health","protocol":"http:","res":null,"reusedSocket":false,"sendDate":false,"shouldKeepAlive":true,"strictContentLength":false,"upgradeOrConnect":false,"useChunkedEncodingByDefault":false,"writable":true},"_parent":null,"_pendingData":"GET /health HTTP/1.1\r\nAccept: application/json, text/plain, */*\r\nUser-Agent: axios/1.11.0\r\nAccept-Encoding: gzip, compress, deflate, br\r\nHost: localhost:8081\r\nConnection: keep-alive\r\n\r\n","_pendingEncoding":"latin1","_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"_server":null,"_sockname":null,"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":185,"pendingcb":1,"writelen":185},"allowHalfOpen":false,"autoSelectFamilyAttemptedAddresses":["::1:8081","127.0.0.1:8081"],"connecting":false,"parser":null,"server":null,"timeout":5000}]},"totalSocketCount":1},"maxHeaderSize":16384},"https:":{"globalAgent":{"_events":{},"_eventsCount":2,"_sessionCache":{"list":[],"map":{}},"defaultPort":443,"freeSockets":{},"keepAlive":true,"keepAliveMsecs":1000,"maxCachedSessions":100,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"https:","requests":{},"scheduling":"lifo","sockets":{},"totalSocketCount":0}}},"path":"/health","pathname":"/health","port":"8081","protocol":"http:"},"_redirectCount":0,"_redirects":[],"_requestBodyBuffers":[],"_requestBodyLength":0,"_timeout":null,"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0}},"stack":"AggregateError\n    at Function.AxiosError.from (D:\\A-CLAUDE-BIGMODEL20250730\\node_modules\\axios\\lib\\core\\AxiosError.js:92:14)\n    at RedirectableRequest.handleRequestError (D:\\A-CLAUDE-BIGMODEL20250730\\node_modules\\axios\\lib\\adapters\\http.js:620:25)\n    at RedirectableRequest.emit (node:events:530:35)\n    at RedirectableRequest.emit (node:domain:489:12)\n    at ClientRequest.eventHandlers.<computed> (D:\\A-CLAUDE-BIGMODEL20250730\\node_modules\\follow-redirects\\index.js:49:24)\n    at ClientRequest.emit (node:events:518:28)\n    at ClientRequest.emit (node:domain:489:12)\n    at emitErrorEvent (node:_http_client:104:11)\n    at Socket.socketErrorListener (node:_http_client:518:5)\n    at Socket.emit (node:events:518:28)\n    at Axios.request (D:\\A-CLAUDE-BIGMODEL20250730\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async RPAConnection.testConnection (D:\\A-CLAUDE-BIGMODEL20250730\\src\\core\\connection\\engine-detector.ts:91:24)\n    at async RPAEngineDetector.detectYingdaoEngine (D:\\A-CLAUDE-BIGMODEL20250730\\src\\core\\connection\\engine-detector.ts:38:29)\n    at async RPAEngineDetector.detectLocalEngines (D:\\A-CLAUDE-BIGMODEL20250730\\src\\core\\connection\\engine-detector.ts:14:29)\n    at async RPAConnectionManager.initialize (D:\\A-CLAUDE-BIGMODEL20250730\\src\\core\\connection\\connection-manager.ts:27:28)\n    at async RPAEngine.initialize (D:\\A-CLAUDE-BIGMODEL20250730\\src\\core\\index.ts:24:7)\n    at async main (D:\\A-CLAUDE-BIGMODEL20250730\\src\\index.ts:12:5)","timestamp":"2025-07-31T00:53:28.136Z"}
{"cause":{"code":"ECONNREFUSED"},"code":"ECONNREFUSED","config":{"adapter":["xhr","http","fetch"],"allowAbsoluteUrls":true,"env":{},"headers":{"Accept":"application/json, text/plain, */*","Accept-Encoding":"gzip, compress, deflate, br","User-Agent":"axios/1.11.0"},"maxBodyLength":-1,"maxContentLength":-1,"method":"get","timeout":5000,"transformRequest":[null],"transformResponse":[null],"transitional":{"clarifyTimeoutError":false,"forcedJSONParsing":true,"silentJSONParsing":true},"url":"http://localhost:9090/health","xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN"},"errors":[{"address":"::1","code":"ECONNREFUSED","errno":-4078,"port":9090,"syscall":"connect"},{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"port":9090,"syscall":"connect"}],"level":"error","message":"Connection test failed:","module":"RPAConnection","name":"AggregateError","request":{"_currentRequest":{"_closed":false,"_contentLength":0,"_defaultKeepAlive":true,"_ended":false,"_events":{},"_eventsCount":7,"_hasBody":true,"_header":"GET /health HTTP/1.1\r\nAccept: application/json, text/plain, */*\r\nUser-Agent: axios/1.11.0\r\nAccept-Encoding: gzip, compress, deflate, br\r\nHost: localhost:9090\r\nConnection: keep-alive\r\n\r\n","_headerSent":true,"_keepAliveTimeout":0,"_last":true,"_redirectable":"[Circular]","_removedConnection":false,"_removedContLen":false,"_removedTE":false,"_trailer":"","aborted":false,"agent":{"_events":{},"_eventsCount":2,"defaultPort":80,"freeSockets":{},"keepAlive":true,"keepAliveMsecs":1000,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"http:","requests":{},"scheduling":"lifo","sockets":{"localhost:9090:":[{"_closeAfterHandlingError":false,"_events":{"close":[null,null,null],"connect":[null,null,null],"timeout":[null,null,null]},"_eventsCount":8,"_hadError":true,"_host":"localhost","_httpMessage":"[Circular]","_parent":null,"_pendingData":"GET /health HTTP/1.1\r\nAccept: application/json, text/plain, */*\r\nUser-Agent: axios/1.11.0\r\nAccept-Encoding: gzip, compress, deflate, br\r\nHost: localhost:9090\r\nConnection: keep-alive\r\n\r\n","_pendingEncoding":"latin1","_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"_server":null,"_sockname":null,"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":185,"pendingcb":1,"writelen":185},"allowHalfOpen":false,"autoSelectFamilyAttemptedAddresses":["::1:9090","127.0.0.1:9090"],"connecting":false,"parser":null,"server":null,"timeout":5000}]},"totalSocketCount":1},"chunkedEncoding":false,"destroyed":false,"finished":true,"host":"localhost","maxHeadersCount":null,"maxRequestsOnConnectionReached":false,"method":"GET","outputData":[],"outputSize":0,"parser":null,"path":"/health","protocol":"http:","res":null,"reusedSocket":false,"sendDate":false,"shouldKeepAlive":true,"strictContentLength":false,"upgradeOrConnect":false,"useChunkedEncodingByDefault":false,"writable":true},"_currentUrl":"http://localhost:9090/health","_ended":true,"_ending":true,"_events":{"socket":[null,null]},"_eventsCount":3,"_options":{"agents":{},"beforeRedirects":{},"headers":{"Accept":"application/json, text/plain, */*","Accept-Encoding":"gzip, compress, deflate, br","User-Agent":"axios/1.11.0"},"hostname":"localhost","maxBodyLength":null,"maxRedirects":21,"method":"GET","nativeProtocols":{"http:":{"METHODS":["ACL","BIND","CHECKOUT","CONNECT","COPY","DELETE","GET","HEAD","LINK","LOCK","M-SEARCH","MERGE","MKACTIVITY","MKCALENDAR","MKCOL","MOVE","NOTIFY","OPTIONS","PATCH","POST","PROPFIND","PROPPATCH","PURGE","PUT","QUERY","REBIND","REPORT","SEARCH","SOURCE","SUBSCRIBE","TRACE","UNBIND","UNLINK","UNLOCK","UNSUBSCRIBE"],"STATUS_CODES":{"100":"Continue","101":"Switching Protocols","102":"Processing","103":"Early Hints","200":"OK","201":"Created","202":"Accepted","203":"Non-Authoritative Information","204":"No Content","205":"Reset Content","206":"Partial Content","207":"Multi-Status","208":"Already Reported","226":"IM Used","300":"Multiple Choices","301":"Moved Permanently","302":"Found","303":"See Other","304":"Not Modified","305":"Use Proxy","307":"Temporary Redirect","308":"Permanent Redirect","400":"Bad Request","401":"Unauthorized","402":"Payment Required","403":"Forbidden","404":"Not Found","405":"Method Not Allowed","406":"Not Acceptable","407":"Proxy Authentication Required","408":"Request Timeout","409":"Conflict","410":"Gone","411":"Length Required","412":"Precondition Failed","413":"Payload Too Large","414":"URI Too Long","415":"Unsupported Media Type","416":"Range Not Satisfiable","417":"Expectation Failed","418":"I'm a Teapot","421":"Misdirected Request","422":"Unprocessable Entity","423":"Locked","424":"Failed Dependency","425":"Too Early","426":"Upgrade Required","428":"Precondition Required","429":"Too Many Requests","431":"Request Header Fields Too Large","451":"Unavailable For Legal Reasons","500":"Internal Server Error","501":"Not Implemented","502":"Bad Gateway","503":"Service Unavailable","504":"Gateway Timeout","505":"HTTP Version Not Supported","506":"Variant Also Negotiates","507":"Insufficient Storage","508":"Loop Detected","509":"Bandwidth Limit Exceeded","510":"Not Extended","511":"Network Authentication Required"},"globalAgent":{"_events":{},"_eventsCount":2,"defaultPort":80,"freeSockets":{},"keepAlive":true,"keepAliveMsecs":1000,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"http:","requests":{},"scheduling":"lifo","sockets":{"localhost:9090:":[{"_closeAfterHandlingError":false,"_events":{"close":[null,null,null],"connect":[null,null,null],"timeout":[null,null,null]},"_eventsCount":8,"_hadError":true,"_host":"localhost","_httpMessage":{"_closed":false,"_contentLength":0,"_defaultKeepAlive":true,"_ended":false,"_events":{},"_eventsCount":7,"_hasBody":true,"_header":"GET /health HTTP/1.1\r\nAccept: application/json, text/plain, */*\r\nUser-Agent: axios/1.11.0\r\nAccept-Encoding: gzip, compress, deflate, br\r\nHost: localhost:9090\r\nConnection: keep-alive\r\n\r\n","_headerSent":true,"_keepAliveTimeout":0,"_last":true,"_redirectable":"[Circular]","_removedConnection":false,"_removedContLen":false,"_removedTE":false,"_trailer":"","aborted":false,"agent":"[Circular]","chunkedEncoding":false,"destroyed":false,"finished":true,"host":"localhost","maxHeadersCount":null,"maxRequestsOnConnectionReached":false,"method":"GET","outputData":[],"outputSize":0,"parser":null,"path":"/health","protocol":"http:","res":null,"reusedSocket":false,"sendDate":false,"shouldKeepAlive":true,"strictContentLength":false,"upgradeOrConnect":false,"useChunkedEncodingByDefault":false,"writable":true},"_parent":null,"_pendingData":"GET /health HTTP/1.1\r\nAccept: application/json, text/plain, */*\r\nUser-Agent: axios/1.11.0\r\nAccept-Encoding: gzip, compress, deflate, br\r\nHost: localhost:9090\r\nConnection: keep-alive\r\n\r\n","_pendingEncoding":"latin1","_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"_server":null,"_sockname":null,"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":185,"pendingcb":1,"writelen":185},"allowHalfOpen":false,"autoSelectFamilyAttemptedAddresses":["::1:9090","127.0.0.1:9090"],"connecting":false,"parser":null,"server":null,"timeout":5000}]},"totalSocketCount":1},"maxHeaderSize":16384},"https:":{"globalAgent":{"_events":{},"_eventsCount":2,"_sessionCache":{"list":[],"map":{}},"defaultPort":443,"freeSockets":{},"keepAlive":true,"keepAliveMsecs":1000,"maxCachedSessions":100,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"https:","requests":{},"scheduling":"lifo","sockets":{},"totalSocketCount":0}}},"path":"/health","pathname":"/health","port":"9090","protocol":"http:"},"_redirectCount":0,"_redirects":[],"_requestBodyBuffers":[],"_requestBodyLength":0,"_timeout":null,"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0}},"stack":"AggregateError\n    at Function.AxiosError.from (D:\\A-CLAUDE-BIGMODEL20250730\\node_modules\\axios\\lib\\core\\AxiosError.js:92:14)\n    at RedirectableRequest.handleRequestError (D:\\A-CLAUDE-BIGMODEL20250730\\node_modules\\axios\\lib\\adapters\\http.js:620:25)\n    at RedirectableRequest.emit (node:events:530:35)\n    at RedirectableRequest.emit (node:domain:489:12)\n    at ClientRequest.eventHandlers.<computed> (D:\\A-CLAUDE-BIGMODEL20250730\\node_modules\\follow-redirects\\index.js:49:24)\n    at ClientRequest.emit (node:events:518:28)\n    at ClientRequest.emit (node:domain:489:12)\n    at emitErrorEvent (node:_http_client:104:11)\n    at Socket.socketErrorListener (node:_http_client:518:5)\n    at Socket.emit (node:events:518:28)\n    at Axios.request (D:\\A-CLAUDE-BIGMODEL20250730\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async RPAConnection.testConnection (D:\\A-CLAUDE-BIGMODEL20250730\\src\\core\\connection\\engine-detector.ts:91:24)\n    at async RPAEngineDetector.detectYingdaoEngine (D:\\A-CLAUDE-BIGMODEL20250730\\src\\core\\connection\\engine-detector.ts:38:29)\n    at async RPAEngineDetector.detectLocalEngines (D:\\A-CLAUDE-BIGMODEL20250730\\src\\core\\connection\\engine-detector.ts:14:29)\n    at async RPAConnectionManager.initialize (D:\\A-CLAUDE-BIGMODEL20250730\\src\\core\\connection\\connection-manager.ts:27:28)\n    at async RPAEngine.initialize (D:\\A-CLAUDE-BIGMODEL20250730\\src\\core\\index.ts:24:7)\n    at async main (D:\\A-CLAUDE-BIGMODEL20250730\\src\\index.ts:12:5)","timestamp":"2025-07-31T00:53:28.141Z"}
{"cause":{"code":"ECONNREFUSED"},"code":"ECONNREFUSED","config":{"adapter":["xhr","http","fetch"],"allowAbsoluteUrls":true,"env":{},"headers":{"Accept":"application/json, text/plain, */*","Accept-Encoding":"gzip, compress, deflate, br","User-Agent":"axios/1.11.0"},"maxBodyLength":-1,"maxContentLength":-1,"method":"get","timeout":5000,"transformRequest":[null],"transformResponse":[null],"transitional":{"clarifyTimeoutError":false,"forcedJSONParsing":true,"silentJSONParsing":true},"url":"http://localhost:9091/health","xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN"},"errors":[{"address":"::1","code":"ECONNREFUSED","errno":-4078,"port":9091,"syscall":"connect"},{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"port":9091,"syscall":"connect"}],"level":"error","message":"Connection test failed:","module":"RPAConnection","name":"AggregateError","request":{"_currentRequest":{"_closed":false,"_contentLength":0,"_defaultKeepAlive":true,"_ended":false,"_events":{},"_eventsCount":7,"_hasBody":true,"_header":"GET /health HTTP/1.1\r\nAccept: application/json, text/plain, */*\r\nUser-Agent: axios/1.11.0\r\nAccept-Encoding: gzip, compress, deflate, br\r\nHost: localhost:9091\r\nConnection: keep-alive\r\n\r\n","_headerSent":true,"_keepAliveTimeout":0,"_last":true,"_redirectable":"[Circular]","_removedConnection":false,"_removedContLen":false,"_removedTE":false,"_trailer":"","aborted":false,"agent":{"_events":{},"_eventsCount":2,"defaultPort":80,"freeSockets":{},"keepAlive":true,"keepAliveMsecs":1000,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"http:","requests":{},"scheduling":"lifo","sockets":{"localhost:9091:":[{"_closeAfterHandlingError":false,"_events":{"close":[null,null,null],"connect":[null,null,null],"timeout":[null,null,null]},"_eventsCount":8,"_hadError":true,"_host":"localhost","_httpMessage":"[Circular]","_parent":null,"_pendingData":"GET /health HTTP/1.1\r\nAccept: application/json, text/plain, */*\r\nUser-Agent: axios/1.11.0\r\nAccept-Encoding: gzip, compress, deflate, br\r\nHost: localhost:9091\r\nConnection: keep-alive\r\n\r\n","_pendingEncoding":"latin1","_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"_server":null,"_sockname":null,"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":185,"pendingcb":1,"writelen":185},"allowHalfOpen":false,"autoSelectFamilyAttemptedAddresses":["::1:9091","127.0.0.1:9091"],"connecting":false,"parser":null,"server":null,"timeout":5000}]},"totalSocketCount":1},"chunkedEncoding":false,"destroyed":false,"finished":true,"host":"localhost","maxHeadersCount":null,"maxRequestsOnConnectionReached":false,"method":"GET","outputData":[],"outputSize":0,"parser":null,"path":"/health","protocol":"http:","res":null,"reusedSocket":false,"sendDate":false,"shouldKeepAlive":true,"strictContentLength":false,"upgradeOrConnect":false,"useChunkedEncodingByDefault":false,"writable":true},"_currentUrl":"http://localhost:9091/health","_ended":true,"_ending":true,"_events":{"socket":[null,null]},"_eventsCount":3,"_options":{"agents":{},"beforeRedirects":{},"headers":{"Accept":"application/json, text/plain, */*","Accept-Encoding":"gzip, compress, deflate, br","User-Agent":"axios/1.11.0"},"hostname":"localhost","maxBodyLength":null,"maxRedirects":21,"method":"GET","nativeProtocols":{"http:":{"METHODS":["ACL","BIND","CHECKOUT","CONNECT","COPY","DELETE","GET","HEAD","LINK","LOCK","M-SEARCH","MERGE","MKACTIVITY","MKCALENDAR","MKCOL","MOVE","NOTIFY","OPTIONS","PATCH","POST","PROPFIND","PROPPATCH","PURGE","PUT","QUERY","REBIND","REPORT","SEARCH","SOURCE","SUBSCRIBE","TRACE","UNBIND","UNLINK","UNLOCK","UNSUBSCRIBE"],"STATUS_CODES":{"100":"Continue","101":"Switching Protocols","102":"Processing","103":"Early Hints","200":"OK","201":"Created","202":"Accepted","203":"Non-Authoritative Information","204":"No Content","205":"Reset Content","206":"Partial Content","207":"Multi-Status","208":"Already Reported","226":"IM Used","300":"Multiple Choices","301":"Moved Permanently","302":"Found","303":"See Other","304":"Not Modified","305":"Use Proxy","307":"Temporary Redirect","308":"Permanent Redirect","400":"Bad Request","401":"Unauthorized","402":"Payment Required","403":"Forbidden","404":"Not Found","405":"Method Not Allowed","406":"Not Acceptable","407":"Proxy Authentication Required","408":"Request Timeout","409":"Conflict","410":"Gone","411":"Length Required","412":"Precondition Failed","413":"Payload Too Large","414":"URI Too Long","415":"Unsupported Media Type","416":"Range Not Satisfiable","417":"Expectation Failed","418":"I'm a Teapot","421":"Misdirected Request","422":"Unprocessable Entity","423":"Locked","424":"Failed Dependency","425":"Too Early","426":"Upgrade Required","428":"Precondition Required","429":"Too Many Requests","431":"Request Header Fields Too Large","451":"Unavailable For Legal Reasons","500":"Internal Server Error","501":"Not Implemented","502":"Bad Gateway","503":"Service Unavailable","504":"Gateway Timeout","505":"HTTP Version Not Supported","506":"Variant Also Negotiates","507":"Insufficient Storage","508":"Loop Detected","509":"Bandwidth Limit Exceeded","510":"Not Extended","511":"Network Authentication Required"},"globalAgent":{"_events":{},"_eventsCount":2,"defaultPort":80,"freeSockets":{},"keepAlive":true,"keepAliveMsecs":1000,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"http:","requests":{},"scheduling":"lifo","sockets":{"localhost:9091:":[{"_closeAfterHandlingError":false,"_events":{"close":[null,null,null],"connect":[null,null,null],"timeout":[null,null,null]},"_eventsCount":8,"_hadError":true,"_host":"localhost","_httpMessage":{"_closed":false,"_contentLength":0,"_defaultKeepAlive":true,"_ended":false,"_events":{},"_eventsCount":7,"_hasBody":true,"_header":"GET /health HTTP/1.1\r\nAccept: application/json, text/plain, */*\r\nUser-Agent: axios/1.11.0\r\nAccept-Encoding: gzip, compress, deflate, br\r\nHost: localhost:9091\r\nConnection: keep-alive\r\n\r\n","_headerSent":true,"_keepAliveTimeout":0,"_last":true,"_redirectable":"[Circular]","_removedConnection":false,"_removedContLen":false,"_removedTE":false,"_trailer":"","aborted":false,"agent":"[Circular]","chunkedEncoding":false,"destroyed":false,"finished":true,"host":"localhost","maxHeadersCount":null,"maxRequestsOnConnectionReached":false,"method":"GET","outputData":[],"outputSize":0,"parser":null,"path":"/health","protocol":"http:","res":null,"reusedSocket":false,"sendDate":false,"shouldKeepAlive":true,"strictContentLength":false,"upgradeOrConnect":false,"useChunkedEncodingByDefault":false,"writable":true},"_parent":null,"_pendingData":"GET /health HTTP/1.1\r\nAccept: application/json, text/plain, */*\r\nUser-Agent: axios/1.11.0\r\nAccept-Encoding: gzip, compress, deflate, br\r\nHost: localhost:9091\r\nConnection: keep-alive\r\n\r\n","_pendingEncoding":"latin1","_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"_server":null,"_sockname":null,"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":185,"pendingcb":1,"writelen":185},"allowHalfOpen":false,"autoSelectFamilyAttemptedAddresses":["::1:9091","127.0.0.1:9091"],"connecting":false,"parser":null,"server":null,"timeout":5000}]},"totalSocketCount":1},"maxHeaderSize":16384},"https:":{"globalAgent":{"_events":{},"_eventsCount":2,"_sessionCache":{"list":[],"map":{}},"defaultPort":443,"freeSockets":{},"keepAlive":true,"keepAliveMsecs":1000,"maxCachedSessions":100,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"https:","requests":{},"scheduling":"lifo","sockets":{},"totalSocketCount":0}}},"path":"/health","pathname":"/health","port":"9091","protocol":"http:"},"_redirectCount":0,"_redirects":[],"_requestBodyBuffers":[],"_requestBodyLength":0,"_timeout":null,"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0}},"stack":"AggregateError\n    at Function.AxiosError.from (D:\\A-CLAUDE-BIGMODEL20250730\\node_modules\\axios\\lib\\core\\AxiosError.js:92:14)\n    at RedirectableRequest.handleRequestError (D:\\A-CLAUDE-BIGMODEL20250730\\node_modules\\axios\\lib\\adapters\\http.js:620:25)\n    at RedirectableRequest.emit (node:events:530:35)\n    at RedirectableRequest.emit (node:domain:489:12)\n    at ClientRequest.eventHandlers.<computed> (D:\\A-CLAUDE-BIGMODEL20250730\\node_modules\\follow-redirects\\index.js:49:24)\n    at ClientRequest.emit (node:events:518:28)\n    at ClientRequest.emit (node:domain:489:12)\n    at emitErrorEvent (node:_http_client:104:11)\n    at Socket.socketErrorListener (node:_http_client:518:5)\n    at Socket.emit (node:events:518:28)\n    at Axios.request (D:\\A-CLAUDE-BIGMODEL20250730\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async RPAConnection.testConnection (D:\\A-CLAUDE-BIGMODEL20250730\\src\\core\\connection\\engine-detector.ts:91:24)\n    at async RPAEngineDetector.detectYingdaoEngine (D:\\A-CLAUDE-BIGMODEL20250730\\src\\core\\connection\\engine-detector.ts:38:29)\n    at async RPAEngineDetector.detectLocalEngines (D:\\A-CLAUDE-BIGMODEL20250730\\src\\core\\connection\\engine-detector.ts:14:29)\n    at async RPAConnectionManager.initialize (D:\\A-CLAUDE-BIGMODEL20250730\\src\\core\\connection\\connection-manager.ts:27:28)\n    at async RPAEngine.initialize (D:\\A-CLAUDE-BIGMODEL20250730\\src\\core\\index.ts:24:7)\n    at async main (D:\\A-CLAUDE-BIGMODEL20250730\\src\\index.ts:12:5)","timestamp":"2025-07-31T00:53:28.148Z"}
{"level":"info","message":"Initialized with 0 engines","module":"RPAConnectionManager","timestamp":"2025-07-31T00:53:28.149Z"}
{"level":"info","message":"RPA Engine initialized successfully","module":"RPAEngine","timestamp":"2025-07-31T00:53:28.149Z"}
{"level":"info","message":"Starting plugin activation...","module":"PluginActivator","timestamp":"2025-07-31T00:53:28.149Z"}
{"level":"info","message":"Starting plugin activation...","module":"PluginLifecycleManager","timestamp":"2025-07-31T00:53:28.150Z"}
{"level":"info","message":"Plugin activated successfully","module":"PluginLifecycleManager","timestamp":"2025-07-31T00:53:28.453Z"}
{"level":"info","message":"Initializing Cursor integration...","module":"CursorIntegration","timestamp":"2025-07-31T00:53:28.453Z"}
{"level":"info","message":"Plugin activated successfully","module":"PluginActivator","timestamp":"2025-07-31T00:53:28.454Z"}
{"level":"info","message":"Cursor RPA Integration started successfully","module":"main","timestamp":"2025-07-31T00:53:28.454Z"}
{"level":"info","message":"Cursor integration initialized","module":"CursorIntegration","timestamp":"2025-07-31T00:53:28.454Z"}
{"level":"info","message":"Shutting down...","module":"main","timestamp":"2025-07-31T00:53:33.456Z"}
{"level":"info","message":"Starting plugin deactivation...","module":"PluginActivator","timestamp":"2025-07-31T00:53:33.457Z"}
{"level":"info","message":"Disposing Cursor integration...","module":"CursorIntegration","timestamp":"2025-07-31T00:53:33.458Z"}
{"level":"info","message":"Starting plugin deactivation...","module":"PluginLifecycleManager","timestamp":"2025-07-31T00:53:33.459Z"}
{"level":"info","message":"Shutting down RPA Engine...","module":"RPAEngine","timestamp":"2025-07-31T00:53:33.460Z"}
{"level":"info","message":"Engine service started on port 8080","module":"EngineServiceManager","timestamp":"2025-07-31T00:59:39.689Z"}
{"level":"info","message":"Yingdao RPA Engine server started on port 8080","module":"YingdaoRPAServer","timestamp":"2025-07-31T00:59:39.689Z"}
{"level":"info","message":"Starting Cursor RPA Integration...","module":"main","timestamp":"2025-07-31T00:59:39.656Z"}
{"level":"info","message":"Starting Yingdao RPA Engine service...","module":"main","timestamp":"2025-07-31T00:59:39.660Z"}
{"level":"info","message":"Initializing RPA Engine...","module":"RPAEngine","timestamp":"2025-07-31T00:59:41.693Z"}
{"level":"info","message":"Initializing RPA connection manager...","module":"RPAConnectionManager","timestamp":"2025-07-31T00:59:41.694Z"}
{"level":"info","message":"Detecting local RPA engines...","module":"RPAEngineDetector","timestamp":"2025-07-31T00:59:41.694Z"}
{"level":"info","message":"Added engine: Yingdao RPA Engine (yingdao-8080)","module":"RPAConnectionManager","timestamp":"2025-07-31T00:59:42.089Z"}
{"level":"info","message":"RPA Engine initialized successfully","module":"RPAEngine","timestamp":"2025-07-31T00:59:42.089Z"}
{"level":"info","message":"Starting plugin activation...","module":"PluginActivator","timestamp":"2025-07-31T00:59:42.090Z"}
{"level":"info","message":"Starting plugin activation...","module":"PluginLifecycleManager","timestamp":"2025-07-31T00:59:42.090Z"}
{"level":"info","message":"Initialized with 1 engines","module":"RPAConnectionManager","timestamp":"2025-07-31T00:59:42.089Z"}
{"level":"info","message":"Plugin activated successfully","module":"PluginLifecycleManager","timestamp":"2025-07-31T00:59:42.250Z"}
{"level":"info","message":"Initializing Cursor integration...","module":"CursorIntegration","timestamp":"2025-07-31T00:59:42.251Z"}
{"level":"info","message":"Plugin activated successfully","module":"PluginActivator","timestamp":"2025-07-31T00:59:42.251Z"}
{"level":"info","message":"Cursor RPA Integration started successfully","module":"main","timestamp":"2025-07-31T00:59:42.252Z"}
{"level":"info","message":"Cursor integration initialized","module":"CursorIntegration","timestamp":"2025-07-31T00:59:42.251Z"}
{"level":"info","message":"Yingdao RPA Engine running on port 8080","module":"main","timestamp":"2025-07-31T00:59:42.252Z"}
{"level":"info","message":"Development timeout reached, shutting down...","module":"main","timestamp":"2025-07-31T01:00:12.255Z"}
{"level":"info","message":"Starting plugin deactivation...","module":"PluginActivator","timestamp":"2025-07-31T01:00:12.256Z"}
{"level":"info","message":"Disposing Cursor integration...","module":"CursorIntegration","timestamp":"2025-07-31T01:00:12.257Z"}
{"level":"info","message":"Starting plugin deactivation...","module":"PluginLifecycleManager","timestamp":"2025-07-31T01:00:12.258Z"}
{"level":"info","message":"Shutting down RPA Engine...","module":"RPAEngine","timestamp":"2025-07-31T01:00:12.258Z"}
{"level":"info","message":"Yingdao RPA Engine server stopped","module":"YingdaoRPAServer","timestamp":"2025-07-31T01:00:12.259Z"}
{"level":"info","message":"Engine service stopped","module":"EngineServiceManager","timestamp":"2025-07-31T01:00:12.259Z"}
{"level":"info","message":"Engine service started on port 8080","module":"EngineServiceManager","timestamp":"2025-07-31T01:00:23.581Z"}
{"level":"info","message":"Yingdao RPA Engine server started on port 8080","module":"YingdaoRPAServer","timestamp":"2025-07-31T01:00:23.581Z"}
{"level":"info","message":"Starting Yingdao RPA Engine service...","module":"EngineStarter","timestamp":"2025-07-31T01:00:23.547Z"}
{"level":"info","message":"Engine service started successfully on port 8080","module":"EngineStarter","timestamp":"2025-07-31T01:00:23.581Z"}
{"level":"info","message":"Engine is ready to accept connections","module":"EngineStarter","timestamp":"2025-07-31T01:00:23.584Z"}
{"level":"info","message":"Engine service is running. Press Ctrl+C to stop.","module":"EngineStarter","timestamp":"2025-07-31T01:00:23.584Z"}
{"level":"info","message":"Created process: Test Process (process_1753924329995_24bpvvcvp)","module":"YingdaoRPAServer","timestamp":"2025-07-31T01:12:09.995Z"}
{"level":"info","message":"Process execution completed: exec_1753924329999_uybvl3j4n","module":"YingdaoRPAServer","timestamp":"2025-07-31T01:12:18.548Z"}
