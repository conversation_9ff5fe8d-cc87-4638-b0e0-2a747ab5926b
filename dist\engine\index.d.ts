import { YingdaoRPAServer } from './services/yingdao-server';
export declare class EngineServiceManager {
    private server;
    private logger;
    private isRunning;
    constructor(port?: number);
    start(): Promise<void>;
    stop(): Promise<void>;
    isServiceRunning(): boolean;
    getServer(): YingdaoRPAServer;
    getPort(): number;
}
export declare const engineServiceManager: EngineServiceManager;
//# sourceMappingURL=index.d.ts.map