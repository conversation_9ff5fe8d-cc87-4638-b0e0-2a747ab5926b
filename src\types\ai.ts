export interface AIAssistant {
  id: string;
  name: string;
  type: 'design' | 'codegen' | 'debugger';
  capabilities: string[];
  model: string;
  config: AIModelConfig;
}

export interface AIModelConfig {
  apiKey?: string;
  baseUrl?: string;
  model: string;
  temperature: number;
  maxTokens: number;
  timeout: number;
}

export interface AIRequest {
  prompt: string;
  context?: any;
  options?: AIModelConfig;
}

export interface AIResponse {
  success: boolean;
  content?: string;
  error?: string;
  usage?: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };
}

export interface DesignSuggestion {
  type: 'workflow' | 'component' | 'optimization';
  title: string;
  description: string;
  code?: string;
  confidence: number;
}

export interface CodeSuggestion {
  text: string;
  position: {
    line: number;
    column: number;
  };
  type: 'completion' | 'refactoring' | 'fix';
  confidence: number;
}