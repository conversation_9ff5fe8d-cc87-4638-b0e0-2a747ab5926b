import { RPAProcess, RPAExecutionConfig, RPAExecutionResult } from '../../types';
import { RPAProcessExecutor } from './process-executor';
import { RPAConnectionManager } from '../connection';
import { createLogger } from '../../utils/logger';

export interface ProcessDefinition {
  id: string;
  name: string;
  description: string;
  version: string;
  author: string;
  created: Date;
  modified: Date;
  tags: string[];
  inputParameters: ProcessParameter[];
  outputParameters: ProcessParameter[];
  steps: ProcessStep[];
  dependencies?: string[];
}

export interface ProcessParameter {
  name: string;
  type: 'string' | 'number' | 'boolean' | 'object' | 'array';
  required: boolean;
  defaultValue?: any;
  description?: string;
}

export interface ProcessStep {
  id: string;
  name: string;
  type: 'action' | 'condition' | 'loop' | 'parallel';
  config: any;
  nextSteps: string[];
  errorHandling?: {
    retryCount: number;
    retryDelay: number;
    onFailure: 'continue' | 'stop' | 'goto';
    gotoStep?: string;
  };
}

export class RPAProcessManager {
  private executor: RPAProcessExecutor;
  private connectionManager: RPAConnectionManager;
  private logger = createLogger('RPAProcessManager');
  private processes: Map<string, RPAProcess> = new Map();
  private processDefinitions: Map<string, ProcessDefinition> = new Map();

  constructor(connectionManager: RPAConnectionManager) {
    this.executor = new RPAProcessExecutor();
    this.connectionManager = connectionManager;
    
    // Listen to execution events
    this.executor.on('execution', this.handleExecutionEvent.bind(this));
  }

  async createProcess(definition: ProcessDefinition): Promise<RPAProcess> {
    try {
      // Validate process definition
      this.validateProcessDefinition(definition);

      const process: RPAProcess = {
        id: definition.id,
        name: definition.name,
        description: definition.description,
        status: 'idle',
        variables: {},
        logs: []
      };

      this.processes.set(definition.id, process);
      this.processDefinitions.set(definition.id, definition);

      this.logger.info(`Created process: ${definition.name} (${definition.id})`);
      
      return process;
    } catch (error) {
      this.logger.error('Failed to create process:', error);
      throw error;
    }
  }

  async executeProcess(
    processId: string, 
    config: Partial<RPAExecutionConfig> = {}
  ): Promise<RPAExecutionResult> {
    try {
      const process = this.processes.get(processId);
      if (!process) {
        throw new Error(`Process not found: ${processId}`);
      }

      const definition = this.processDefinitions.get(processId);
      if (!definition) {
        throw new Error(`Process definition not found: ${processId}`);
      }

      // Check if engine is available
      const availableEngines = this.connectionManager.getAvailableEngines();
      if (availableEngines.length === 0) {
        throw new Error('No RPA engines available');
      }

      // Prepare execution configuration
      const executionConfig: RPAExecutionConfig = {
        engineId: availableEngines[0].id,
        processId,
        parameters: config.parameters || {},
        timeout: config.timeout || 300000,
        retryCount: config.retryCount || 0,
        debugMode: config.debugMode || false
      };

      this.logger.info(`Executing process: ${processId}`);

      // Execute the process
      const result = await this.executor.executeProcess(executionConfig, process);

      return result;
    } catch (error) {
      this.logger.error(`Failed to execute process ${processId}:`, error);
      throw error;
    }
  }

  async stopProcess(processId: string): Promise<boolean> {
    try {
      const activeExecutions = this.executor.getActiveExecutions();
      const execution = activeExecutions.find(exec => exec.processId === processId);
      
      if (execution) {
        return await this.executor.cancelExecution(execution.id);
      }

      return false;
    } catch (error) {
      this.logger.error(`Failed to stop process ${processId}:`, error);
      return false;
    }
  }

  getProcess(processId: string): RPAProcess | undefined {
    return this.processes.get(processId);
  }

  getProcessDefinition(processId: string): ProcessDefinition | undefined {
    return this.processDefinitions.get(processId);
  }

  getAllProcesses(): RPAProcess[] {
    return Array.from(this.processes.values());
  }

  getActiveProcesses(): RPAProcess[] {
    return this.getAllProcesses().filter(process => process.status === 'running');
  }

  async loadProcessFromFile(filePath: string): Promise<RPAProcess> {
    try {
      const fs = require('fs');
      const path = require('path');
      
      const content = fs.readFileSync(filePath, 'utf8');
      const definition = JSON.parse(content);
      
      return await this.createProcess(definition);
    } catch (error) {
      this.logger.error(`Failed to load process from ${filePath}:`, error);
      throw error;
    }
  }

  async saveProcessToFile(processId: string, filePath: string): Promise<void> {
    try {
      const definition = this.processDefinitions.get(processId);
      if (!definition) {
        throw new Error(`Process definition not found: ${processId}`);
      }

      const fs = require('fs');
      const path = require('path');
      
      fs.writeFileSync(filePath, JSON.stringify(definition, null, 2));
      
      this.logger.info(`Saved process ${processId} to ${filePath}`);
    } catch (error) {
      this.logger.error(`Failed to save process ${processId} to ${filePath}:`, error);
      throw error;
    }
  }

  async deleteProcess(processId: string): Promise<boolean> {
    try {
      const process = this.processes.get(processId);
      if (!process) {
        return false;
      }

      // Check if process is running
      if (process.status === 'running') {
        throw new Error('Cannot delete running process');
      }

      this.processes.delete(processId);
      this.processDefinitions.delete(processId);

      this.logger.info(`Deleted process: ${processId}`);
      return true;
    } catch (error) {
      this.logger.error(`Failed to delete process ${processId}:`, error);
      return false;
    }
  }

  getExecutionHistory(processId?: string): any[] {
    return this.executor.getExecutionHistory(processId);
  }

  getActiveExecutions(): any[] {
    return this.executor.getActiveExecutions();
  }

  private validateProcessDefinition(definition: ProcessDefinition): void {
    if (!definition.id || !definition.name) {
      throw new Error('Process definition must have id and name');
    }

    if (!definition.steps || definition.steps.length === 0) {
      throw new Error('Process must have at least one step');
    }

    // Validate step structure
    for (const step of definition.steps) {
      if (!step.id || !step.name || !step.type) {
        throw new Error('Each step must have id, name, and type');
      }
    }
  }

  private handleExecutionEvent(event: any): void {
    this.logger.debug('Execution event:', event);
    
    // Handle execution events
    switch (event.type) {
      case 'started':
        this.handleExecutionStarted(event);
        break;
      case 'completed':
        this.handleExecutionCompleted(event);
        break;
      case 'error':
        this.handleExecutionError(event);
        break;
      case 'cancelled':
        this.handleExecutionCancelled(event);
        break;
    }
  }

  private handleExecutionStarted(event: any): void {
    const process = this.processes.get(event.processId);
    if (process) {
      process.status = 'running';
      process.startTime = event.timestamp;
    }
  }

  private handleExecutionCompleted(event: any): void {
    const process = this.processes.get(event.processId);
    if (process) {
      process.status = 'completed';
      process.endTime = event.timestamp;
    }
  }

  private handleExecutionError(event: any): void {
    const process = this.processes.get(event.processId);
    if (process) {
      process.status = 'error';
      process.endTime = event.timestamp;
    }
  }

  private handleExecutionCancelled(event: any): void {
    const process = this.processes.get(event.processId);
    if (process) {
      process.status = 'idle';
      process.endTime = event.timestamp;
    }
  }
}