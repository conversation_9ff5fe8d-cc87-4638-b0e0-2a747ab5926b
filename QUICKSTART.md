# 影刀RPA引擎快速开始

## 🚀 5分钟快速上手

### 1. 安装依赖
```bash
npm install
```

### 2. 启动服务
```bash
npm run dev
```

### 3. 验证安装
```bash
curl http://localhost:8080/health
```

### 4. 创建第一个流程
```bash
curl -X POST http://localhost:8080/processes \
  -H "Content-Type: application/json" \
  -d '{"name": "我的第一个流程", "description": "测试流程"}'
```

### 5. 执行流程
```bash
curl -X POST http://localhost:8080/processes/{流程ID}/execute \
  -H "Content-Type: application/json" \
  -d '{"parameters": {"test": true}}'
```

## 📋 完整使用教程

- **[完整教程](./README.md)** - 详细的使用指南和配置说明
- **[API参考](./docs/API_REFERENCE.md)** - 完整的API文档
- **[最佳实践](./docs/BEST_PRACTICES.md)** - 开发最佳实践指南
- **[故障排除](./docs/TROUBLESHOOTING.md)** - 常见问题解决方案

## 🎯 核心功能

### ✅ 已实现功能
- **完整的HTTP API服务** - RESTful API支持
- **WebSocket实时通信** - 执行状态实时推送
- **流程管理** - 创建、读取、更新、删除流程
- **执行控制** - 启动、停止、监控流程执行
- **变量管理** - 动态管理流程变量
- **示例流程** - 3个预置示例流程

### 📡 主要API端点
- `GET /health` - 健康检查
- `GET /info` - 引擎信息
- `GET /processes` - 获取所有流程
- `POST /processes` - 创建流程
- `POST /processes/{id}/execute` - 执行流程
- `GET /executions/{id}` - 获取执行状态

## 🛠️ 开发工具

### 启动选项
```bash
npm run dev          # 完整集成模式
npm run dev:engine   # 仅引擎服务
npm run build        # 构建项目
npm test             # 运行测试
```

### 示例代码
- **JavaScript示例**: `examples/javascript/rpa-client.js`
- **Python示例**: `examples/python/rpa-client.py`
- **Shell脚本**: `examples/curl/rpa-client.sh`

## 🔧 配置说明

### 基础配置 (config.json)
```json
{
  "settings": {
    "rpaEngine": {
      "host": "localhost",
      "port": 8080,
      "timeout": 30000
    },
    "debug": {
      "enabled": false,
      "logLevel": "info"
    }
  }
}
```

### 环境变量 (.env)
```bash
RPA_ENGINE_HOST=localhost
RPA_ENGINE_PORT=8080
LOG_LEVEL=info
DEBUG_MODE=false
```

## 🎉 开始使用

1. **阅读教程**: [完整教程](./README.md)
2. **查看API**: [API参考](./docs/API_REFERENCE.md)
3. **运行示例**: 
   ```bash
   node examples/javascript/rpa-client.js
   python examples/python/rpa-client.py
   ./examples/curl/rpa-client.sh
   ```
4. **学习最佳实践**: [最佳实践](./docs/BEST_PRACTICES.md)

## 🤝 技术支持

- **问题排查**: [故障排除指南](./docs/TROUBLESHOOTING.md)
- **API测试**: `ts-node test-engine-api.ts`
- **健康检查**: `curl http://localhost:8080/health`

---

祝您使用愉快！🎉