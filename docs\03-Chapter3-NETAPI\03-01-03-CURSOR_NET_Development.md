# CURSOR+.NET开发

## 3.3.1 使用CURSOR生成C#代码

**CURSOR代码生成提示词：**
```
请生成一个AutoCAD .NET C#类，实现[具体功能描述]，要求：
1. 命名空间：AutoCAD.[模块名称]
2. 类名：[类名]
3. 命令方法：[命令列表]
4. 功能：[详细功能描述]
5. 异常处理：[异常处理要求]
6. 性能优化：[性能要求]
```

**代码生成示例：**
```csharp
// CURSOR提示词：
// 请生成一个AutoCAD .NET C#类，实现建筑墙体绘制功能，要求：
// 1. 命名空间：AutoCAD.Architecture
// 2. 类名：WallDrawingTools
// 3. 命令方法：DRAWWALL, DRAWWALLS, WALLSETTINGS
// 4. 功能：绘制单墙、批量绘制墙体、墙体参数设置
// 5. 异常处理：参数验证、AutoCAD异常处理
// 6. 性能优化：批量操作、事务处理

using Autodesk.AutoCAD.Runtime;
using Autodesk.AutoCAD.ApplicationServices;
using Autodesk.AutoCAD.DatabaseServices;
using Autodesk.AutoCAD.Geometry;
using Autodesk.AutoCAD.EditorInput;
using System;
using System.Collections.Generic;

namespace AutoCAD.Architecture
{
    public class WallDrawingTools
    {
        // 墙体参数
        private static double wallThickness = 240; // 墙体厚度(mm)
        private static double wallHeight = 3000;   // 墙体高度(mm)
        private static string wallLayer = "WALLS"; // 墙体图层
        
        [CommandMethod("WALLSETTINGS")]
        public void WallSettings()
        {
            Document doc = Application.DocumentManager.MdiActiveDocument;
            Editor ed = doc.Editor;
            
            try
            {
                // 获取墙体厚度
                PromptDoubleOptions thicknessOpt = new PromptDoubleOptions("\n请输入墙体厚度(mm): ");
                thicknessOpt.DefaultValue = wallThickness;
                thicknessOpt.AllowNegative = false;
                thicknessOpt.AllowZero = false;
                
                PromptDoubleResult thicknessResult = ed.GetDouble(thicknessOpt);
                if (thicknessResult.Status == PromptStatus.OK)
                {
                    wallThickness = thicknessResult.Value;
                }
                
                // 获取墙体高度
                PromptDoubleOptions heightOpt = new PromptDoubleOptions("\n请输入墙体高度(mm): ");
                heightOpt.DefaultValue = wallHeight;
                heightOpt.AllowNegative = false;
                heightOpt.AllowZero = false;
                
                PromptDoubleResult heightResult = ed.GetDouble(heightOpt);
                if (heightResult.Status == PromptStatus.OK)
                {
                    wallHeight = heightResult.Value;
                }
                
                // 获取墙体图层
                PromptStringOptions layerOpt = new PromptStringOptions("\n请输入墙体图层名称: ");
                layerOpt.DefaultValue = wallLayer;
                
                PromptResult layerResult = ed.GetString(layerOpt);
                if (layerResult.Status == PromptStatus.OK)
                {
                    wallLayer = layerResult.StringResult;
                }
                
                ed.WriteMessage($"\n墙体参数已更新：厚度={wallThickness}mm, 高度={wallHeight}mm, 图层={wallLayer}");
            }
            catch (System.Exception ex)
            {
                ed.WriteMessage($"\n设置墙体参数时出错：{ex.Message}");
            }
        }
        
        [CommandMethod("DRAWWALL")]
        public void DrawWall()
        {
            Document doc = Application.DocumentManager.MdiActiveDocument;
            Database db = doc.Database;
            Editor ed = doc.Editor;
            
            try
            {
                // 获取起点
                PromptPointOptions startOpt = new PromptPointOptions("\n请选择墙体起点: ");
                PromptPointResult startResult = ed.GetPoint(startOpt);
                if (startResult.Status != PromptStatus.OK)
                    return;
                
                // 获取终点
                PromptPointOptions endOpt = new PromptPointOptions("\n请选择墙体终点: ");
                PromptPointResult endResult = ed.GetPoint(endOpt);
                if (endResult.Status != PromptStatus.OK)
                    return;
                
                // 绘制墙体
                DrawSingleWall(db, startResult.Value, endResult.Value);
                
                ed.WriteMessage("\n墙体绘制完成！");
            }
            catch (System.Exception ex)
            {
                ed.WriteMessage($"\n绘制墙体时出错：{ex.Message}");
            }
        }
        
        [CommandMethod("DRAWWALLS")]
        public void DrawWalls()
        {
            Document doc = Application.DocumentManager.MdiActiveDocument;
            Database db = doc.Database;
            Editor ed = doc.Editor;
            
            try
            {
                // 获取墙体点
                PromptPointOptions ptOpt = new PromptPointOptions("\n请选择墙体点(Enter结束): ");
                List<Point3d> wallPoints = new List<Point3d>();
                
                while (true)
                {
                    PromptPointResult ptResult = ed.GetPoint(ptOpt);
                    if (ptResult.Status == PromptStatus.OK)
                    {
                        wallPoints.Add(ptResult.Value);
                        ptOpt.UseBasePoint = true;
                        ptOpt.BasePoint = ptResult.Value;
                        ptOpt.Message = "\n请选择下一个点: ";
                    }
                    else
                    {
                        break;
                    }
                }
                
                if (wallPoints.Count < 2)
                {
                    ed.WriteMessage("\n至少需要两个点才能绘制墙体！");
                    return;
                }
                
                // 批量绘制墙体
                using (Transaction trans = db.TransactionManager.StartTransaction())
                {
                    for (int i = 0; i < wallPoints.Count - 1; i++)
                    {
                        DrawSingleWallInTransaction(trans, db, wallPoints[i], wallPoints[i + 1]);
                    }
                    trans.Commit();
                }
                
                ed.WriteMessage($"\n成功绘制{wallPoints.Count - 1}段墙体！");
            }
            catch (System.Exception ex)
            {
                ed.WriteMessage($"\n批量绘制墙体时出错：{ex.Message}");
            }
        }
        
        private void DrawSingleWall(Database db, Point3d startPoint, Point3d endPoint)
        {
            using (Transaction trans = db.TransactionManager.StartTransaction())
            {
                DrawSingleWallInTransaction(trans, db, startPoint, endPoint);
                trans.Commit();
            }
        }
        
        private void DrawSingleWallInTransaction(Transaction trans, Database db, Point3d startPoint, Point3d endPoint)
        {
            // 创建墙体图层
            LayerTable lt = trans.GetObject(db.LayerTableId, OpenMode.ForWrite) as LayerTable;
            if (!lt.Has(wallLayer))
            {
                LayerTableRecord wallLtr = new LayerTableRecord();
                wallLtr.Name = wallLayer;
                wallLtr.Color = Color.FromColorIndex(ColorMethod.ByColor, 1);
                lt.Add(wallLtr);
                trans.AddNewlyCreatedDBObject(wallLtr, true);
            }
            
            // 获取模型空间
            BlockTable bt = trans.GetObject(db.BlockTableId, OpenMode.ForRead) as BlockTable;
            BlockTableRecord btr = trans.GetObject(bt[BlockTableRecord.ModelSpace], OpenMode.ForWrite) as BlockTableRecord;
            
            // 计算墙体方向
            Vector3d wallDirection = endPoint - startPoint;
            Vector3d normal = Vector3d.ZAxis;
            Vector3d wallNormal = wallDirection.GetPerpendicularVector();
            
            // 计算墙体边界点
            Point3d offset1 = startPoint + wallNormal * (wallThickness / 2);
            Point3d offset2 = startPoint - wallNormal * (wallThickness / 2);
            Point3d offset3 = endPoint - wallNormal * (wallThickness / 2);
            Point3d offset4 = endPoint + wallNormal * (wallThickness / 2);
            
            // 创建墙体多段线
            Polyline wall = new Polyline();
            wall.AddVertexAt(0, new Point2d(offset1.X, offset1.Y), 0, 0, 0);
            wall.AddVertexAt(1, new Point2d(offset2.X, offset2.Y), 0, 0, 0);
            wall.AddVertexAt(2, new Point2d(offset3.X, offset3.Y), 0, 0, 0);
            wall.AddVertexAt(3, new Point2d(offset4.X, offset4.Y), 0, 0, 0);
            wall.Closed = true;
            wall.Layer = wallLayer;
            wall.Color = Color.FromColorIndex(ColorMethod.ByColor, 1);
            
            btr.AppendEntity(wall);
            trans.AddNewlyCreatedDBObject(wall, true);
        }
    }
}
```

## 3.3.2 代码重构和优化

**CURSOR优化提示词：**
```
请优化以下AutoCAD .NET代码，提高性能和可维护性：
[原始代码]

优化要求：
1. 减少重复代码
2. 优化事务处理
3. 改善错误处理
4. 提高代码复用性
5. 添加性能监控
```

**优化示例：**
```csharp
// 原始代码存在的问题：
// 1. 重复的事务处理代码
// 2. 硬编码的参数
// 3. 缺乏统一的错误处理
// 4. 性能没有优化

// 优化后的代码
using Autodesk.AutoCAD.Runtime;
using Autodesk.AutoCAD.ApplicationServices;
using Autodesk.AutoCAD.DatabaseServices;
using Autodesk.AutoCAD.Geometry;
using Autodesk.AutoCAD.EditorInput;
using System;
using System.Collections.Generic;
using System.Diagnostics;

namespace AutoCAD.Architecture
{
    public class OptimizedWallDrawingTools
    {
        #region 私有字段
        
        private static WallParameters _wallParams = new WallParameters();
        private static PerformanceMonitor _performanceMonitor = new PerformanceMonitor();
        
        #endregion
        
        #region 命令方法
        
        [CommandMethod("OPTWALLSETTINGS")]
        public void OptWallSettings()
        {
            var context = new CommandContext();
            
            try
            {
                _performanceMonitor.Start();
                
                var settingsUI = new WallSettingsUI(_wallParams);
                if (settingsUI.ShowDialog() == System.Windows.Forms.DialogResult.OK)
                {
                    _wallParams = settingsUI.Parameters;
                    context.Editor.WriteMessage("\n墙体参数已更新！");
                }
            }
            catch (System.Exception ex)
            {
                context.HandleError(ex, "设置墙体参数");
            }
            finally
            {
                _performanceMonitor.Stop("墙体参数设置");
            }
        }
        
        [CommandMethod("OPTDRAWWALLS")]
        public void OptDrawWalls()
        {
            var context = new CommandContext();
            
            try
            {
                _performanceMonitor.Start();
                
                // 获取墙体点
                var wallPoints = GetWallPoints(context.Editor);
                if (wallPoints.Count < 2)
                {
                    context.Editor.WriteMessage("\n至少需要两个点才能绘制墙体！");
                    return;
                }
                
                // 批量绘制墙体
                var result = BatchDrawWalls(context.Database, wallPoints, _wallParams);
                
                context.Editor.WriteMessage($"\n成功绘制{result.WallCount}段墙体！");
                context.Editor.WriteMessage($"\n耗时：{result.ElapsedTime.TotalMilliseconds:F2}ms");
            }
            catch (System.Exception ex)
            {
                context.HandleError(ex, "批量绘制墙体");
            }
            finally
            {
                _performanceMonitor.Stop("批量绘制墙体");
            }
        }
        
        #endregion
        
        #region 私有方法
        
        private List<Point3d> GetWallPoints(Editor editor)
        {
            var points = new List<Point3d>();
            var ptOpt = new PromptPointOptions("\n请选择墙体点(Enter结束): ");
            
            while (true)
            {
                var ptResult = editor.GetPoint(ptOpt);
                if (ptResult.Status == PromptStatus.OK)
                {
                    points.Add(ptResult.Value);
                    ptOpt.UseBasePoint = true;
                    ptOpt.BasePoint = ptResult.Value;
                    ptOpt.Message = "\n请选择下一个点: ";
                }
                else
                {
                    break;
                }
            }
            
            return points;
        }
        
        private BatchDrawResult BatchDrawWalls(Database database, List<Point3d> points, WallParameters parameters)
        {
            var stopwatch = Stopwatch.StartNew();
            int wallCount = 0;
            
            using (var trans = database.TransactionManager.StartTransaction())
            {
                // 准备图层
                EnsureWallLayer(trans, database, parameters.LayerName);
                
                // 获取模型空间
                var btr = GetModelSpace(trans, database);
                
                // 批量绘制墙体
                for (int i = 0; i < points.Count - 1; i++)
                {
                    if (DrawWallSegment(trans, btr, points[i], points[i + 1], parameters))
                    {
                        wallCount++;
                    }
                }
                
                trans.Commit();
            }
            
            stopwatch.Stop();
            return new BatchDrawResult(wallCount, stopwatch.Elapsed);
        }
        
        private bool DrawWallSegment(Transaction trans, BlockTableRecord btr, Point3d start, Point3d end, WallParameters parameters)
        {
            try
            {
                var wall = CreateWallGeometry(start, end, parameters);
                btr.AppendEntity(wall);
                trans.AddNewlyCreatedDBObject(wall, true);
                return true;
            }
            catch
            {
                return false;
            }
        }
        
        private Polyline CreateWallGeometry(Point3d start, Point3d end, WallParameters parameters)
        {
            var wallDirection = end - start;
            var wallNormal = wallDirection.GetPerpendicularVector();
            
            var halfThickness = parameters.Thickness / 2;
            var offset1 = start + wallNormal * halfThickness;
            var offset2 = start - wallNormal * halfThickness;
            var offset3 = end - wallNormal * halfThickness;
            var offset4 = end + wallNormal * halfThickness;
            
            var wall = new Polyline();
            wall.AddVertexAt(0, new Point2d(offset1.X, offset1.Y), 0, 0, 0);
            wall.AddVertexAt(1, new Point2d(offset2.X, offset2.Y), 0, 0, 0);
            wall.AddVertexAt(2, new Point2d(offset3.X, offset3.Y), 0, 0, 0);
            wall.AddVertexAt(3, new Point2d(offset4.X, offset4.Y), 0, 0, 0);
            wall.Closed = true;
            wall.Layer = parameters.LayerName;
            wall.Color = parameters.Color;
            
            return wall;
        }
        
        private void EnsureWallLayer(Transaction trans, Database database, string layerName)
        {
            var lt = trans.GetObject(database.LayerTableId, OpenMode.ForWrite) as LayerTable;
            
            if (!lt.Has(layerName))
            {
                var layer = new LayerTableRecord
                {
                    Name = layerName,
                    Color = Color.FromColorIndex(ColorMethod.ByColor, 1),
                    LineWeight = LineWeight.LineWeight050
                };
                
                lt.Add(layer);
                trans.AddNewlyCreatedDBObject(layer, true);
            }
        }
        
        private BlockTableRecord GetModelSpace(Transaction trans, Database database)
        {
            var bt = trans.GetObject(database.BlockTableId, OpenMode.ForRead) as BlockTable;
            return trans.GetObject(bt[BlockTableRecord.ModelSpace], OpenMode.ForWrite) as BlockTableRecord;
        }
        
        #endregion
        
        #region 辅助类
        
        private class WallParameters
        {
            public double Thickness { get; set; } = 240;
            public double Height { get; set; } = 3000;
            public string LayerName { get; set; } = "WALLS";
            public Color Color { get; set; } = Color.FromColorIndex(ColorMethod.ByColor, 1);
        }
        
        private class BatchDrawResult
        {
            public int WallCount { get; }
            public TimeSpan ElapsedTime { get; }
            
            public BatchDrawResult(int wallCount, TimeSpan elapsedTime)
            {
                WallCount = wallCount;
                ElapsedTime = elapsedTime;
            }
        }
        
        private class CommandContext
        {
            public Document Document => Application.DocumentManager.MdiActiveDocument;
            public Database Database => Document.Database;
            public Editor Editor => Document.Editor;
            
            public void HandleError(Exception ex, string operation)
            {
                Editor.WriteMessage($"\n{operation}时出错：{ex.Message}");
                Debug.WriteLine($"Error in {operation}: {ex}");
            }
        }
        
        private class PerformanceMonitor
        {
            private Stopwatch _stopwatch;
            
            public void Start()
            {
                _stopwatch = Stopwatch.StartNew();
            }
            
            public void Stop(string operation)
            {
                _stopwatch?.Stop();
                Debug.WriteLine($"{operation} 耗时: {_stopwatch?.ElapsedMilliseconds}ms");
            }
        }
        
        #endregion
    }
}
```

## 3.3.3 异常处理和日志记录

**CURSOR异常处理提示词：**
```
请为以下AutoCAD .NET代码添加完整的异常处理和日志记录：
[原始代码]

要求：
1. 添加自定义异常类
2. 实现日志记录系统
3. 添加参数验证
4. 实现错误恢复机制
5. 添加调试信息
```

**异常处理和日志记录示例：**
```csharp
using Autodesk.AutoCAD.Runtime;
using Autodesk.AutoCAD.ApplicationServices;
using Autodesk.AutoCAD.DatabaseServices;
using Autodesk.AutoCAD.Geometry;
using Autodesk.AutoCAD.EditorInput;
using System;
using System.IO;

namespace AutoCAD.Architecture
{
    // 自定义异常类
    public class AutoCADPluginException : Exception
    {
        public enum ErrorType
        {
            InvalidParameter,
            DatabaseError,
            DrawingError,
            FileSystemError,
            UnknownError
        }
        
        public ErrorType Type { get; }
        public string CommandName { get; }
        
        public AutoCADPluginException(ErrorType type, string commandName, string message, Exception innerException = null)
            : base($"[{commandName}] {message}", innerException)
        {
            Type = type;
            CommandName = commandName;
        }
    }
    
    // 日志记录器
    public class PluginLogger
    {
        private static readonly string LogFilePath = Path.Combine(
            Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
            "AutoCADPlugin",
            "AutoCADPlugin.log");
        
        private static readonly object _lockObject = new object();
        
        public static void LogInfo(string commandName, string message)
        {
            Log("INFO", commandName, message);
        }
        
        public static void LogWarning(string commandName, string message)
        {
            Log("WARNING", commandName, message);
        }
        
        public static void LogError(string commandName, string message, Exception ex = null)
        {
            var logMessage = message;
            if (ex != null)
            {
                logMessage += $"\nException: {ex.GetType().Name}\nMessage: {ex.Message}\nStackTrace: {ex.StackTrace}";
            }
            Log("ERROR", commandName, logMessage);
        }
        
        public static void LogDebug(string commandName, string message)
        {
            #if DEBUG
            Log("DEBUG", commandName, message);
            #endif
        }
        
        private static void Log(string level, string commandName, string message)
        {
            lock (_lockObject)
            {
                try
                {
                    var logDirectory = Path.GetDirectoryName(LogFilePath);
                    if (!Directory.Exists(logDirectory))
                    {
                        Directory.CreateDirectory(logDirectory);
                    }
                    
                    var logEntry = $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] [{level}] [{commandName}] {message}";
                    
                    File.AppendAllText(LogFilePath, logEntry + Environment.NewLine);
                    
                    // 同时输出到AutoCAD命令行
                    var doc = Application.DocumentManager.MdiActiveDocument;
                    if (doc != null)
                    {
                        doc.Editor.WriteMessage($"\n{logEntry}");
                    }
                }
                catch
                {
                    // 日志记录失败时不影响主程序
                }
            }
        }
    }
    
    // 参数验证器
    public static class ParameterValidator
    {
        public static void ValidatePoint(Point3d point, string paramName)
        {
            if (point == null)
            {
                throw new AutoCADPluginException(
                    AutoCADPluginException.ErrorType.InvalidParameter,
                    "ParameterValidation",
                    $"{paramName} 不能为 null");
            }
            
            if (double.IsNaN(point.X) || double.IsNaN(point.Y) || double.IsNaN(point.Z))
            {
                throw new AutoCADPluginException(
                    AutoCADPluginException.ErrorType.InvalidParameter,
                    "ParameterValidation",
                    $"{paramName} 包含无效坐标值");
            }
        }
        
        public static void ValidatePositiveDouble(double value, string paramName, string commandName)
        {
            if (value <= 0)
            {
                throw new AutoCADPluginException(
                    AutoCADPluginException.ErrorType.InvalidParameter,
                    commandName,
                    $"{paramName} 必须大于0，当前值：{value}");
            }
            
            if (double.IsInfinity(value))
            {
                throw new AutoCADPluginException(
                    AutoCADPluginException.ErrorType.InvalidParameter,
                    commandName,
                    $"{paramName} 值过大，当前值：{value}");
            }
        }
        
        public static void ValidateString(string value, string paramName, string commandName)
        {
            if (string.IsNullOrWhiteSpace(value))
            {
                throw new AutoCADPluginException(
                    AutoCADPluginException.ErrorType.InvalidParameter,
                    commandName,
                    $"{paramName} 不能为空");
            }
        }
    }
    
    // 异常处理示例
    public class RobustWallDrawingTools
    {
        [CommandMethod("ROBUSTDRAWWALL")]
        public void RobustDrawWall()
        {
            const string commandName = "ROBUSTDRAWWALL";
            
            try
            {
                PluginLogger.LogInfo(commandName, "开始绘制墙体");
                
                var context = new CommandContext();
                var parameters = GetWallParameters(context, commandName);
                var wallPoints = GetWallPoints(context, commandName);
                
                if (wallPoints.Count < 2)
                {
                    throw new AutoCADPluginException(
                        AutoCADPluginException.ErrorType.InvalidParameter,
                        commandName,
                        "至少需要两个点才能绘制墙体");
                }
                
                var result = SafeDrawWalls(context.Database, wallPoints, parameters, commandName);
                
                context.Editor.WriteMessage($"\n成功绘制{result.WallCount}段墙体！");
                PluginLogger.LogInfo(commandName, $"墙体绘制完成，共{result.WallCount}段");
            }
            catch (AutoCADPluginException ex)
            {
                HandlePluginException(ex, commandName);
            }
            catch (System.Exception ex)
            {
                HandleGenericException(ex, commandName);
            }
        }
        
        private WallParameters GetWallParameters(CommandContext context, string commandName)
        {
            try
            {
                PluginLogger.LogDebug(commandName, "获取墙体参数");
                
                // 这里可以从配置文件、数据库或用户输入获取参数
                return new WallParameters
                {
                    Thickness = 240,
                    Height = 3000,
                    LayerName = "WALLS",
                    Color = Color.FromColorIndex(ColorMethod.ByColor, 1)
                };
            }
            catch (Exception ex)
            {
                throw new AutoCADPluginException(
                    AutoCADPluginException.ErrorType.InvalidParameter,
                    commandName,
                    "获取墙体参数失败",
                    ex);
            }
        }
        
        private List<Point3d> GetWallPoints(CommandContext context, string commandName)
        {
            var points = new List<Point3d>();
            
            try
            {
                PluginLogger.LogDebug(commandName, "获取墙体点");
                
                var ptOpt = new PromptPointOptions("\n请选择墙体点(Enter结束): ");
                
                while (true)
                {
                    var ptResult = context.Editor.GetPoint(ptOpt);
                    if (ptResult.Status == PromptStatus.OK)
                    {
                        ParameterValidator.ValidatePoint(ptResult.Value, "墙体点");
                        points.Add(ptResult.Value);
                        
                        ptOpt.UseBasePoint = true;
                        ptOpt.BasePoint = ptResult.Value;
                        ptOpt.Message = "\n请选择下一个点: ";
                    }
                    else
                    {
                        break;
                    }
                }
                
                PluginLogger.LogDebug(commandName, $"获取到{points.Count}个墙体点");
                return points;
            }
            catch (Exception ex)
            {
                throw new AutoCADPluginException(
                    AutoCADPluginException.ErrorType.InvalidParameter,
                    commandName,
                    "获取墙体点失败",
                    ex);
            }
        }
        
        private BatchDrawResult SafeDrawWalls(Database database, List<Point3d> points, WallParameters parameters, string commandName)
        {
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();
            int wallCount = 0;
            int errorCount = 0;
            
            try
            {
                PluginLogger.LogDebug(commandName, "开始批量绘制墙体");
                
                using (var trans = database.TransactionManager.StartTransaction())
                {
                    try
                    {
                        // 准备图层
                        EnsureWallLayer(trans, database, parameters.LayerName, commandName);
                        
                        // 获取模型空间
                        var btr = GetModelSpace(trans, database, commandName);
                        
                        // 批量绘制墙体
                        for (int i = 0; i < points.Count - 1; i++)
                        {
                            try
                            {
                                if (DrawWallSegmentSafely(trans, btr, points[i], points[i + 1], parameters, commandName))
                                {
                                    wallCount++;
                                    PluginLogger.LogDebug(commandName, $"成功绘制第{i+1}段墙体");
                                }
                                else
                                {
                                    errorCount++;
                                    PluginLogger.LogWarning(commandName, $"第{i+1}段墙体绘制失败");
                                }
                            }
                            catch (Exception ex)
                            {
                                errorCount++;
                                PluginLogger.LogError(commandName, $"第{i+1}段墙体绘制异常", ex);
                            }
                        }
                        
                        trans.Commit();
                    }
                    catch (Exception ex)
                    {
                        trans.Abort();
                        throw new AutoCADPluginException(
                            AutoCADPluginException.ErrorType.DatabaseError,
                            commandName,
                            "数据库操作失败",
                            ex);
                    }
                }
                
                stopwatch.Stop();
                var result = new BatchDrawResult(wallCount, errorCount, stopwatch.Elapsed);
                
                PluginLogger.LogInfo(commandName, 
                    $"批量绘制完成：成功{wallCount}段，失败{errorCount}段，耗时{result.ElapsedTime.TotalMilliseconds:F2}ms");
                
                return result;
            }
            catch (Exception ex)
            {
                throw new AutoCADPluginException(
                    AutoCADPluginException.ErrorType.DrawingError,
                    commandName,
                    "批量绘制墙体失败",
                    ex);
            }
        }
        
        private bool DrawWallSegmentSafely(Transaction trans, BlockTableRecord btr, Point3d start, Point3d end, WallParameters parameters, string commandName)
        {
            try
            {
                ParameterValidator.ValidatePoint(start, "起点");
                ParameterValidator.ValidatePoint(end, "终点");
                ParameterValidator.ValidatePositiveDouble(parameters.Thickness, "墙体厚度", commandName);
                
                var wall = CreateWallGeometry(start, end, parameters);
                btr.AppendEntity(wall);
                trans.AddNewlyCreatedDBObject(wall, true);
                
                return true;
            }
            catch (Exception ex)
            {
                PluginLogger.LogError(commandName, $"墙体段绘制失败：{start} -> {end}", ex);
                return false;
            }
        }
        
        private void HandlePluginException(AutoCADPluginException ex, string commandName)
        {
            var doc = Application.DocumentManager.MdiActiveDocument;
            if (doc != null)
            {
                doc.Editor.WriteMessage($"\n错误：{ex.Message}");
            }
            
            PluginLogger.LogError(commandName, ex.Message, ex);
        }
        
        private void HandleGenericException(Exception ex, string commandName)
        {
            var pluginEx = new AutoCADPluginException(
                AutoCADPluginException.ErrorType.UnknownError,
                commandName,
                "未知错误",
                ex);
            
            HandlePluginException(pluginEx, commandName);
        }
        
        // 其他辅助方法...
    }
}
```

---

**CURSOR提示词模板：**
```
请为AutoCAD .NET开发提供以下CURSOR辅助功能：
1. 代码生成：根据需求生成完整的C#类和方法
2. 代码优化：重构和优化现有代码
3. 异常处理：添加完整的错误处理和日志记录
4. 性能优化：提供性能监控和优化建议
```