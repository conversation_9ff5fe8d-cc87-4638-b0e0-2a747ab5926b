import { PluginConfig, PluginContext, PluginState } from '../../types';
import EventEmitter from 'events';
export interface ActivationEvent {
    type: 'activating' | 'activated' | 'deactivating' | 'deactivated' | 'error';
    timestamp: Date;
    error?: string;
}
export declare class PluginLifecycleManager extends EventEmitter {
    private config;
    private state;
    private logger;
    private activationPromise;
    constructor();
    activate(context?: Partial<PluginContext>): Promise<void>;
    private performActivation;
    deactivate(): Promise<void>;
    restart(context?: Partial<PluginContext>): Promise<void>;
    getState(): PluginState;
    getConfig(): PluginConfig;
    updateConfig(newConfig: Partial<PluginConfig>): Promise<void>;
    private validateDependencies;
    private initializeServices;
    private initializeDirectories;
    private initializeDatabase;
    private registerHooks;
    private unregisterHooks;
    private cleanupServices;
    private updateState;
    private emitEvent;
    isActive(): boolean;
    isActivating(): boolean;
    getActivationTime(): Date | undefined;
    getLastError(): string | undefined;
}
//# sourceMappingURL=lifecycle-manager.d.ts.map