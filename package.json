{"name": "cursor-rpa-integration", "version": "1.0.0", "description": "Cursor IDE integration with Yingdao RPA", "main": "dist/index.js", "scripts": {"build": "tsc", "dev": "ts-node src/index.ts", "dev:engine": "ts-node src/engine/start-engine.ts", "test": "jest", "lint": "eslint src --ext .ts", "start": "node dist/index.js", "start:engine": "node dist/engine/start-engine.js"}, "keywords": ["cursor", "rpa", "y<PERSON><PERSON>o", "automation"], "author": "", "license": "MIT", "dependencies": {"axios": "^1.6.0", "express": "^4.18.2", "ws": "^8.14.0", "sqlite3": "^5.1.6", "winston": "^3.11.0", "uuid": "^9.0.0", "joi": "^17.11.0"}, "devDependencies": {"@types/node": "^20.8.0", "@types/ws": "^8.5.0", "@types/uuid": "^9.0.0", "@types/express": "^4.17.17", "@typescript-eslint/eslint-plugin": "^6.7.0", "@typescript-eslint/parser": "^6.7.0", "eslint": "^8.50.0", "jest": "^29.7.0", "@types/jest": "^29.5.0", "ts-jest": "^29.1.0", "ts-node": "^10.9.0", "typescript": "^5.2.0"}}