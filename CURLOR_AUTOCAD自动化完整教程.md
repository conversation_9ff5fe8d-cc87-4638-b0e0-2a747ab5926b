# CURSOR+AUTOCAD自动化完整教程

## 目录
- [第一章：基础概念与环境搭建](#第一章基础概念与环境搭建)
- [第二章：AutoCAD基础自动化](#第二章autocad基础自动化)
- [第三章：.NET API高级开发](#第三章net-api高级开发)
- [第四章：实际项目案例](#第四章实际项目案例)
- [第五章：高级技术与最佳实践](#第五章高级技术与最佳实践)
- [第六章：标准化与质量控制](#第六章标准化与质量控制)
- [第七章：项目实战与案例分析](#第七章项目实战与案例分析)
- [第八章：进阶技术与未来趋势](#第八章进阶技术与未来趋势)

---

## 第一章：基础概念与环境搭建

### 1.1 CURSOR+AUTOCAD自动化概述

#### 1.1.1 什么是CURSOR+AUTOCAD自动化
CURSOR+AUTOCAD自动化是指利用AI编程助手CURSOR与AutoCAD的各种编程接口相结合，实现设计工作的自动化。这种自动化可以显著提高设计效率，减少重复性工作，确保设计质量的一致性。

**核心概念：**
- **AI辅助编程**：利用CURSOR的代码生成和优化能力
- **多接口支持**：AutoLISP、VBA、.NET API、ObjectARX
- **自动化流程**：从简单脚本到复杂的工作流程
- **标准化输出**：确保符合行业和企业标准

#### 1.1.2 自动化的优势和应用场景

**优势：**
- 效率提升：减少80%的重复性工作
- 质量保证：标准化设计，减少人为错误
- 成本节约：降低人力成本和时间成本
- 一致性：确保设计符合标准规范

**应用场景：**
- 建筑设计：标准图框生成、批量图纸处理
- 机械设计：标准件库管理、装配图生成
- 电气设计：电气符号库、接线图自动生成
- 土木工程：地形图处理、管线设计自动化

#### 1.1.3 学习路径和预期成果

**学习路径：**
1. **基础阶段**（1-2个月）：AutoLISP基础，简单脚本编写
2. **进阶阶段**（2-3个月）：VBA开发，用户界面设计
3. **高级阶段**（3-4个月）：.NET API开发，复杂应用开发
4. **专家阶段**（4-6个月）：完整解决方案开发，项目管理

**预期成果：**
- 能够独立开发AutoCAD自动化工具
- 掌握CURSOR辅助开发技巧
- 具备项目实施和管理能力
- 能够解决复杂的技术问题

### 1.2 开发环境准备

#### 1.2.1 AutoCAD版本选择与安装

**推荐版本：**
- AutoCAD 2024-2025（最新版本，功能最全）
- AutoCAD 2020-2023（稳定版本，兼容性好）
- AutoCAD LT（基础版本，适合简单自动化）

**安装步骤：**
```bash
# 1. 下载AutoCAD安装包
# 2. 运行安装程序
# 3. 选择自定义安装
# 4. 安装开发工具包
# 5. 配置许可和激活
```

**开发工具包：**
- ObjectARX SDK
- AutoCAD .NET API
- VBA IDE
- AutoLISP编辑器

#### 1.2.2 CURSOR编辑器配置

**CURSOR安装：**
```bash
# 下载并安装CURSOR编辑器
# 配置AutoCAD开发环境
# 安装必要的插件和扩展
```

**CURSOR配置文件：**
```json
{
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true
  },
  "languages": {
    "csharp": {
      "formatting": {
        "provider": "roslyn"
      }
    }
  },
  "autocomplete": {
    "enable": true,
    "suggestOnTriggerCharacters": true
  }
}
```

#### 1.2.3 开发工具链搭建

**Visual Studio配置：**
```csharp
// 创建Class Library项目
// 添加AutoCAD .NET API引用
// 配置调试设置
// 创建启动项目
```

**VS Code配置：**
```json
{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "AutoCAD Debug",
      "type": "clr",
      "request": "launch",
      "preLaunchTask": "build",
      "program": "${workspaceFolder}/bin/Debug/net48/YourPlugin.dll",
      "args": [],
      "cwd": "${workspaceFolder}",
      "stopAtEntry": false,
      "console": "internalConsole"
    }
  ]
}
```

#### 1.2.4 必要的插件和扩展

**推荐插件：**
- AutoCAD Tools for VS Code
- C# Extension
- AutoLISP Extension
- VBA Extension
- Git Extension

**扩展功能：**
- 代码高亮
- 智能提示
- 调试支持
- 版本控制

### 1.3 AutoCAD编程接口介绍

#### 1.3.1 AutoLISP基础

**AutoLISP语法：**
```lisp
; 基本语法示例
(defun hello-world ()
    (princ "Hello, World!")
    (princ)
)

; 变量定义
(setq x 10)
(setq y 20)
(setq z (+ x y))

; 函数定义
(defun calculate-area (width height)
    (* width height)
)

; 图形对象操作
(defun draw-line (start-point end-point)
    (command "line" start-point end-point "")
)
```

#### 1.3.2 VBA编程环境

**VBA基础语法：**
```vba
' 基本语法示例
Sub HelloWorld()
    MsgBox "Hello, World!"
End Sub

' 变量定义
Dim x As Integer
Dim y As Integer
Dim z As Integer

x = 10
y = 20
z = x + y

' AutoCAD对象操作
Sub DrawLine()
    Dim lineObj As AcadLine
    Dim startPoint(0 To 2) As Double
    Dim endPoint(0 To 2) As Double
    
    startPoint(0) = 0: startPoint(1) = 0: startPoint(2) = 0
    endPoint(0) = 100: endPoint(1) = 100: endPoint(2) = 0
    
    Set lineObj = ThisDrawing.ModelSpace.AddLine(startPoint, endPoint)
    ZoomAll
End Sub
```

#### 1.3.3 .NET API架构

**C#基础语法：**
```csharp
using Autodesk.AutoCAD.Runtime;
using Autodesk.AutoCAD.ApplicationServices;
using Autodesk.AutoCAD.DatabaseServices;
using Autodesk.AutoCAD.Geometry;

public class AutoCADCommands
{
    [CommandMethod("HelloWorld")]
    public void HelloWorld()
    {
        Application.DocumentManager.MdiActiveDocument.Editor.WriteMessage("Hello, World!");
    }

    [CommandMethod("DrawLine")]
    public void DrawLine()
    {
        Document doc = Application.DocumentManager.MdiActiveDocument;
        Database db = doc.Database;
        
        using (Transaction trans = db.TransactionManager.StartTransaction())
        {
            BlockTable bt = trans.GetObject(db.BlockTableId, OpenMode.ForRead) as BlockTable;
            BlockTableRecord btr = trans.GetObject(bt[BlockTableRecord.ModelSpace], OpenMode.ForWrite) as BlockTableRecord;
            
            Point3d startPoint = new Point3d(0, 0, 0);
            Point3d endPoint = new Point3d(100, 100, 0);
            
            Line line = new Line(startPoint, endPoint);
            btr.AppendEntity(line);
            trans.AddNewlyCreatedDBObject(line, true);
            
            trans.Commit();
        }
    }
}
```

#### 1.3.4 ObjectARX概述

**ObjectARX基础：**
```cpp
#include "aced.h"
#include "dbents.h"

// 命令定义
void HelloWorld()
{
    acutPrintf(_T("Hello, World!"));
}

void DrawLine()
{
    AcGePoint3d startPoint(0, 0, 0);
    AcGePoint3d endPoint(100, 100, 0);
    
    AcDbLine* line = new AcDbLine(startPoint, endPoint);
    
    AcDbBlockTable* pBlockTable;
    acdbHostApplicationServices()->workingDatabase()->getBlockTable(pBlockTable, AcDb::kForRead);
    
    AcDbBlockTableRecord* pBlockTableRecord;
    pBlockTable->getAt(ACDB_MODEL_SPACE, pBlockTableRecord, AcDb::kForWrite);
    
    pBlockTableRecord->appendAcDbEntity(line);
    pBlockTable->close();
    pBlockTableRecord->close();
    line->close();
}

// 入口点
extern "C" AcRx::AppRetCode acrxEntryPoint(AcRx::AppMsgCode msg, void* pkt)
{
    switch (msg)
    {
    case AcRx::kInitAppMsg:
        acrxDynamicLinker->unlockApplication(pkt);
        acrxRegisterAppMDIAware(pkt);
        acedRegCmds->addCommand("MY_COMMANDS", "HELLO", "HELLO", ACRX_CMD_MODAL, HelloWorld);
        acedRegCmds->addCommand("MY_COMMANDS", "DRAWLINE", "DRAWLINE", ACRX_CMD_MODAL, DrawLine);
        break;
    case AcRx::kUnloadAppMsg:
        acedRegCmds->removeGroup("MY_COMMANDS");
        break;
    }
    return AcRx::kRetOK;
}
```

### 1.4 专业可执行案例

#### 案例1：AutoCAD环境检测工具
**目标**：创建一个工具来检测当前AutoCAD环境配置

**AutoLISP版本：**
```lisp
(defun c:CheckEnvironment (/ version platform memory)
    (setq version (getvar "ACADVER"))
    (setq platform (getvar "PLATFORM"))
    (setq memory (getvar "MEMUSAGE"))
    
    (alert (strcat "AutoCAD版本: " version "\n"
                   "平台: " platform "\n"
                   "内存使用: " (itoa memory) "MB"))
    
    (princ)
)
```

**VBA版本：**
```vba
Sub CheckEnvironment()
    Dim version As String
    Dim platform As String
    Dim memory As Long
    
    version = ThisDrawing.GetVariable("ACADVER")
    platform = ThisDrawing.GetVariable("PLATFORM")
    memory = ThisDrawing.GetVariable("MEMUSAGE")
    
    MsgBox "AutoCAD版本: " & version & vbCrLf & _
           "平台: " & platform & vbCrLf & _
           "内存使用: " & memory & "MB", _
           vbInformation, "环境检测"
End Sub
```

**C#版本：**
```csharp
[CommandMethod("CheckEnvironment")]
public void CheckEnvironment()
{
    Document doc = Application.DocumentManager.MdiActiveDocument;
    Editor ed = doc.Editor;
    
    string version = Application.Version.ToString();
    string platform = System.Environment.OSVersion.ToString();
    long memory = System.Diagnostics.Process.GetCurrentProcess().WorkingSet64 / 1024 / 1024;
    
    ed.WriteMessage($"\nAutoCAD版本: {version}");
    ed.WriteMessage($"\n平台: {platform}");
    ed.WriteMessage($"\n内存使用: {memory}MB");
    
    Application.ShowAlertDialog($"AutoCAD版本: {version}\n平台: {platform}\n内存使用: {memory}MB");
}
```

#### 案例2：开发环境配置向导
**目标**：自动配置AutoCAD开发环境

**AutoLISP版本：**
```lisp
(defun c:SetupDevEnvironment (/ devpath supportpath)
    (setq devpath (getstring "输入开发路径: "))
    (setq supportpath (getvar "SUPPORTFILESEARCHPATH"))
    
    ; 添加开发路径到支持路径
    (setvar "SUPPORTFILESEARCHPATH" (strcat devpath ";" supportpath))
    
    ; 创建必要的目录
    (vl-mkdir (strcat devpath "\\LISP"))
    (vl-mkdir (strcat devpath "\\VBA"))
    (vl-mkdir (strcat devpath "\\NET"))
    
    ; 创建配置文件
    (setq configfile (open (strcat devpath "\\config.txt") "w"))
    (write-line "DevPath=" devpath configfile)
    (write-line "AutoCADVersion=" (getvar "ACADVER") configfile)
    (close configfile)
    
    (alert "开发环境配置完成!")
    (princ)
)
```

**VBA版本：**
```vba
Sub SetupDevEnvironment()
    Dim devPath As String
    Dim supportPath As String
    Dim fso As Object
    Dim configFile As Object
    
    devPath = InputBox("输入开发路径:", "开发环境配置")
    If devPath = "" Then Exit Sub
    
    supportPath = ThisDrawing.GetVariable("SUPPORTFILESEARCHPATH")
    
    ' 添加开发路径到支持路径
    ThisDrawing.SetVariable "SUPPORTFILESEARCHPATH", devPath & ";" & supportPath
    
    ' 创建必要的目录
    Set fso = CreateObject("Scripting.FileSystemObject")
    If Not fso.FolderExists(devPath & "\LISP") Then
        fso.CreateFolder devPath & "\LISP"
    End If
    If Not fso.FolderExists(devPath & "\VBA") Then
        fso.CreateFolder devPath & "\VBA"
    End If
    If Not fso.FolderExists(devPath & "\NET") Then
        fso.CreateFolder devPath & "\NET"
    End If
    
    ' 创建配置文件
    Set configFile = fso.CreateTextFile(devPath & "\config.txt", True)
    configFile.WriteLine "DevPath=" & devPath
    configFile.WriteLine "AutoCADVersion=" & ThisDrawing.GetVariable("ACADVER")
    configFile.Close
    
    MsgBox "开发环境配置完成!", vbInformation, "配置完成"
End Sub
```

**C#版本：**
```csharp
[CommandMethod("SetupDevEnvironment")]
public void SetupDevEnvironment()
{
    Document doc = Application.DocumentManager.MdiActiveDocument;
    Editor ed = doc.Editor;
    
    PromptStringOptions pso = new PromptStringOptions("\n输入开发路径: ");
    PromptResult pr = ed.GetString(pso);
    
    if (pr.Status != PromptStatus.OK) return;
    
    string devPath = pr.StringResult;
    string supportPath = Application.GetSystemVariable("SUPPORTFILESEARCHPATH").ToString();
    
    // 更新支持路径
    Application.SetSystemVariable("SUPPORTFILESEARCHPATH", devPath + ";" + supportPath);
    
    // 创建目录
    System.IO.Directory.CreateDirectory(devPath + "\\LISP");
    System.IO.Directory.CreateDirectory(devPath + "\\VBA");
    System.IO.Directory.CreateDirectory(devPath + "\\NET");
    
    // 创建配置文件
    string configContent = $"DevPath={devPath}\nAutoCADVersion={Application.Version}";
    System.IO.File.WriteAllText(devPath + "\\config.txt", configContent);
    
    ed.WriteMessage("\n开发环境配置完成!");
    Application.ShowAlertDialog("开发环境配置完成!");
}
```

#### 案例3：CURSOR助手配置工具
**目标**：配置CURSOR编辑器以优化AutoCAD开发

**配置脚本：**
```json
{
  "cursor.autocad.config": {
    "version": "1.0.0",
    "author": "CAD自动化专家",
    "description": "AutoCAD开发环境配置",
    "settings": {
      "editor.formatOnSave": true,
      "editor.codeActionsOnSave": {
        "source.fixAll.eslint": true
      },
      "languages": {
        "autolisp": {
          "formatting": {
            "provider": "autolisp-formatter"
          }
        },
        "vba": {
          "formatting": {
            "provider": "vba-beautifier"
          }
        },
        "csharp": {
          "formatting": {
            "provider": "roslyn"
          }
        }
      },
      "autocomplete": {
        "enable": true,
        "suggestOnTriggerCharacters": true,
        "autoImport": true
      },
      "debugger": {
        "enabled": true,
        "breakOnError": true
      }
    },
    "extensions": [
      {
        "name": "AutoCAD Tools",
        "id": "autocad-tools",
        "version": "1.0.0"
      },
      {
        "name": "AutoLISP Extension",
        "id": "autolisp-extension",
        "version": "2.0.0"
      },
      {
        "name": "VBA Extension",
        "id": "vba-extension",
        "version": "1.5.0"
      },
      {
        "name": "C# Extension",
        "id": "csharp-extension",
        "version": "1.0.0"
      }
    ],
    "snippets": {
      "autolisp": [
        {
          "prefix": "defun",
          "body": [
            "(defun c:${1:command} (${2:parameters})",
            "    ${3:; function body}",
            "    (princ)",
            ")"
          ],
          "description": "AutoLISP命令定义模板"
        }
      ],
      "vba": [
        {
          "prefix": "sub",
          "body": [
            "Sub ${1:SubName}()",
            "    ${2:; sub body}",
            "End Sub"
          ],
          "description": "VBA子过程模板"
        }
      ],
      "csharp": [
        {
          "prefix": "cmd",
          "body": [
            "[CommandMethod(\"${1:CommandName}\")]",
            "public void ${1:CommandName}()",
            "{",
            "    Document doc = Application.DocumentManager.MdiActiveDocument;",
            "    Editor ed = doc.Editor;",
            "    ${2:; command body}",
            "}"
          ],
          "description": "AutoCAD命令模板"
        }
      ]
    }
  }
}
```

**安装脚本：**
```csharp
[CommandMethod("InstallCursorConfig")]
public void InstallCursorConfig()
{
    Document doc = Application.DocumentManager.MdiActiveDocument;
    Editor ed = doc.Editor;
    
    try
    {
        string cursorPath = Environment.GetFolderPath(Environment.SpecialFolder.UserProfile) + "\\.cursor";
        string configPath = cursorPath + "\\user\\settings.json";
        
        // 创建目录
        System.IO.Directory.CreateDirectory(cursorPath + "\\user");
        
        // 读取现有配置
        string existingConfig = "";
        if (System.IO.File.Exists(configPath))
        {
            existingConfig = System.IO.File.ReadAllText(configPath);
        }
        
        // 合并配置
        string newConfig = MergeCursorConfig(existingConfig);
        
        // 写入配置文件
        System.IO.File.WriteAllText(configPath, newConfig);
        
        ed.WriteMessage("\nCURSOR配置安装完成!");
        ed.WriteMessage($"\n配置文件位置: {configPath}");
    }
    catch (System.Exception ex)
    {
        ed.WriteMessage($"\n错误: {ex.Message}");
    }
}

private string MergeCursorConfig(string existingConfig)
{
    // 这里实现配置合并逻辑
    // 简化示例，直接返回新配置
    return @"{
  ""editor.formatOnSave"": true,
  ""cursor.autocad"": {
    ""enabled"": true,
    ""version"": ""1.0.0""
  }
}";
}
```

#### 案例4：多版本AutoCAD检测器
**目标**：检测系统中安装的多个AutoCAD版本

**AutoLISP版本：**
```lisp
(defun c:DetectAutoCADVersions (/ versions registry)
    (setq versions '())
    
    ; 检查注册表中的AutoCAD版本
    (setq registry (vl-registry-read "HKEY_LOCAL_MACHINE\\SOFTWARE\\Autodesk\\AutoCAD"))
    
    (if registry
        (foreach key registry
            (if (and (stringp key)
                     (wcmatch key "R*"))
                (setq versions (cons key versions))
            )
        )
    )
    
    ; 显示结果
    (if versions
        (progn
            (alert (strcat "检测到 " (itoa (length versions)) " 个AutoCAD版本:\n"
                           (apply 'strcat (mapcar '(lambda (x) (strcat x "\n")) versions))))
        )
        (alert "未检测到AutoCAD安装")
    )
    
    (princ)
)
```

**VBA版本：**
```vba
Sub DetectAutoCADVersions()
    Dim versions As Collection
    Set versions = New Collection
    
    On Error Resume Next
    
    ' 检查注册表
    Dim shell As Object
    Set shell = CreateObject("WScript.Shell")
    
    Dim acadKey As String
    acadKey = "HKLM\SOFTWARE\Autodesk\AutoCAD\"
    
    Dim key As Variant
    For Each key In shell.RegRead(acadKey)
        If Left(key, 1) = "R" Then
            versions.Add key
        End If
    Next key
    
    ' 显示结果
    Dim result As String
    result = "检测到 " & versions.Count & " 个AutoCAD版本:" & vbCrLf
    
    Dim version As Variant
    For Each version In versions
        result = result & version & vbCrLf
    Next version
    
    MsgBox result, vbInformation, "AutoCAD版本检测"
End Sub
```

**C#版本：**
```csharp
[CommandMethod("DetectAutoCADVersions")]
public void DetectAutoCADVersions()
{
    Document doc = Application.DocumentManager.MdiActiveDocument;
    Editor ed = doc.Editor;
    
    try
    {
        List<string> versions = new List<string>();
        
        // 检查注册表
        using (Microsoft.Win32.RegistryKey acadKey = Microsoft.Win32.Registry.LocalMachine.OpenSubKey(@"SOFTWARE\Autodesk\AutoCAD"))
        {
            if (acadKey != null)
            {
                foreach (string subKeyName in acadKey.GetSubKeyNames())
                {
                    if (subKeyName.StartsWith("R"))
                    {
                        versions.Add(subKeyName);
                    }
                }
            }
        }
        
        // 显示结果
        if (versions.Count > 0)
        {
            ed.WriteMessage($"\n检测到 {versions.Count} 个AutoCAD版本:");
            foreach (string version in versions)
            {
                ed.WriteMessage($"\n  {version}");
            }
        }
        else
        {
            ed.WriteMessage("\n未检测到AutoCAD安装");
        }
    }
    catch (System.Exception ex)
    {
        ed.WriteMessage($"\n错误: {ex.Message}");
    }
}
```

#### 案例5：开发环境验证工具
**目标**：验证开发环境是否正确配置

**AutoLISP版本：**
```lisp
(defun c:ValidateDevEnvironment (/ result)
    (setq result "开发环境验证结果:\n\n")
    
    ; 检查AutoCAD版本
    (setq result (strcat result "AutoCAD版本: " (getvar "ACADVER") "\n"))
    
    ; 检查支持路径
    (setq result (strcat result "支持路径: " (getvar "SUPPORTFILESEARCHPATH") "\n"))
    
    ; 检查LISP支持
    (if (member "acad.lsp" (vl-directory-files (getvar "SUPPORTFILESEARCHPATH")))
        (setq result (strcat result "LISP支持: ✓\n"))
        (setq result (strcat result "LISP支持: ✗\n"))
    )
    
    ; 检查VBA支持
    (if (vlax-read-enabled-p (vlax-get-acad-object))
        (setq result (strcat result "VBA支持: ✓\n"))
        (setq result (strcat result "VBA支持: ✗\n"))
    )
    
    ; 检查.NET支持
    (if (>= (atoi (substr (getvar "ACADVER") 1 4)) 2016)
        (setq result (strcat result ".NET支持: ✓\n"))
        (setq result (strcat result ".NET支持: ✗\n"))
    )
    
    (alert result)
    (princ)
)
```

**VBA版本：**
```vba
Sub ValidateDevEnvironment()
    Dim result As String
    result = "开发环境验证结果:" & vbCrLf & vbCrLf
    
    ' 检查AutoCAD版本
    result = result & "AutoCAD版本: " & ThisDrawing.GetVariable("ACADVER") & vbCrLf
    
    ' 检查支持路径
    result = result & "支持路径: " & ThisDrawing.GetVariable("SUPPORTFILESEARCHPATH") & vbCrLf
    
    ' 检查LISP支持
    Dim acadApp As Object
    Set acadApp = GetObject(, "AutoCAD.Application")
    
    On Error Resume Next
    Dim lispSupport As Boolean
    lispSupport = acadApp.GetVariable("LISPINIT")
    If Err.Number = 0 And lispSupport Then
        result = result & "LISP支持: ✓" & vbCrLf
    Else
        result = result & "LISP支持: ✗" & vbCrLf
    End If
    On Error GoTo 0
    
    ' 检查VBA支持
    result = result & "VBA支持: ✓" & vbCrLf
    
    ' 检查版本兼容性
    Dim acadVersion As String
    acadVersion = ThisDrawing.GetVariable("ACADVER")
    If Val(acadVersion) >= 2016 Then
        result = result & ".NET支持: ✓" & vbCrLf
    Else
        result = result & ".NET支持: ✗" & vbCrLf
    End If
    
    MsgBox result, vbInformation, "环境验证"
End Sub
```

**C#版本：**
```csharp
[CommandMethod("ValidateDevEnvironment")]
public void ValidateDevEnvironment()
{
    Document doc = Application.DocumentManager.MdiActiveDocument;
    Editor ed = doc.Editor;
    
    try
    {
        string result = "开发环境验证结果:\n\n";
        
        // 检查AutoCAD版本
        string acadVersion = Application.Version.ToString();
        result += $"AutoCAD版本: {acadVersion}\n";
        
        // 检查支持路径
        string supportPath = Application.GetSystemVariable("SUPPORTFILESEARCHPATH").ToString();
        result += $"支持路径: {supportPath}\n";
        
        // 检查LISP支持
        bool lispSupport = System.IO.File.Exists(System.IO.Path.Combine(supportPath.Split(';')[0], "acad.lsp"));
        result += $"LISP支持: {(lispSupport ? "✓" : "✗")}\n";
        
        // 检查VBA支持
        bool vbaSupport = true; // VBA通常在AutoCAD中可用
        result += $"VBA支持: {(vbaSupport ? "✓" : "✗")}\n";
        
        // 检查.NET支持
        bool netSupport = acadVersion.StartsWith("R2") || acadVersion.StartsWith("R3");
        result += $".NET支持: {(netSupport ? "✓" : "✗")}\n";
        
        // 检查CURSOR集成
        bool cursorSupport = System.IO.File.Exists(Environment.GetFolderPath(Environment.SpecialFolder.UserProfile) + "\\.cursor\\user\\settings.json");
        result += $"CURSOR支持: {(cursorSupport ? "✓" : "✗")}\n";
        
        ed.WriteMessage("\n" + result);
        Application.ShowAlertDialog(result);
    }
    catch (System.Exception ex)
    {
        ed.WriteMessage($"\n错误: {ex.Message}");
    }
}
```

---

## 第二章：AutoCAD基础自动化

### 2.1 AutoLISP编程基础

#### 2.1.1 LISP语言语法基础

**基本语法结构：**
```lisp
; 注释以分号开头
; 变量赋值
(setq variable_name value)

; 函数定义
(defun function_name (parameters)
    ; 函数体
    (return_value)
)

; 条件语句
(if (condition)
    (then_expression)
    (else_expression)
)

; 循环语句
(while (condition)
    (loop_body)
)

; 列表操作
(list item1 item2 item3)
(car list)           ; 获取第一个元素
(cdr list)           ; 获取剩余元素
(cons item list)     ; 添加元素到列表头部
```

**数据类型：**
```lisp
; 整数
(setq integer_var 42)

; 浮点数
(setq float_var 3.14159)

; 字符串
(setq string_var "Hello, World!")

; 列表
(setq list_var '(1 2 3 4 5))

; 符号
(setq symbol_var 'my_symbol)

; 点坐标
(setq point_var '(10.0 20.0 0.0))
```

#### 2.1.2 AutoLISP函数库

**数学函数：**
```lisp
; 基本运算
(+ 1 2 3)           ; 返回 6
(- 10 4)            ; 返回 6
(* 3 4)             ; 返回 12
(/ 10 3)            ; 返回 3.33333

; 三角函数
(sin angle)         ; 正弦函数
(cos angle)         ; 余弦函数
(tan angle)         ; 正切函数
(atan y x)          ; 反正切函数

; 其他数学函数
(sqrt number)       ; 平方根
(expt base power)   ; 幂运算
(log number)        ; 自然对数
(abs number)        ; 绝对值
```

**字符串函数：**
```lisp
; 字符串操作
(strcat "Hello" " " "World")  ; 连接字符串
(substr "Hello" 1 3)          ; 子字符串
(strlen "Hello")              ; 字符串长度
(strcase "Hello" T)           ; 转换为大写
(strcase "Hello" nil)         ; 转换为小写
```

**列表函数：**
```lisp
; 列表操作
(list 1 2 3 4)                ; 创建列表
(car '(1 2 3))               ; 获取第一个元素 (1)
(cdr '(1 2 3))               ; 获取剩余元素 (2 3)
(cons 1 '(2 3))              ; 添加元素到头部 (1 2 3)
(length '(1 2 3))            ; 列表长度
(reverse '(1 2 3))           ; 反转列表
(member 2 '(1 2 3))          ; 检查成员
(assoc 'key '((key1 val1) (key2 val2)))  ; 关联列表查找
```

#### 2.1.3 图形对象操作

**基本图形创建：**
```lisp
; 创建直线
(defun draw-line (start-point end-point / line-obj)
    (setq line-obj (entmake (list '(0 . "LINE")
                                 (cons 10 start-point)
                                 (cons 11 end-point))))
    line-obj
)

; 创建圆
(defun draw-circle (center radius / circle-obj)
    (setq circle-obj (entmake (list '(0 . "CIRCLE")
                                   (cons 10 center)
                                   (cons 40 radius))))
    circle-obj
)

; 创建矩形
(defun draw-rectangle (bottom-left width height / rect-obj)
    (setq rect-obj (command "rectangle" bottom-left 
                           (list (+ (car bottom-left) width)
                                 (+ (cadr bottom-left) height))))
    rect-obj
)
```

**对象选择和修改：**
```lisp
; 选择对象
(defun select-objects (prompt / selection-set)
    (setq selection-set (ssget prompt))
    selection-set
)

; 修改对象属性
(defun change-layer (selection-set layer-name / index entity)
    (setq index 0)
    (repeat (sslength selection-set)
        (setq entity (ssname selection-set index))
        (if entity
            (progn
                (entmod (subst (cons 8 layer-name)
                              (assoc 8 (entget entity))
                              (entget entity)))
            )
        )
        (setq index (1+ index))
    )
    (princ)
)

; 删除对象
(defun delete-objects (selection-set / index entity)
    (setq index 0)
    (repeat (sslength selection-set)
        (setq entity (ssname selection-set index))
        (if entity
            (entdel entity)
        )
        (setq index (1+ index))
    )
    (princ)
)
```

#### 2.1.4 实例：简单绘图自动化

**实例1：自动绘制网格**
```lisp
(defun c:DrawGrid (/ grid-size spacing x y i j)
    ; 获取用户输入
    (setq grid-size (getint "输入网格大小: "))
    (setq spacing (getreal "输入网格间距: "))
    
    ; 绘制网格线
    (setq i 0)
    (repeat grid-size
        (setq y (* i spacing))
        ; 水平线
        (command "line" (list 0 y 0) (list (* grid-size spacing) y 0) "")
        (setq i (1+ i))
    )
    
    (setq j 0)
    (repeat grid-size
        (setq x (* j spacing))
        ; 垂直线
        (command "line" (list x 0 0) (list x (* grid-size spacing) 0) "")
        (setq j (1+ j))
    )
    
    (princ)
)
```

**实例2：批量创建标注**
```lisp
(defun c:BatchDimension (/ points index point1 point2)
    ; 选择多个点
    (setq points (ssget '((0 . "POINT"))))
    
    (if points
        (progn
            (setq index 0)
            (repeat (sslength points)
                (setq point1 (cdr (assoc 10 (entget (ssname points index)))))
                (setq index (1+ index))
                
                (if (< index (sslength points))
                    (progn
                        (setq point2 (cdr (assoc 10 (entget (ssname points index)))))
                        ; 创建线性标注
                        (command "dimlinear" point1 point2 (list (+ (car point1) 10) (+ (cadr point1) 10)))
                    )
                )
            )
        )
    )
    
    (princ)
)
```

**实例3：自动生成标题栏**
```lisp
(defun c:CreateTitleBlock (/ title-block-width title-block-height 
                              drawing-title drawing-number scale date)
    ; 获取标题栏参数
    (setq title-block-width 180)
    (setq title-block-height 50)
    
    ; 获取图纸信息
    (setq drawing-title (getstring "输入图纸标题: "))
    (setq drawing-number (getstring "输入图号: "))
    (setq scale (getstring "输入比例: "))
    (setq date (menucmd "M=$(edtime, $(getvar, date), YYYY-MO-DD)"))
    
    ; 绘制标题栏边框
    (command "rectangle" (list 0 0 0) (list title-block-width title-block-height 0))
    
    ; 绘制内部线条
    (command "line" (list 100 0 0) (list 100 title-block-height 0) "")
    (command "line" (list 0 35 0) (list title-block-width 35 0) "")
    (command "line" (list 0 20 0) (list 100 20 0) "")
    (command "line" (list 0 10 0) (list 100 10 0) "")
    
    ; 添加文字
    (command "text" "j" "c" (list 50 42.5 0) 5 0 drawing-title)
    (command "text" "j" "c" (list 50 27.5 0) 3.5 0 drawing-number)
    (command "text" "j" "c" (list 50 15 0) 3.5 0 scale)
    (command "text" "j" "c" (list 50 5 0) 3.5 0 date)
    
    (princ)
)
```

### 2.2 VBA自动化开发

#### 2.2.1 VBA编辑器使用

**VBA编辑器界面：**
```vba
' 打开VBA编辑器
' 在AutoCAD中输入：VBAIDE
' 或者通过菜单：工具 -> 宏 -> Visual Basic 编辑器

' VBA编辑器主要组件：
' - 项目浏览器：显示项目中的所有模块和对象
' - 代码窗口：编写和编辑VBA代码
' - 属性窗口：查看和修改对象属性
' - 立即窗口：执行即时命令和调试
' - 本地窗口：查看当前变量的值
' - 监视窗口：监视特定变量或表达式
```

**基本VBA项目结构：**
```vba
' 创建新模块
' 1. 在项目浏览器中右键点击项目
' 2. 选择"插入" -> "模块"
' 3. 在代码窗口中编写代码

' 模块结构示例：
Option Explicit

' 声明模块级变量
Private m AcadApp As AcadApplication
Private m AcadDoc As AcadDocument

' 初始化过程
Public Sub Initialize()
    Set m AcadApp = Application
    Set m AcadDoc = ThisDrawing
End Sub

' 清理过程
Public Sub Cleanup()
    Set m AcadDoc = Nothing
    Set m AcadApp = Nothing
End Sub
```

#### 2.2.2 AutoCAD对象模型

**AutoCAD对象层次结构：**
```vba
' AutoCAD对象模型层次：
' AcadApplication (应用程序)
'   |- AcadDocuments (文档集合)
'       |- AcadDocument (文档)
'           |- AcadModelSpace (模型空间)
'           |- AcadPaperSpace (图纸空间)
'           |- AcadLayers (图层集合)
'           |- AcadTextStyles (文字样式集合)
'           |- AcadDimStyles (标注样式集合)
'           |- AcadBlocks (块集合)
'           |- AcadGroups (组集合)
'           |- AcadSelectionSets (选择集集合)
```

**对象操作示例：**
```vba
' 获取应用程序对象
Dim acadApp As AcadApplication
Set acadApp = Application

' 获取当前文档
Dim acadDoc As AcadDocument
Set acadDoc = ThisDrawing

' 获取模型空间
Dim modelSpace As AcadModelSpace
Set modelSpace = acadDoc.ModelSpace

' 获取图层集合
Dim layers As AcadLayers
Set layers = acadDoc.Layers

' 创建新图层
Dim newLayer As AcadLayer
Set newLayer = layers.Add("NewLayer")
newLayer.Color = acRed
newLayer.LineWeight = acLnWt025
```

#### 2.2.3 基本图形操作

**创建基本图形：**
```vba
' 创建直线
Sub CreateLine()
    Dim lineObj As AcadLine
    Dim startPoint(0 To 2) As Double
    Dim endPoint(0 To 2) As Double
    
    startPoint(0) = 0: startPoint(1) = 0: startPoint(2) = 0
    endPoint(0) = 100: endPoint(1) = 100: endPoint(2) = 0
    
    Set lineObj = ThisDrawing.ModelSpace.AddLine(startPoint, endPoint)
    lineObj.Color = acBlue
    lineObj.Layer = "0"
    
    ZoomAll
End Sub

' 创建圆
Sub CreateCircle()
    Dim circleObj As AcadCircle
    Dim centerPoint(0 To 2) As Double
    Dim radius As Double
    
    centerPoint(0) = 50: centerPoint(1) = 50: centerPoint(2) = 0
    radius = 25
    
    Set circleObj = ThisDrawing.ModelSpace.AddCircle(centerPoint, radius)
    circleObj.Color = acRed
    circleObj.Layer = "0"
    
    ZoomAll
End Sub

' 创建矩形
Sub CreateRectangle()
    Dim rectangleObj As AcadLWPolyline
    Dim points(0 To 7) As Double
    
    ' 定义矩形的四个角点
    points(0) = 0: points(1) = 0
    points(2) = 100: points(3) = 0
    points(4) = 100: points(5) = 50
    points(6) = 0: points(7) = 50
    
    Set rectangleObj = ThisDrawing.ModelSpace.AddLightWeightPolyline(points)
    rectangleObj.Closed = True
    rectangleObj.Color = acGreen
    rectangleObj.Layer = "0"
    
    ZoomAll
End Sub
```

**创建复杂图形：**
```vba
' 创建多段线
Sub CreatePolyline()
    Dim plineObj As AcadLWPolyline
    Dim points(0 To 11) As Double
    
    ' 定义多段线的顶点
    points(0) = 0: points(1) = 0
    points(2) = 50: points(3) = 25
    points(4) = 100: points(5) = 0
    points(6) = 75: points(7) = 50
    points(8) = 25: points(9) = 50
    points(10) = 0: points(11) = 0
    
    Set plineObj = ThisDrawing.ModelSpace.AddLightWeightPolyline(points)
    plineObj.Closed = True
    plineObj.Color = acMagenta
    plineObj.Layer = "0"
    
    ZoomAll
End Sub

' 创建文字
Sub CreateText()
    Dim textObj As AcadText
    Dim insertionPoint(0 To 2) As Double
    Dim height As Double
    Dim textString As String
    
    insertionPoint(0) = 50: insertionPoint(1) = 50: insertionPoint(2) = 0
    height = 5
    textString = "Hello, AutoCAD!"
    
    Set textObj = ThisDrawing.ModelSpace.AddText(textString, insertionPoint, height)
    textObj.Color = acBlue
    textObj.Layer = "0"
    
    ZoomAll
End Sub

' 创建标注
Sub CreateDimension()
    Dim dimObj As AcadDimAligned
    Dim startPoint(0 To 2) As Double
    Dim endPoint(0 To 2) As Double
    Dim textPoint(0 To 2) As Double
    
    startPoint(0) = 0: startPoint(1) = 0: startPoint(2) = 0
    endPoint(0) = 100: endPoint(1) = 0: endPoint(2) = 0
    textPoint(0) = 50: textPoint(1) = 20: textPoint(2) = 0
    
    Set dimObj = ThisDrawing.ModelSpace.AddDimAligned(startPoint, endPoint, textPoint)
    dimObj.Color = acRed
    dimObj.Layer = "0"
    
    ZoomAll
End Sub
```

#### 2.2.4 用户界面交互

**用户输入处理：**
```vba
' 获取用户点输入
Sub GetPointInput()
    Dim point As Variant
    point = ThisDrawing.Utility.GetPoint(, "请选择一个点: ")
    
    If Not IsEmpty(point) Then
        MsgBox "选择的点坐标: (" & point(0) & ", " & point(1) & ", " & point(2) & ")"
    End If
End Sub

' 获取用户字符串输入
Sub GetStringInput()
    Dim inputString As String
    inputString = ThisDrawing.Utility.GetString(True, "请输入字符串: ")
    
    If inputString <> "" Then
        MsgBox "输入的字符串: " & inputString
    End If
End Sub

' 获取用户数值输入
Sub GetNumberInput()
    Dim inputNumber As Double
    inputNumber = ThisDrawing.Utility.GetReal("请输入数值: ")
    
    If Not IsEmpty(inputNumber) Then
        MsgBox "输入的数值: " & inputNumber
    End If
End Sub
```

**创建用户界面：**
```vba
' 创建简单的用户窗体
' 1. 在VBA编辑器中插入用户窗体
' 2. 添加控件（按钮、文本框、标签等）
' 3. 编写事件处理代码

' 用户窗体代码示例：
Private Sub UserForm_Initialize()
    ' 初始化窗体
    Me.Caption = "AutoCAD自动化工具"
    Me.Width = 300
    Me.Height = 200
    
    ' 添加控件
    Dim lblTitle As MSForms.Label
    Set lblTitle = Me.Controls.Add("Forms.Label.1", "lblTitle")
    lblTitle.Caption = "请选择操作:"
    lblTitle.Left = 10
    lblTitle.Top = 10
    lblTitle.Width = 200
    
    Dim btnDrawLine As MSForms.CommandButton
    Set btnDrawLine = Me.Controls.Add("Forms.CommandButton.1", "btnDrawLine")
    btnDrawLine.Caption = "绘制直线"
    btnDrawLine.Left = 10
    btnDrawLine.Top = 40
    btnDrawLine.Width = 100
    
    Dim btnDrawCircle As MSForms.CommandButton
    Set btnDrawCircle = Me.Controls.Add("Forms.CommandButton.1", "btnDrawCircle")
    btnDrawCircle.Caption = "绘制圆形"
    btnDrawCircle.Left = 10
    btnDrawCircle.Top = 80
    btnDrawCircle.Width = 100
    
    Dim btnCancel As MSForms.CommandButton
    Set btnCancel = Me.Controls.Add("Forms.CommandButton.1", "btnCancel")
    btnCancel.Caption = "取消"
    btnCancel.Left = 10
    btnCancel.Top = 120
    btnCancel.Width = 100
End Sub

Private Sub btnDrawLine_Click()
    ' 调用绘制直线函数
    CreateLine
    Unload Me
End Sub

Private Sub btnDrawCircle_Click()
    ' 调用绘制圆形函数
    CreateCircle
    Unload Me
End Sub

Private Sub btnCancel_Click()
    Unload Me
End Sub

' 显示用户窗体
Sub ShowUserForm()
    Dim frm As New UserForm1
    frm.Show
End Sub
```

### 2.3 CURSOR辅助开发

#### 2.3.1 使用CURSOR生成AutoLISP代码

**CURSOR提示词模板：**
```text
请生成一个AutoLISP函数，用于：
1. 函数名称：c:FunctionName
2. 功能描述：[详细描述功能]
3. 输入参数：[列出参数]
4. 输出结果：[描述输出]
5. 错误处理：[描述错误处理]
6. 示例用法：[提供示例]

要求：
- 使用标准的AutoLISP语法
- 包含详细的注释
- 实现完整的错误处理
- 提供使用示例
```

**生成AutoLISP代码示例：**
```lisp
;;; 函数名称: c:DrawMultipleCircles
;;; 功能描述: 在指定位置绘制多个圆形
;;; 输入参数: 
;;;   - center-point: 圆心点坐标
;;;   - radius-list: 半径列表
;;;   - layer-name: 图层名称
;;; 输出结果: 绘制多个圆形对象
;;; 错误处理: 检查输入参数有效性
;;; 示例用法: (c:DrawMultipleCircles '(0 0 0) '(10 20 30) "CIRCLES")

(defun c:DrawMultipleCircles (center-point radius-list layer-name / 
                             valid-center valid-radii valid-layer 
                             index current-radius circle-obj)
    ; 错误处理：检查圆心点
    (if (and (listp center-point) 
             (= (length center-point) 3)
             (numberp (car center-point))
             (numberp (cadr center-point))
             (numberp (caddr center-point)))
        (setq valid-center T)
        (progn
            (alert "错误：圆心点必须是包含3个数值的列表")
            (exit)
        )
    )
    
    ; 错误处理：检查半径列表
    (if (and (listp radius-list)
             (> (length radius-list) 0))
        (progn
            (setq valid-radii T)
            (foreach radius radius-list
                (if (not (numberp radius))
                    (setq valid-radii nil)
                )
            )
        )
        (setq valid-radii nil)
    )
    
    (if (not valid-radii)
        (progn
            (alert "错误：半径列表必须包含至少一个数值")
            (exit)
        )
    )
    
    ; 错误处理：检查图层是否存在
    (if (tblsearch "LAYER" layer-name)
        (setq valid-layer T)
        (progn
            (alert (strcat "错误：图层 '" layer-name "' 不存在"))
            (exit)
        )
    )
    
    ; 设置当前图层
    (setvar "CLAYER" layer-name)
    
    ; 绘制圆形
    (setq index 0)
    (foreach radius radius-list
        (setq circle-obj (entmake (list '(0 . "CIRCLE")
                                       (cons 10 center-point)
                                       (cons 40 radius)
                                       (cons 8 layer-name))))
        (if circle-obj
            (princ (strcat "\n圆形 " (itoa (1+ index)) " 已绘制，半径: " (rtos radius)))
            (princ (strcat "\n警告：圆形 " (itoa (1+ index)) " 绘制失败"))
        )
        (setq index (1+ index))
    )
    
    (princ "\n绘制完成!")
    (princ)
)

;;; 使用示例：
;;; (c:DrawMultipleCircles '(0 0 0) '(10 20 30) "CIRCLES")
;;; (c:DrawMultipleCircles '(100 100 0) '(5 15 25 35) "TEMP")
```

#### 2.3.2 CURSOR优化VBA脚本

**CURSOR优化提示词：**
```text
请优化以下VBA代码：
1. 代码功能：[描述代码功能]
2. 原始代码：[提供原始代码]
3. 优化要求：
   - 提高代码执行效率
   - 增强错误处理
   - 改进代码结构
   - 添加详细注释
   - 提供性能优化建议
4. 使用场景：[描述使用场景]
```

**优化后的VBA代码示例：**
```vba
' 函数名称: BatchCreateCircles
' 功能描述: 批量创建圆形对象
' 优化版本: 提高性能，增强错误处理，改进代码结构
' 作者: CURSOR AI助手
' 日期: 2025-01-01

Option Explicit

' 声明常量
Private Const MAX_CIRCLES As Integer = 1000
Private Const DEFAULT_LAYER As String = "0"
Private Const ERROR_INVALID_INPUT As Long = 1001
Private Const ERROR_LAYER_NOT_FOUND As Long = 1002
Private Const ERROR_CREATE_FAILED As Long = 1003

' 主函数：批量创建圆形
Public Sub BatchCreateCircles()
    On Error GoTo ErrorHandler
    
    ' 声明变量
    Dim acadDoc As AcadDocument
    Dim modelSpace As AcadModelSpace
    Dim centerPoints() As Variant
    Dim radii() As Double
    Dim layerName As String
    Dim startTime As Double
    Dim endTime As Double
    Dim circleCount As Long
    
    ' 记录开始时间
    startTime = Timer
    
    ' 获取当前文档和模型空间
    Set acadDoc = ThisDrawing
    Set modelSpace = acadDoc.ModelSpace
    
    ' 获取用户输入
    If Not GetUserInput(centerPoints, radii, layerName) Then
        Exit Sub
    End If
    
    ' 验证输入
    If Not ValidateInput(centerPoints, radii, layerName) Then
        Exit Sub
    End If
    
    ' 禁用屏幕更新以提高性能
    acadDoc.SetVariable "REGENMODE", 0
    Application.ScreenUpdating = False
    
    ' 批量创建圆形
    circleCount = CreateCirclesBatch(modelSpace, centerPoints, radii, layerName)
    
    ' 恢复屏幕更新
    Application.ScreenUpdating = True
    acadDoc.SetVariable "REGENMODE", 1
    
    ' 重生成视图
    acadDoc.Regen acAllViewports
    
    ' 记录结束时间并显示结果
    endTime = Timer
    ShowResults circleCount, endTime - startTime
    
    Exit Sub
    
ErrorHandler:
    ' 错误处理
    Application.ScreenUpdating = True
    acadDoc.SetVariable "REGENMODE", 1
    
    Select Case Err.Number
        Case ERROR_INVALID_INPUT
            MsgBox "错误：输入参数无效", vbExclamation, "输入错误"
        Case ERROR_LAYER_NOT_FOUND
            MsgBox "错误：指定的图层不存在", vbExclamation, "图层错误"
        Case ERROR_CREATE_FAILED
            MsgBox "错误：创建圆形对象失败", vbExclamation, "创建错误"
        Case Else
            MsgBox "未知错误: " & Err.Description, vbCritical, "错误"
    End Select
End Sub

' 获取用户输入
Private Function GetUserInput(ByRef centerPoints() As Variant, _
                           ByRef radii() As Double, _
                           ByRef layerName As String) As Boolean
    On Error Resume Next
    
    ' 获取图层名称
    layerName = InputBox("请输入图层名称 (默认: " & DEFAULT_LAYER & "):", _
                        "批量创建圆形", DEFAULT_LAYER)
    If layerName = "" Then layerName = DEFAULT_LAYER
    
    ' 获取圆形数量
    Dim count As Integer
    count = CInt(InputBox("请输入要创建的圆形数量 (1-" & MAX_CIRCLES & "):", _
                         "批量创建圆形", "10"))
    
    If count < 1 Or count > MAX_CIRCLES Then
        MsgBox "圆形数量必须在 1 到 " & MAX_CIRCLES & " 之间", vbExclamation, "输入错误"
        GetUserInput = False
        Exit Function
    End If
    
    ' 重新定义数组
    ReDim centerPoints(0 To count - 1)
    ReDim radii(0 To count - 1)
    
    ' 获取每个圆形的参数
    Dim i As Integer
    Dim point As Variant
    Dim radius As Double
    
    For i = 0 To count - 1
        ' 获取圆心点
        point = ThisDrawing.Utility.GetPoint(, "请输入第 " & (i + 1) & " 个圆的圆心点: ")
        If IsEmpty(point) Then
            GetUserInput = False
            Exit Function
        End If
        centerPoints(i) = point
        
        ' 获取半径
        radius = ThisDrawing.Utility.GetReal("请输入第 " & (i + 1) & " 个圆的半径: ")
        If radius <= 0 Then
            MsgBox "半径必须大于 0", vbExclamation, "输入错误"
            GetUserInput = False
            Exit Function
        End If
        radii(i) = radius
    Next i
    
    GetUserInput = True
End Function

' 验证输入参数
Private Function ValidateInput(centerPoints() As Variant, _
                              radii() As Double, _
                              layerName As String) As Boolean
    On Error GoTo ErrorHandler
    
    ' 检查数组长度
    If UBound(centerPoints) <> UBound(radii) Then
        Err.Raise ERROR_INVALID_INPUT, "ValidateInput", "中心点数组和半径数组长度不匹配"
    End If
    
    ' 检查图层是否存在
    Dim layer As AcadLayer
    On Error Resume Next
    Set layer = ThisDrawing.Layers(layerName)
    On Error GoTo ErrorHandler
    
    If layer Is Nothing Then
        Err.Raise ERROR_LAYER_NOT_FOUND, "ValidateInput", "图层 '" & layerName & "' 不存在"
    End If
    
    ' 检查半径值
    Dim i As Integer
    For i = LBound(radii) To UBound(radii)
        If radii(i) <= 0 Then
            Err.Raise ERROR_INVALID_INPUT, "ValidateInput", "半径必须大于 0"
        End If
    Next i
    
    ValidateInput = True
    Exit Function
    
ErrorHandler:
    ValidateInput = False
End Function

' 批量创建圆形
Private Function CreateCirclesBatch(modelSpace As AcadModelSpace, _
                                  centerPoints() As Variant, _
                                  radii() As Double, _
                                  layerName As String) As Long
    On Error GoTo ErrorHandler
    
    Dim i As Integer
    Dim circleObj As AcadCircle
    Dim successCount As Long
    
    successCount = 0
    
    ' 批量创建圆形
    For i = LBound(centerPoints) To UBound(centerPoints)
        On Error Resume Next
        Set circleObj = modelSpace.AddCircle(centerPoints(i), radii(i))
        
        If Not circleObj Is Nothing Then
            ' 设置圆形属性
            circleObj.Layer = layerName
            circleObj.Color = acByLayer
            circleObj.Linetype = "ByLayer"
            
            successCount = successCount + 1
        Else
            Debug.Print "创建第 " & (i + 1) & " 个圆形失败"
        End If
        
        On Error GoTo ErrorHandler
    Next i
    
    CreateCirclesBatch = successCount
    Exit Function
    
ErrorHandler:
    Err.Raise ERROR_CREATE_FAILED, "CreateCirclesBatch", "创建圆形对象失败"
End Function

' 显示结果
Private Sub ShowResults(circleCount As Long, elapsedTime As Double)
    Dim message As String
    
    message = "批量创建圆形完成！" & vbCrLf & vbCrLf
    message = message & "成功创建圆形数量: " & circleCount & vbCrLf
    message = message & "耗时: " & Format(elapsedTime, "0.00") & " 秒" & vbCrLf
    
    If circleCount > 0 Then
        message = message & "平均每个圆形耗时: " & Format(elapsedTime / circleCount, "0.000") & " 秒"
    End If
    
    MsgBox message, vbInformation, "操作结果"
End Sub

' 性能优化建议：
' 1. 使用数组存储对象引用，减少重复访问
' 2. 批量操作时禁用屏幕更新
' 3. 使用事务处理确保数据一致性
' 4. 实现进度显示以提高用户体验
' 5. 添加内存管理以处理大量对象
' 6. 实现错误恢复机制
' 7. 使用多线程处理（如果支持）
' 8. 缓存常用对象和属性
```

#### 2.3.3 代码调试和错误处理

**AutoLISP调试技巧：**
```lisp
;;; 调试工具函数
;;; 打印调试信息
(defun debug-print (message / )
    (if (getvar "DEBUGMODE")
        (princ (strcat "\nDEBUG: " message))
    )
)

;;; 变量检查函数
(defun check-variable (var-name var-value / )
    (if (not (boundp var-name))
        (progn
            (debug-print (strcat "变量 " var-name " 未定义"))
            nil
        )
        (progn
            (debug-print (strcat "变量 " var-name " = " (vl-princ-to-string var-value)))
            T
        )
    )
)

;;; 错误处理框架
(defun with-error-handling (function error-handler / result)
    (setq result (vl-catch-all-apply function))
    (if (vl-catch-all-error-p result)
        (progn
            (debug-print (strcat "错误: " (vl-catch-all-error-message result)))
            (if error-handler
                (funcall error-handler result)
                nil
            )
        )
        result
    )
)

;;; 使用示例
(defun c:SafeDrawLine (/ start-point end-point)
    (with-error-handling
        '(lambda ()
           (setq start-point (getpoint "\n选择起点: "))
           (setq end-point (getpoint start-point "\n选择终点: "))
           (command "line" start-point end-point "")
           (princ "直线绘制完成")
         )
        '(lambda (error)
           (princ (strcat "\n绘制失败: " (vl-catch-all-error-message error)))
         )
    )
    (princ)
)
```

**VBA调试技巧：**
```vba
' 调试工具模块
Option Explicit

' 调试模式开关
Private Const DEBUG_MODE As Boolean = True

' 调试输出函数
Public Sub DebugPrint(message As String)
    If DEBUG_MODE Then
        Debug.Print "[DEBUG] " & Now & " - " & message
    End If
End Sub

' 变量检查函数
Public Sub DebugVariable(varName As String, varValue As Variant)
    If DEBUG_MODE Then
        Debug.Print "[DEBUG] " & varName & " = " & GetValueString(varValue)
    End If
End Sub

' 获取变量值的字符串表示
Private Function GetValueString(varValue As Variant) As String
    On Error Resume Next
    
    Select Case VarType(varValue)
        Case vbEmpty
            GetValueString = "[Empty]"
        Case vbNull
            GetValueString = "[Null]"
        Case vbInteger, vbLong, vbSingle, vbDouble, vbCurrency, vbDecimal
            GetValueString = CStr(varValue)
        Case vbString
            GetValueString = """" & varValue & """"
        Case vbBoolean
            GetValueString = IIf(varValue, "True", "False")
        Case vbDate
            GetValueString = "#" & Format(varValue, "yyyy-mm-dd hh:mm:ss") & "#"
        Case vbObject
            GetValueString = "[Object: " & TypeName(varValue) & "]"
        Case Else
            GetValueString = "[Unknown Type]"
    End Select
End Function

' 错误处理框架
Public Function SafeExecute(funcName As String, ParamArray args() As Variant) As Variant
    On Error GoTo ErrorHandler
    
    DebugPrint "开始执行函数: " & funcName
    
    ' 这里应该根据函数名称调用相应的函数
    ' 简化示例，直接返回成功
    SafeExecute = "执行成功"
    
    DebugPrint "函数执行完成: " & funcName
    Exit Function
    
ErrorHandler:
    DebugPrint "函数执行失败: " & funcName & " - " & Err.Description
    SafeExecute = CVErr(Err.Number)
End Function

' 性能监控类
Private Class PerformanceMonitor
    Private startTime As Double
    Private operationName As String
    
    Public Sub Start(opName As String)
        operationName = opName
        startTime = Timer
        DebugPrint "开始操作: " & operationName
    End Sub
    
    Public Sub Stop()
        Dim endTime As Double
        Dim duration As Double
        
        endTime = Timer
        duration = endTime - startTime
        
        DebugPrint "完成操作: " & operationName & " - 耗时: " & Format(duration, "0.000") & "秒"
    End Sub
End Class

' 使用示例
Public Sub TestDebugTools()
    Dim monitor As New PerformanceMonitor
    
    monitor.Start "测试操作"
    
    ' 模拟一些操作
    DebugVariable "测试变量", 123
    DebugVariable "测试字符串", "Hello"
    DebugVariable "测试对象", ThisDrawing
    
    ' 模拟错误
    On Error Resume Next
    Dim result As Variant
    result = 1 / 0
    If Err.Number <> 0 Then
        DebugPrint "捕获到错误: " & Err.Description
    End If
    On Error GoTo 0
    
    monitor.Stop
End Sub
```

#### 2.3.4 最佳实践和规范

**AutoLISP编码规范：**
```lisp
;;; AutoLISP编码规范
;;; 文件头注释示例
;;; 文件名: my_cad_tools.lsp
;;; 作者: [作者名称]
;;; 创建日期: [创建日期]
;;; 最后修改: [修改日期]
;;; 描述: [文件描述]
;;; 版本: [版本号]

;;; 函数注释模板
;;; 函数名称: function_name
;;; 功能描述: [详细描述函数功能]
;;; 输入参数:
;;;   - param1: [参数1描述]
;;;   - param2: [参数2描述]
;;; 输出结果: [返回值描述]
;;; 错误处理: [错误处理描述]
;;; 示例用法: [使用示例]
;;; 依赖关系: [依赖的其他函数或变量]

;;; 命名规范
;;; - 函数名使用小写字母，单词间用下划线分隔
;;; - 变量名使用小写字母，单词间用下划线分隔
;;; - 常量使用大写字母，单词间用下划线分隔
;;; - 全局变量以g_开头
;;; - 局部变量在参数列表中声明

;;; 示例函数
(defun calculate_area (width height / result)
    ;;; 计算矩形面积
    ;;; 输入参数:
    ;;;   - width: 矩形宽度
    ;;;   - height: 矩形高度
    ;;; 输出结果: 矩形面积
    ;;; 错误处理: 检查输入参数是否为正数
    
    ; 参数验证
    (if (or (<= width 0) (<= height 0))
        (progn
            (alert "错误：宽度和高度必须为正数")
            (exit)
        )
    )
    
    ; 计算面积
    (setq result (* width height))
    
    ; 返回结果
    result
)

;;; 错误处理最佳实践
(defun safe_divide (numerator denominator / result)
    ;;; 安全除法函数
    ;;; 输入参数:
    ;;;   - numerator: 分子
    ;;;   - denominator: 分母
    ;;; 输出结果: 除法结果，如果分母为0则返回nil
    
    (if (zerop denominator)
        (progn
            (debug-print "错误：分母不能为0")
            nil
        )
        (/ numerator denominator)
    )
)

;;; 性能优化建议
;;; 1. 避免重复计算，使用变量存储中间结果
;;; 2. 使用局部变量而不是全局变量
;;; 3. 批量操作时减少屏幕更新
;;; 4. 使用适当的数据结构（列表、数组等）
;;; 5. 避免不必要的对象创建和销毁
;;; 6. 使用缓存机制存储常用数据
```

**VBA编码规范：**
```vba
' VBA编码规范
' 文件头注释
' 文件名: CADTools.bas
' 作者: [作者名称]
' 创建日期: [创建日期]
' 最后修改: [修改日期]
' 描述: [文件描述]
' 版本: [版本号]

' 选项设置
Option Explicit
Option Compare Text
Option Base 1

' 常量声明
Private Const MODULE_NAME As String = "CADTools"
Private Const VERSION As String = "1.0.0"
Private Const MAX_RETRY_COUNT As Integer = 3

' 枚举类型
Public Enum OperationResult
    Success = 0
    InvalidInput = 1
    OperationFailed = 2
    Cancelled = 3
End Enum

' 函数注释模板
' 函数名称: FunctionName
' 功能描述: [详细描述函数功能]
' 输入参数:
'   - paramName: [参数描述]
' 输出结果: [返回值描述]
' 错误处理: [错误处理描述]
' 示例用法: [使用示例]
' 依赖关系: [依赖的其他函数或变量]

' 命名规范
' - 函数名使用PascalCase
' - 变量名使用camelCase
' - 常量使用UPPER_CASE
' - 模块级变量以m_开头
' - 全局变量以g_开头

' 示例函数
Public Function CalculateRectangleArea(ByVal width As Double, _
                                      ByVal height As Double) As Double
    ' 计算矩形面积
    ' 输入参数:
    '   - width: 矩形宽度
    '   - height: 矩形高度
    ' 输出结果: 矩形面积
    ' 错误处理: 检查输入参数是否为正数
    
    ' 参数验证
    If width <= 0 Or height <= 0 Then
        Err.Raise vbObjectError + 1, "CalculateRectangleArea", "宽度和高度必须为正数"
    End If
    
    ' 计算面积
    CalculateRectangleArea = width * height
End Function

' 错误处理最佳实践
Public Function SafeDivide(ByVal numerator As Double, _
                          ByVal denominator As Double) As Variant
    ' 安全除法函数
    ' 输入参数:
    '   - numerator: 分子
    '   - denominator: 分母
    ' 输出结果: 除法结果，如果分母为0则返回Empty
    
    On Error GoTo ErrorHandler
    
    If denominator = 0 Then
        SafeDivide = Empty
        Exit Function
    End If
    
    SafeDivide = numerator / denominator
    Exit Function
    
ErrorHandler:
    Debug.Print "SafeDivide 错误: " & Err.Description
    SafeDivide = Empty
End Function

' 性能优化建议
' 1. 使用With语句减少对象访问
' 2. 批量操作时禁用屏幕更新
' 3. 使用适当的数据类型
' 4. 避免不必要的类型转换
' 5. 使用数组而不是集合处理大量数据
' 6. 实现对象池模式重用对象
' 7. 使用早期绑定而不是后期绑定

' 使用With语句示例
Public Sub ProcessLayerProperties()
    Dim layer As AcadLayer
    Set layer = ThisDrawing.Layers.Add("TestLayer")
    
    With layer
        .Color = acRed
        .Linetype = "Continuous"
        .LineWeight = acLnWt025
        .Plottable = True
        .LayerOn = True
    End With
End Sub

' 批量操作示例
Public Sub BatchProcessEntities()
    Dim selectionSet As AcadSelectionSet
    Dim entity As AcadEntity
    Dim i As Integer
    
    ' 禁用屏幕更新
    Application.ScreenUpdating = False
    
    ' 创建选择集
    On Error Resume Next
    ThisDrawing.SelectionSets("BatchProcess").Delete
    On Error GoTo 0
    
    Set selectionSet = ThisDrawing.SelectionSets.Add("BatchProcess")
    selectionSet.SelectOnScreen
    
    ' 批量处理实体
    For i = 0 To selectionSet.Count - 1
        Set entity = selectionSet.Item(i)
        
        ' 处理实体
        With entity
            .Color = acByLayer
            .Linetype = "ByLayer"
        End With
    Next i
    
    ' 清理
    selectionSet.Delete
    Application.ScreenUpdating = True
    ThisDrawing.Regen acAllViewports
End Sub
```

### 2.4 专业可执行案例

#### 案例1：智能图层管理器
**目标**：创建一个智能图层管理器，可以批量创建、修改和管理图层

**AutoLISP版本：**
```lisp
;;; 智能图层管理器
;;; 文件名: smart_layer_manager.lsp
;;; 作者: CAD自动化专家
;;; 版本: 1.0.0

;;; 主函数：智能图层管理器
(defun c:SmartLayerManager (/ layer-config action)
    ;;; 显示主菜单
    (setq action (menu_action))
    
    (cond
        ((= action "CREATE") (create_layers_batch))
        ((= action "MODIFY") (modify_layers_batch))
        ((= action "DELETE") (delete_layers_batch))
        ((= action "EXPORT") (export_layers_config))
        ((= action "IMPORT") (import_layers_config))
        ((= action "REPORT") (generate_layer_report))
        (T (princ "\n操作取消"))
    )
    
    (princ)
)

;;; 显示操作菜单
(defun menu_action (/ choice)
    (initget "Create Modify Delete Export Import Report")
    (setq choice (getkword "\n选择操作 [Create/Modify/Delete/Export/Import/Report]: "))
    (if choice (strcase choice) "CANCEL")
)

;;; 批量创建图层
(defun create_layers_batch (/ config-file layer-list layer-name layer-data)
    ;;; 获取配置文件
    (setq config-file (getfiled "选择图层配置文件" "" "csv" 8))
    (if (not config-file)
        (progn
            (princ "\n未选择配置文件")
            (exit)
        )
    )
    
    ;;; 读取配置文件
    (setq layer-list (read_layer_config config-file))
    (if (not layer-list)
        (progn
            (princ "\n配置文件读取失败")
            (exit)
        )
    )
    
    ;;; 批量创建图层
    (foreach layer-data layer-list
        (setq layer-name (car layer-data))
        (if (not (tblsearch "LAYER" layer-name))
            (create_single_layer layer-data)
            (princ (strcat "\n图层 '" layer-name "' 已存在，跳过"))
        )
    )
    
    (princ "\n图层创建完成")
)

;;; 创建单个图层
(defun create_single_layer (layer-data / layer-name color linetype lineweight)
    (setq layer-name (car layer-data))
    (setq color (cadr layer-data))
    (setq linetype (caddr layer-data))
    (setq lineweight (cadddr layer-data))
    
    ;;; 创建图层
    (command "LAYER" "N" layer-name)
    
    ;;; 设置颜色
    (if color
        (command "C" color layer-name)
    )
    
    ;;; 设置线型
    (if linetype
        (command "L" linetype layer-name)
    )
    
    ;;; 设置线宽
    (if lineweight
        (command "LW" lineweight layer-name)
    )
    
    (command "")
    
    (princ (strcat "\n图层 '" layer-name "' 创建成功"))
)

;;; 读取图层配置文件
(defun read_layer_config (config-file / file-content lines layer-list)
    (setq file-content (open config-file "r"))
    (if (not file-content)
        (progn
            (princ "\n无法打开配置文件")
            nil
        )
        (progn
            (setq lines '())
            (while (setq line (read-line file-content))
                (setq lines (cons line lines))
            )
            (close file-content)
            
            ;;; 解析配置行
            (setq layer-list '())
            (foreach line (reverse lines)
                (if (and line (> (strlen line) 0))
                    (setq layer-list (cons (parse_layer_line line) layer-list))
                )
            )
            
            layer-list
        )
    )
)

;;; 解析图层配置行
(defun parse_layer_line (line / parts)
    (setq parts (parse_csv_line line))
    (if (>= (length parts) 4)
        (list (nth 0 parts)      ; 图层名称
              (nth 1 parts)      ; 颜色
              (nth 2 parts)      ; 线型
              (nth 3 parts)      ; 线宽
        )
        nil
    )
)

;;; 解析CSV行
(defun parse_csv_line (line / parts current-char in-quotes result)
    (setq parts '())
    (setq current-part "")
    (setq in-quotes nil)
    
    (foreach char (vl-string->list line)
        (setq current-char (chr char))
        
        (cond
            ((= current-char "\"")
             (setq in-quotes (not in-quotes))
            )
            ((and (= current-char ",") (not in-quotes))
             (setq parts (cons current-part parts))
             (setq current-part "")
            )
            (T
             (setq current-part (strcat current-part current-char))
            )
        )
    )
    
    ;;; 添加最后一个部分
    (setq parts (cons current-part parts))
    
    ;;; 返回反转后的列表
    (reverse parts)
)

;;; 批量修改图层
(defun modify_layers_batch (/ selection-set layer-name new-color new-linetype new-lineweight)
    ;;; 选择要修改的图层
    (setq layer-name (getstring "\n输入要修改的图层名称: "))
    (if (not (tblsearch "LAYER" layer-name))
        (progn
            (princ (strcat "\n图层 '" layer-name "' 不存在"))
            (exit)
        )
    )
    
    ;;; 获取新属性
    (setq new-color (getint "\n输入新颜色 (1-255): "))
    (setq new-linetype (getstring "\n输入新线型: "))
    (setq new-lineweight (getreal "\n输入新线宽: "))
    
    ;;; 修改图层属性
    (command "LAYER")
    
    (if new-color
        (command "C" new-color layer-name)
    )
    
    (if new-linetype
        (command "L" new-linetype layer-name)
    )
    
    (if new-lineweight
        (command "LW" new-lineweight layer-name)
    )
    
    (command "")
    
    (princ (strcat "\n图层 '" layer-name "' 修改完成"))
)

;;; 批量删除图层
(defun delete_layers_batch (/ layer-names layer-name)
    ;;; 获取要删除的图层列表
    (setq layer-names (getstring "\n输入要删除的图层名称 (用逗号分隔): "))
    (if (not layer-names)
        (progn
            (princ "\n未输入图层名称")
            (exit)
        )
    )
    
    ;;; 分割图层名称
    (foreach layer-name (parse_layer_names layer_names)
        (if (tblsearch "LAYER" layer-name)
            (progn
                (command "LAYER" "D" layer-name "")
                (princ (strcat "\n图层 '" layer-name "' 已删除"))
            )
            (princ (strcat "\n图层 '" layer-name "' 不存在"))
        )
    )
    
    (princ "\n图层删除完成")
)

;;; 解析图层名称列表
(defun parse_layer_names (layer-names / names)
    (setq names '())
    (while (setq pos (vl-string-search "," layer-names))
        (setq names (cons (substr layer-names 1 pos) names))
        (setq layer-names (substr layer-names (+ pos 2)))
    )
    (setq names (cons layer-names names))
    names
)

;;; 导出图层配置
(defun export_layers_config (/ config-file layers layer-data)
    ;;; 获取输出文件
    (setq config-file (getfiled "选择输出文件" "" "csv" 1))
    (if (not config-file)
        (progn
            (princ "\n未选择输出文件")
            (exit)
        )
    )
    
    ;;; 获取所有图层
    (setq layers (get_all_layers))
    
    ;;; 写入配置文件
    (setq file-output (open config-file "w"))
    (if file-output
        (progn
            ;;; 写入标题行
            (write-line "LayerName,Color,Linetype,LineWeight" file-output)
            
            ;;; 写入图层数据
            (foreach layer-data layers
                (write-line (format_layer_data layer-data) file-output)
            )
            
            (close file-output)
            (princ (strcat "\n图层配置已导出到: " config-file))
        )
        (princ "\n无法创建输出文件")
    )
)

;;; 获取所有图层
(defun get_all_layers (/ layers layer layer-data)
    (setq layers '())
    (setq layer (tblnext "LAYER" T))
    
    (while layer
        (setq layer-data (list 
                          (cdr (assoc 2 layer))    ; 图层名称
                          (cdr (assoc 62 layer))    ; 颜色
                          (cdr (assoc 6 layer))    ; 线型
                          (cdr (assoc 370 layer))  ; 线宽
                        ))
        (setq layers (cons layer-data layers))
        (setq layer (tblnext "LAYER"))
    )
    
    layers
)

;;; 格式化图层数据
(defun format_layer_data (layer-data / layer-name color linetype lineweight)
    (setq layer-name (car layer-data))
    (setq color (if (cadr layer-data) (itoa (cadr layer-data)) ""))
    (setq linetype (if (caddr layer-data) (caddr layer-data) ""))
    (setq lineweight (if (cadddr layer-data) (rtos (cadddr layer-data)) ""))
    
    (strcat "\"" layer-name "\"," color ",\"" linetype "\"," lineweight)
)

;;; 导入图层配置
(defun import_layers_config (/ config-file layer-list)
    ;;; 获取配置文件
    (setq config-file (getfiled "选择配置文件" "" "csv" 8))
    (if (not config-file)
        (progn
            (princ "\n未选择配置文件")
            (exit)
        )
    )
    
    ;;; 读取配置文件
    (setq layer-list (read_layer_config config-file))
    (if (not layer-list)
        (progn
            (princ "\n配置文件读取失败")
            (exit)
        )
    )
    
    ;;; 导入图层配置
    (foreach layer-data layer-list
        (import_single_layer layer-data)
    )
    
    (princ "\n图层配置导入完成")
)

;;; 导入单个图层
(defun import_single_layer (layer-data / layer-name color linetype lineweight)
    (setq layer-name (car layer-data))
    (setq color (cadr layer-data))
    (setq linetype (caddr layer-data))
    (setq lineweight (cadddr layer-data))
    
    ;;; 检查图层是否存在
    (if (tblsearch "LAYER" layer-name)
        (progn
            ;;; 修改现有图层
            (command "LAYER")
            
            (if color
                (command "C" color layer-name)
            )
            
            (if linetype
                (command "L" linetype layer-name)
            )
            
            (if lineweight
                (command "LW" lineweight layer-name)
            )
            
            (command "")
            
            (princ (strcat "\n图层 '" layer-name "' 已更新"))
        )
        (progn
            ;;; 创建新图层
            (create_single_layer layer-data)
        )
    )
)

;;; 生成图层报告
(defun generate_layer_report (/ report-file layers layer-data)
    ;;; 获取报告文件
    (setq report-file (getfiled "选择报告文件" "" "txt" 1))
    (if (not report-file)
        (progn
            (princ "\n未选择报告文件")
            (exit)
        )
    )
    
    ;;; 获取所有图层
    (setq layers (get_all_layers))
    
    ;;; 生成报告
    (setq file-output (open report-file "w"))
    (if file-output
        (progn
            ;;; 写入报告标题
            (write-line "AutoCAD 图层报告" file-output)
            (write-line "================" file-output)
            (write-line (strcat "生成时间: " (menucmd "M=$(edtime, $(getvar, date), YYYY-MO-DD HH:MM:SS)")) file-output)
            (write-line "" file-output)
            
            ;;; 写入图层统计
            (write-line (strcat "总图层数: " (itoa (length layers))) file-output)
            (write-line "" file-output)
            
            ;;; 写入图层详细信息
            (write-line "图层详细信息:" file-output)
            (write-line "--------------" file-output)
            
            (foreach layer-data layers
                (write-line (format_layer_report layer-data) file-output)
            )
            
            (close file-output)
            (princ (strcat "\n图层报告已生成: " report-file))
        )
        (princ "\n无法创建报告文件")
    )
)

;;; 格式化图层报告行
(defun format_layer_report (layer-data / layer-name color linetype lineweight)
    (setq layer-name (car layer-data))
    (setq color (if (cadr layer-data) (itoa (cadr layer-data)) "ByLayer"))
    (setq linetype (if (caddr layer-data) (caddr layer-data) "ByLayer"))
    (setq lineweight (if (cadddr layer-data) (rtos (cadddr layer-data)) "ByLayer"))
    
    (strcat "图层: " layer-name " | 颜色: " color " | 线型: " linetype " | 线宽: " lineweight)
)

;;; 使用示例：
;;; (c:SmartLayerManager) - 启动图层管理器
;;; (create_layers_batch) - 批量创建图层
;;; (modify_layers_batch) - 批量修改图层
;;; (delete_layers_batch) - 批量删除图层
;;; (export_layers_config) - 导出图层配置
;;; (import_layers_config) - 导入图层配置
;;; (generate_layer_report) - 生成图层报告
```

**VBA版本：**
```vba
' 智能图层管理器
' 文件名: SmartLayerManager.cls
' 作者: CAD自动化专家
' 版本: 1.0.0

Option Explicit

' 私有变量
Private m AcadApp As AcadApplication
Private m AcadDoc As AcadDocument
Private m Layers As AcadLayers

' 初始化
Private Sub Class_Initialize()
    Set m AcadApp = Application
    Set m AcadDoc = ThisDrawing
    Set m Layers = m AcadDoc.Layers
End Sub

' 清理
Private Sub Class_Terminate()
    Set m Layers = Nothing
    Set m AcadDoc = Nothing
    Set m AcadApp = Nothing
End Sub

' 主函数：智能图层管理器
Public Sub SmartLayerManager()
    Dim action As String
    
    ' 显示主菜单
    action = ShowMenu()
    
    Select Case action
        Case "CREATE"
            CreateLayersBatch
        Case "MODIFY"
            ModifyLayersBatch
        Case "DELETE"
            DeleteLayersBatch
        Case "EXPORT"
            ExportLayersConfig
        Case "IMPORT"
            ImportLayersConfig
        Case "REPORT"
            GenerateLayerReport
        Case Else
            MsgBox "操作已取消", vbInformation, "提示"
    End Select
End Sub

' 显示操作菜单
Private Function ShowMenu() As String
    Dim form As Object
    Dim result As String
    
    ' 创建用户窗体
    Set form = CreateObject("UserForm")
    form.Caption = "智能图层管理器"
    form.Width = 300
    form.Height = 200
    
    ' 添加控件
    Dim lblTitle As Object
    Set lblTitle = form.Controls.Add("Forms.Label.1")
    lblTitle.Caption = "请选择操作:"
    lblTitle.Left = 10
    lblTitle.Top = 10
    lblTitle.Width = 200
    
    Dim btnCreate As Object
    Set btnCreate = form.Controls.Add("Forms.CommandButton.1")
    btnCreate.Caption = "创建图层"
    btnCreate.Left = 10
    btnCreate.Top = 40
    btnCreate.Width = 100
    btnCreate.Tag = "CREATE"
    
    Dim btnModify As Object
    Set btnModify = form.Controls.Add("Forms.CommandButton.1")
    btnModify.Caption = "修改图层"
    btnModify.Left = 120
    btnModify.Top = 40
    btnModify.Width = 100
    btnModify.Tag = "MODIFY"
    
    Dim btnDelete As Object
    Set btnDelete = form.Controls.Add("Forms.CommandButton.1")
    btnDelete.Caption = "删除图层"
    btnDelete.Left = 10
    btnDelete.Top = 80
    btnDelete.Width = 100
    btnDelete.Tag = "DELETE"
    
    Dim btnExport As Object
    Set btnExport = form.Controls.Add("Forms.CommandButton.1")
    btnExport.Caption = "导出配置"
    btnExport.Left = 120
    btnExport.Top = 80
    btnExport.Width = 100
    btnExport.Tag = "EXPORT"
    
    Dim btnImport As Object
    Set btnImport = form.Controls.Add("Forms.CommandButton.1")
    btnImport.Caption = "导入配置"
    btnImport.Left = 10
    btnImport.Top = 120
    btnImport.Width = 100
    btnImport.Tag = "IMPORT"
    
    Dim btnReport As Object
    Set btnReport = form.Controls.Add("Forms.CommandButton.1")
    btnReport.Caption = "生成报告"
    btnReport.Left = 120
    btnReport.Top = 120
    btnReport.Width = 100
    btnReport.Tag = "REPORT"
    
    Dim btnCancel As Object
    Set btnCancel = form.Controls.Add("Forms.CommandButton.1")
    btnCancel.Caption = "取消"
    btnCancel.Left = 65
    btnCancel.Top = 160
    btnCancel.Width = 100
    btnCancel.Tag = "CANCEL"
    
    ' 显示窗体
    form.Show vbModal
    
    ShowMenu = result
End Function

' 批量创建图层
Public Sub CreateLayersBatch()
    On Error GoTo ErrorHandler
    
    Dim configFilePath As String
    Dim layerList As Collection
    Dim layerData As Variant
    Dim successCount As Long
    Dim failCount As Long
    
    ' 获取配置文件
    configFilePath = GetConfigFilePath("选择图层配置文件", "CSV文件 (*.csv)|*.csv|所有文件 (*.*)|*.*")
    If configFilePath = "" Then Exit Sub
    
    ' 读取配置文件
    Set layerList = ReadLayerConfig(configFilePath)
    If layerList Is Nothing Then
        MsgBox "配置文件读取失败", vbExclamation, "错误"
        Exit Sub
    End If
    
    ' 禁用屏幕更新
    m AcadDoc.SetVariable "REGENMODE", 0
    Application.ScreenUpdating = False
    
    ' 批量创建图层
    successCount = 0
    failCount = 0
    
    For Each layerData In layerList
        If CreateSingleLayer(layerData) Then
            successCount = successCount + 1
        Else
            failCount = failCount + 1
        End If
    Next layerData
    
    ' 恢复屏幕更新
    Application.ScreenUpdating = True
    m AcadDoc.SetVariable "REGENMODE", 1
    
    ' 显示结果
    MsgBox "图层创建完成!" & vbCrLf & _
           "成功: " & successCount & vbCrLf & _
           "失败: " & failCount, _
           vbInformation, "操作结果"
    
    Exit Sub
    
ErrorHandler:
    Application.ScreenUpdating = True
    m AcadDoc.SetVariable "REGENMODE", 1
    MsgBox "错误: " & Err.Description, vbCritical, "错误"
End Sub

' 创建单个图层
Private Function CreateSingleLayer(layerData As Variant) As Boolean
    On Error GoTo ErrorHandler
    
    Dim layerName As String
    Dim color As Integer
    Dim linetype As String
    Dim lineweight As Integer
    Dim layer As AcadLayer
    
    layerName = layerData(0)
    color = layerData(1)
    linetype = layerData(2)
    lineweight = layerData(3)
    
    ' 检查图层是否已存在
    If LayerExists(layerName) Then
        Debug.Print "图层 '" & layerName & "' 已存在，跳过"
        CreateSingleLayer = True
        Exit Function
    End If
    
    ' 创建图层
    Set layer = m Layers.Add(layerName)
    
    ' 设置属性
    If color > 0 Then layer.Color = color
    If linetype <> "" Then layer.Linetype = linetype
    If lineweight > 0 Then layer.LineWeight = lineweight
    
    Debug.Print "图层 '" & layerName & "' 创建成功"
    CreateSingleLayer = True
    
    Exit Function
    
ErrorHandler:
    Debug.Print "创建图层 '" & layerName & "' 失败: " & Err.Description
    CreateSingleLayer = False
End Function

' 检查图层是否存在
Private Function LayerExists(layerName As String) As Boolean
    On Error Resume Next
    Dim layer As AcadLayer
    Set layer = m Layers(layerName)
    LayerExists = Not layer Is Nothing
    On Error GoTo 0
End Function

' 读取图层配置文件
Private Function ReadLayerConfig(configFilePath As String) As Collection
    On Error GoTo ErrorHandler
    
    Dim fso As Object
    Dim file As Object
    Dim line As String
    Dim layerList As New Collection
    Dim layerData As Variant
    
    Set fso = CreateObject("Scripting.FileSystemObject")
    Set file = fso.OpenTextFile(configFilePath, 1) ' ForReading
    
    ' 跳过标题行
    If Not file.AtEndOfStream Then
        file.ReadLine
    End If
    
    ' 读取数据行
    Do While Not file.AtEndOfStream
        line = file.ReadLine
        If Trim(line) <> "" Then
            layerData = ParseCSVLine(line)
            If UBound(layerData) >= 3 Then
                layerList.Add layerData
            End If
        End If
    Loop
    
    file.Close
    Set ReadLayerConfig = layerList
    
    Exit Function
    
ErrorHandler:
    If Not file Is Nothing Then file.Close
    Set ReadLayerConfig = Nothing
End Function

' 解析CSV行
Private Function ParseCSVLine(line As String) As Variant
    Dim parts() As String
    Dim currentPart As String
    Dim inQuotes As Boolean
    Dim i As Integer
    Dim char As String
    Dim partCount As Integer
    
    ReDim parts(0)
    currentPart = ""
    inQuotes = False
    partCount = 0
    
    For i = 1 To Len(line)
        char = Mid(line, i, 1)
        
        Select Case char
            Case """"
                inQuotes = Not inQuotes
            Case ","
                If Not inQuotes Then
                    parts(partCount) = currentPart
                    partCount = partCount + 1
                    ReDim Preserve parts(partCount)
                    currentPart = ""
                Else
                    currentPart = currentPart & char
                End If
            Case Else
                currentPart = currentPart & char
        End Select
    Next i
    
    ' 添加最后一部分
    parts(partCount) = currentPart
    
    ParseCSVLine = parts
End Function

' 获取配置文件路径
Private Function GetConfigFilePath(title As String, filter As String) As String
    Dim dialog As Object
    Set dialog = CreateObject("UserAccounts.CommonDialog")
    
    dialog.Filter = filter
    dialog.Title = title
    dialog.ShowOpen
    
    GetConfigFilePath = dialog.FileName
End Function

' 批量修改图层
Public Sub ModifyLayersBatch()
    On Error GoTo ErrorHandler
    
    Dim layerName As String
    Dim newColor As Integer
    Dim newLinetype As String
    Dim newLineweight As Integer
    Dim layer As AcadLayer
    
    ' 获取图层名称
    layerName = InputBox("请输入要修改的图层名称:", "修改图层")
    If layerName = "" Then Exit Sub
    
    ' 检查图层是否存在
    If Not LayerExists(layerName) Then
        MsgBox "图层 '" & layerName & "' 不存在", vbExclamation, "错误"
        Exit Sub
    End If
    
    ' 获取新属性
    newColor = GetNewColor()
    newLinetype = GetNewLinetype()
    newLineweight = GetNewLineweight()
    
    ' 修改图层
    Set layer = m Layers(layerName)
    
    If newColor > 0 Then layer.Color = newColor
    If newLinetype <> "" Then layer.Linetype = newLinetype
    If newLineweight > 0 Then layer.LineWeight = newLineweight
    
    MsgBox "图层 '" & layerName & "' 修改完成", vbInformation, "成功"
    
    Exit Sub
    
ErrorHandler:
    MsgBox "错误: " & Err.Description, vbCritical, "错误"
End Sub

' 获取新颜色
Private Function GetNewColor() As Integer
    Dim colorStr As String
    colorStr = InputBox("请输入新颜色 (1-255, 0=ByLayer):", "颜色", "0")
    
    If colorStr = "" Then
        GetNewColor = 0
    ElseIf IsNumeric(colorStr) Then
        GetNewColor = CInt(colorStr)
    Else
        GetNewColor = 0
    End If
End Function

' 获取新线型
Private Function GetNewLinetype() As String
    GetNewLinetype = InputBox("请输入新线型 (留空=ByLayer):", "线型", "")
End Function

' 获取新线宽
Private Function GetNewLineweight() As Integer
    Dim weightStr As String
    weightStr = InputBox("请输入新线宽 (1-211, 0=ByLayer):", "线宽", "0")
    
    If weightStr = "" Then
        GetNewLineweight = 0
    ElseIf IsNumeric(weightStr) Then
        GetNewLineweight = CInt(weightStr)
    Else
        GetNewLineweight = 0
    End If
End Function

' 批量删除图层
Public Sub DeleteLayersBatch()
    On Error GoTo ErrorHandler
    
    Dim layerNames As String
    Dim nameArray() As String
    Dim i As Integer
    Dim layerName As String
    Dim successCount As Long
    Dim failCount As Long
    
    ' 获取图层名称列表
    layerNames = InputBox("请输入要删除的图层名称 (用逗号分隔):", "删除图层")
    If layerNames = "" Then Exit Sub
    
    ' 分割图层名称
    nameArray = Split(layerNames, ",")
    
    ' 禁用屏幕更新
    m AcadDoc.SetVariable "REGENMODE", 0
    Application.ScreenUpdating = False
    
    ' 批量删除图层
    successCount = 0
    failCount = 0
    
    For i = LBound(nameArray) To UBound(nameArray)
        layerName = Trim(nameArray(i))
        If layerName <> "" Then
            If DeleteSingleLayer(layerName) Then
                successCount = successCount + 1
            Else
                failCount = failCount + 1
            End If
        End If
    Next i
    
    ' 恢复屏幕更新
    Application.ScreenUpdating = True
    m AcadDoc.SetVariable "REGENMODE", 1
    
    ' 显示结果
    MsgBox "图层删除完成!" & vbCrLf & _
           "成功: " & successCount & vbCrLf & _
           "失败: " & failCount, _
           vbInformation, "操作结果"
    
    Exit Sub
    
ErrorHandler:
    Application.ScreenUpdating = True
    m AcadDoc.SetVariable "REGENMODE", 1
    MsgBox "错误: " & Err.Description, vbCritical, "错误"
End Sub

' 删除单个图层
Private Function DeleteSingleLayer(layerName As String) As Boolean
    On Error GoTo ErrorHandler
    
    If LayerExists(layerName) Then
        m Layers(layerName).Delete
        Debug.Print "图层 '" & layerName & "' 删除成功"
        DeleteSingleLayer = True
    Else
        Debug.Print "图层 '" & layerName & "' 不存在"
        DeleteSingleLayer = False
    End If
    
    Exit Function
    
ErrorHandler:
    Debug.Print "删除图层 '" & layerName & "' 失败: " & Err.Description
    DeleteSingleLayer = False
End Function

' 导出图层配置
Public Sub ExportLayersConfig()
    On Error GoTo ErrorHandler
    
    Dim configFilePath As String
    Dim file As Object
    Dim fso As Object
    Dim layer As AcadLayer
    Dim layerData As Variant
    
    ' 获取输出文件路径
    configFilePath = GetConfigFilePath("选择输出文件", "CSV文件 (*.csv)|*.csv|所有文件 (*.*)|*.*")
    If configFilePath = "" Then Exit Sub
    
    ' 创建文件系统对象
    Set fso = CreateObject("Scripting.FileSystemObject")
    Set file = fso.CreateTextFile(configFilePath, True)
    
    ' 写入标题行
    file.WriteLine "LayerName,Color,Linetype,LineWeight"
    
    ' 写入图层数据
    For Each layer In m Layers
        layerData = GetLayerData(layer)
        file.WriteLine FormatLayerData(layerData)
    Next layer
    
    file.Close
    
    MsgBox "图层配置已导出到:" & vbCrLf & configFilePath, vbInformation, "成功"
    
    Exit Sub
    
ErrorHandler:
    If Not file Is Nothing Then file.Close
    MsgBox "错误: " & Err.Description, vbCritical, "错误"
End Sub

' 获取图层数据
Private Function GetLayerData(layer As AcadLayer) As Variant
    Dim layerData(3) As Variant
    
    layerData(0) = layer.Name
    layerData(1) = layer.Color
    layerData(2) = layer.Linetype
    layerData(3) = layer.LineWeight
    
    GetLayerData = layerData
End Function

' 格式化图层数据
Private Function FormatLayerData(layerData As Variant) As String
    Dim layerName As String
    Dim color As String
    Dim linetype As String
    Dim lineweight As String
    
    layerName = layerData(0)
    color = CStr(layerData(1))
    linetype = layerData(2)
    lineweight = CStr(layerData(3))
    
    FormatLayerData = """" & layerName & """," & color & ",""" & linetype & """," & lineweight
End Function

' 导入图层配置
Public Sub ImportLayersConfig()
    On Error GoTo ErrorHandler
    
    Dim configFilePath As String
    Dim layerList As Collection
    Dim layerData As Variant
    Dim successCount As Long
    Dim failCount As Long
    
    ' 获取配置文件
    configFilePath = GetConfigFilePath("选择配置文件", "CSV文件 (*.csv)|*.csv|所有文件 (*.*)|*.*")
    If configFilePath = "" Then Exit Sub
    
    ' 读取配置文件
    Set layerList = ReadLayerConfig(configFilePath)
    If layerList Is Nothing Then
        MsgBox "配置文件读取失败", vbExclamation, "错误"
        Exit Sub
    End If
    
    ' 禁用屏幕更新
    m AcadDoc.SetVariable "REGENMODE", 0
    Application.ScreenUpdating = False
    
    ' 导入图层配置
    successCount = 0
    failCount = 0
    
    For Each layerData In layerList
        If ImportSingleLayer(layerData) Then
            successCount = successCount + 1
        Else
            failCount = failCount + 1
        End If
    Next layerData
    
    ' 恢复屏幕更新
    Application.ScreenUpdating = True
    m AcadDoc.SetVariable "REGENMODE", 1
    
    ' 显示结果
    MsgBox "图层配置导入完成!" & vbCrLf & _
           "成功: " & successCount & vbCrLf & _
           "失败: " & failCount, _
           vbInformation, "操作结果"
    
    Exit Sub
    
ErrorHandler:
    Application.ScreenUpdating = True
    m AcadDoc.SetVariable "REGENMODE", 1
    MsgBox "错误: " & Err.Description, vbCritical, "错误"
End Sub

' 导入单个图层
Private Function ImportSingleLayer(layerData As Variant) As Boolean
    On Error GoTo ErrorHandler
    
    Dim layerName As String
    Dim color As Integer
    Dim linetype As String
    Dim lineweight As Integer
    Dim layer As AcadLayer
    
    layerName = layerData(0)
    color = layerData(1)
    linetype = layerData(2)
    lineweight = layerData(3)
    
    ' 检查图层是否存在
    If LayerExists(layerName) Then
        ' 修改现有图层
        Set layer = m Layers(layerName)
        
        If color > 0 Then layer.Color = color
        If linetype <> "" Then layer.Linetype = linetype
        If lineweight > 0 Then layer.LineWeight = lineweight
        
        Debug.Print "图层 '" & layerName & "' 已更新"
    Else
        ' 创建新图层
        Set layer = m Layers.Add(layerName)
        
        If color > 0 Then layer.Color = color
        If linetype <> "" Then layer.Linetype = linetype
        If lineweight > 0 Then layer.LineWeight = lineweight
        
        Debug.Print "图层 '" & layerName & "' 已创建"
    End If
    
    ImportSingleLayer = True
    
    Exit Function
    
ErrorHandler:
    Debug.Print "导入图层 '" & layerName & "' 失败: " & Err.Description
    ImportSingleLayer = False
End Function

' 生成图层报告
Public Sub GenerateLayerReport()
    On Error GoTo ErrorHandler
    
    Dim reportFilePath As String
    Dim file As Object
    Dim fso As Object
    Dim layer As AcadLayer
    Dim layerData As Variant
    
    ' 获取报告文件路径
    reportFilePath = GetConfigFilePath("选择报告文件", "文本文件 (*.txt)|*.txt|所有文件 (*.*)|*.*")
    If reportFilePath = "" Then Exit Sub
    
    ' 创建文件系统对象
    Set fso = CreateObject("Scripting.FileSystemObject")
    Set file = fso.CreateTextFile(reportFilePath, True)
    
    ' 写入报告标题
    file.WriteLine "AutoCAD 图层报告"
    file.WriteLine "================"
    file.WriteLine "生成时间: " & Now
    file.WriteLine ""
    
    ' 写入图层统计
    file.WriteLine "总图层数: " & m Layers.Count
    file.WriteLine ""
    
    ' 写入图层详细信息
    file.WriteLine "图层详细信息:"
    file.WriteLine "--------------"
    
    For Each layer In m Layers
        layerData = GetLayerData(layer)
        file.WriteLine FormatLayerReport(layerData)
    Next layer
    
    file.Close
    
    MsgBox "图层报告已生成:" & vbCrLf & reportFilePath, vbInformation, "成功"
    
    Exit Sub
    
ErrorHandler:
    If Not file Is Nothing Then file.Close
    MsgBox "错误: " & Err.Description, vbCritical, "错误"
End Sub

' 格式化图层报告行
Private Function FormatLayerReport(layerData As Variant) As String
    Dim layerName As String
    Dim color As String
    Dim linetype As String
    Dim lineweight As String
    
    layerName = layerData(0)
    color = CStr(layerData(1))
    linetype = layerData(2)
    lineweight = CStr(layerData(3))
    
    FormatLayerReport = "图层: " & layerName & " | 颜色: " & color & " | 线型: " & linetype & " | 线宽: " & lineweight
End Function

' 使用示例：
' Dim layerManager As New SmartLayerManager
' layerManager.SmartLayerManager
' layerManager.CreateLayersBatch
' layerManager.ModifyLayersBatch
' layerManager.DeleteLayersBatch
' layerManager.ExportLayersConfig
' layerManager.ImportLayersConfig
' layerManager.GenerateLayerReport
```

**C#版本：**
```csharp
using Autodesk.AutoCAD.Runtime;
using Autodesk.AutoCAD.ApplicationServices;
using Autodesk.AutoCAD.DatabaseServices;
using Autodesk.AutoCAD.EditorInput;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;

namespace CADAutomation
{
    public class SmartLayerManager
    {
        // 主函数：智能图层管理器
        [CommandMethod("SmartLayerManager")]
        public void SmartLayerManager()
        {
            Document doc = Application.DocumentManager.MdiActiveDocument;
            Editor ed = doc.Editor;
            
            try
            {
                // 显示主菜单
                string action = ShowMenu(ed);
                
                switch (action.ToUpper())
                {
                    case "CREATE":
                        CreateLayersBatch(doc, ed);
                        break;
                    case "MODIFY":
                        ModifyLayersBatch(doc, ed);
                        break;
                    case "DELETE":
                        DeleteLayersBatch(doc, ed);
                        break;
                    case "EXPORT":
                        ExportLayersConfig(doc, ed);
                        break;
                    case "IMPORT":
                        ImportLayersConfig(doc, ed);
                        break;
                    case "REPORT":
                        GenerateLayerReport(doc, ed);
                        break;
                    default:
                        ed.WriteMessage("\n操作取消");
                        break;
                }
            }
            catch (System.Exception ex)
            {
                ed.WriteMessage($"\n错误: {ex.Message}");
            }
        }
        
        // 显示操作菜单
        private string ShowMenu(Editor ed)
        {
            PromptKeywordOptions pko = new PromptKeywordOptions("\n选择操作 [Create/Modify/Delete/Export/Import/Report]: ");
            pko.Keywords.Add("Create");
            pko.Keywords.Add("Modify");
            pko.Keywords.Add("Delete");
            pko.Keywords.Add("Export");
            pko.Keywords.Add("Import");
            pko.Keywords.Add("Report");
            pko.AllowNone = true;
            
            PromptResult pr = ed.GetKeywords(pko);
            return pr.Status == PromptStatus.OK ? pr.StringResult : "CANCEL";
        }
        
        // 批量创建图层
        [CommandMethod("CreateLayersBatch")]
        public void CreateLayersBatch()
        {
            Document doc = Application.DocumentManager.MdiActiveDocument;
            Editor ed = doc.Editor;
            CreateLayersBatch(doc, ed);
        }
        
        private void CreateLayersBatch(Document doc, Editor ed)
        {
            try
            {
                // 获取配置文件
                string configFilePath = GetConfigFilePath(ed, "选择图层配置文件", "CSV文件 (*.csv)|*.csv|所有文件 (*.*)|*.*");
                if (string.IsNullOrEmpty(configFilePath)) return;
                
                // 读取配置文件
                List<LayerData> layerList = ReadLayerConfig(configFilePath);
                if (layerList == null)
                {
                    ed.WriteMessage("\n配置文件读取失败");
                    return;
                }
                
                // 禁用屏幕更新
                using (DocumentLock docLock = doc.LockDocument())
                {
                    using (Transaction trans = doc.TransactionManager.StartTransaction())
                    {
                        Database db = doc.Database;
                        LayerTable layerTable = trans.GetObject(db.LayerTableId, OpenMode.ForRead) as LayerTable;
                        
                        int successCount = 0;
                        int failCount = 0;
                        
                        // 批量创建图层
                        foreach (LayerData layerData in layerList)
                        {
                            if (CreateSingleLayer(layerTable, layerData, trans))
                            {
                                successCount++;
                                ed.WriteMessage($"\n图层 '{layerData.Name}' 创建成功");
                            }
                            else
                            {
                                failCount++;
                                ed.WriteMessage($"\n图层 '{layerData.Name}' 创建失败");
                            }
                        }
                        
                        trans.Commit();
                        
                        // 显示结果
                        ed.WriteMessage($"\n图层创建完成! 成功: {successCount}, 失败: {failCount}");
                    }
                }
            }
            catch (System.Exception ex)
            {
                ed.WriteMessage($"\n错误: {ex.Message}");
            }
        }
        
        // 创建单个图层
        private bool CreateSingleLayer(LayerTable layerTable, LayerData layerData, Transaction trans)
        {
            try
            {
                // 检查图层是否已存在
                if (layerTable.Has(layerData.Name))
                {
                    return true; // 图层已存在，跳过
                }
                
                // 创建新图层
                LayerTableRecord layer = new LayerTableRecord();
                layer.Name = layerData.Name;
                
                // 设置属性
                if (layerData.Color > 0) layer.Color = Color.FromColorIndex(ColorMethod.ByColor, layerData.Color);
                if (!string.IsNullOrEmpty(layerData.Linetype)) layer.Linetype = layerData.Linetype;
                if (layerData.LineWeight > 0) layer.LineWeight = layerData.LineWeight;
                
                // 添加到图层表
                layerTable.UpgradeOpen();
                layerTable.Add(layer);
                trans.AddNewlyCreatedDBObject(layer, true);
                
                return true;
            }
            catch (System.Exception)
            {
                return false;
            }
        }
        
        // 图层数据结构
        private class LayerData
        {
            public string Name { get; set; }
            public int Color { get; set; }
            public string Linetype { get; set; }
            public int LineWeight { get; set; }
        }
        
        // 读取图层配置文件
        private List<LayerData> ReadLayerConfig(string configFilePath)
        {
            try
            {
                List<LayerData> layerList = new List<LayerData>();
                string[] lines = File.ReadAllLines(configFilePath);
                
                // 跳过标题行
                for (int i = 1; i < lines.Length; i++)
                {
                    string line = lines[i].Trim();
                    if (!string.IsNullOrEmpty(line))
                    {
                        LayerData layerData = ParseCSVLine(line);
                        if (layerData != null)
                        {
                            layerList.Add(layerData);
                        }
                    }
                }
                
                return layerList;
            }
            catch (System.Exception)
            {
                return null;
            }
        }
        
        // 解析CSV行
        private LayerData ParseCSVLine(string line)
        {
            try
            {
                string[] parts = ParseCSV(line);
                if (parts.Length >= 4)
                {
                    return new LayerData
                    {
                        Name = parts[0].Trim('"'),
                        Color = int.TryParse(parts[1], out int color) ? color : 0,
                        Linetype = parts[2].Trim('"'),
                        LineWeight = int.TryParse(parts[3], out int weight) ? weight : 0
                    };
                }
                return null;
            }
            catch (System.Exception)
            {
                return null;
            }
        }
        
        // 解析CSV
        private string[] ParseCSV(string line)
        {
            List<string> parts = new List<string>();
            string currentPart = "";
            bool inQuotes = false;
            
            for (int i = 0; i < line.Length; i++)
            {
                char c = line[i];
                
                if (c == '"')
                {
                    inQuotes = !inQuotes;
                }
                else if (c == ',' && !inQuotes)
                {
                    parts.Add(currentPart);
                    currentPart = "";
                }
                else
                {
                    currentPart += c;
                }
            }
            
            parts.Add(currentPart);
            return parts.ToArray();
        }
        
        // 获取配置文件路径
        private string GetConfigFilePath(Editor ed, string title, string filter)
        {
            PromptOpenFileOptions pfo = new PromptOpenFileOptions(title);
            pfo.Filter = filter;
            pfo.AllowReadOnly = true;
            
            PromptFileNameResult pfr = ed.GetFileNameForOpen(pfo);
            return pfr.Status == PromptStatus.OK ? pfr.StringResult : string.Empty;
        }
        
        // 批量修改图层
        [CommandMethod("ModifyLayersBatch")]
        public void ModifyLayersBatch()
        {
            Document doc = Application.DocumentManager.MdiActiveDocument;
            Editor ed = doc.Editor;
            ModifyLayersBatch(doc, ed);
        }
        
        private void ModifyLayersBatch(Document doc, Editor ed)
        {
            try
            {
                // 获取图层名称
                PromptStringOptions pso = new PromptStringOptions("\n请输入要修改的图层名称: ");
                PromptResult pr = ed.GetString(pso);
                
                if (pr.Status != PromptStatus.OK || string.IsNullOrEmpty(pr.StringResult))
                    return;
                
                string layerName = pr.StringResult;
                
                // 获取新属性
                int newColor = GetNewColor(ed);
                string newLinetype = GetNewLinetype(ed);
                int newLineweight = GetNewLineweight(ed);
                
                // 修改图层
                using (DocumentLock docLock = doc.LockDocument())
                {
                    using (Transaction trans = doc.TransactionManager.StartTransaction())
                    {
                        Database db = doc.Database;
                        LayerTable layerTable = trans.GetObject(db.LayerTableId, OpenMode.ForRead) as LayerTable;
                        
                        if (!layerTable.Has(layerName))
                        {
                            ed.WriteMessage($"\n图层 '{layerName}' 不存在");
                            return;
                        }
                        
                        LayerTableRecord layer = trans.GetObject(layerTable[layerName], OpenMode.ForWrite) as LayerTableRecord;
                        
                        if (newColor > 0) layer.Color = Color.FromColorIndex(ColorMethod.ByColor, newColor);
                        if (!string.IsNullOrEmpty(newLinetype)) layer.Linetype = newLinetype;
                        if (newLineweight > 0) layer.LineWeight = newLineweight;
                        
                        trans.Commit();
                        ed.WriteMessage($"\n图层 '{layerName}' 修改完成");
                    }
                }
            }
            catch (System.Exception ex)
            {
                ed.WriteMessage($"\n错误: {ex.Message}");
            }
        }
        
        // 获取新颜色
        private int GetNewColor(Editor ed)
        {
            PromptIntegerOptions pio = new PromptIntegerOptions("\n请输入新颜色 (1-255, 0=ByLayer): ");
            pio.AllowNegative = false;
            pio.AllowZero = true;
            pio.DefaultValue = 0;
            
            PromptIntegerResult pir = ed.GetInteger(pio);
            return pir.Status == PromptStatus.OK ? pir.Value : 0;
        }
        
        // 获取新线型
        private string GetNewLinetype(Editor ed)
        {
            PromptStringOptions pso = new PromptStringOptions("\n请输入新线型 (留空=ByLayer): ");
            pso.AllowSpaces = true;
            
            PromptResult pr = ed.GetString(pso);
            return pr.Status == PromptStatus.OK ? pr.StringResult : string.Empty;
        }
        
        // 获取新线宽
        private int GetNewLineweight(Editor ed)
        {
            PromptIntegerOptions pio = new PromptIntegerOptions("\n请输入新线宽 (1-211, 0=ByLayer): ");
            pio.AllowNegative = false;
            pio.AllowZero = true;
            pio.DefaultValue = 0;
            
            PromptIntegerResult pir = ed.GetInteger(pio);
            return pir.Status == PromptStatus.OK ? pir.Value : 0;
        }
        
        // 批量删除图层
        [CommandMethod("DeleteLayersBatch")]
        public void DeleteLayersBatch()
        {
            Document doc = Application.DocumentManager.MdiActiveDocument;
            Editor ed = doc.Editor;
            DeleteLayersBatch(doc, ed);
        }
        
        private void DeleteLayersBatch(Document doc, Editor ed)
        {
            try
            {
                // 获取图层名称列表
                PromptStringOptions pso = new PromptStringOptions("\n请输入要删除的图层名称 (用逗号分隔): ");
                PromptResult pr = ed.GetString(pso);
                
                if (pr.Status != PromptStatus.OK || string.IsNullOrEmpty(pr.StringResult))
                    return;
                
                string[] layerNames = pr.StringResult.Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries);
                
                // 批量删除图层
                using (DocumentLock docLock = doc.LockDocument())
                {
                    using (Transaction trans = doc.TransactionManager.StartTransaction())
                    {
                        Database db = doc.Database;
                        LayerTable layerTable = trans.GetObject(db.LayerTableId, OpenMode.ForRead) as LayerTable;
                        
                        int successCount = 0;
                        int failCount = 0;
                        
                        foreach (string layerName in layerNames)
                        {
                            string trimmedName = layerName.Trim();
                            if (!string.IsNullOrEmpty(trimmedName))
                            {
                                if (DeleteSingleLayer(layerTable, trimmedName, trans))
                                {
                                    successCount++;
                                    ed.WriteMessage($"\n图层 '{trimmedName}' 删除成功");
                                }
                                else
                                {
                                    failCount++;
                                    ed.WriteMessage($"\n图层 '{trimmedName}' 删除失败");
                                }
                            }
                        }
                        
                        trans.Commit();
                        
                        // 显示结果
                        ed.WriteMessage($"\n图层删除完成! 成功: {successCount}, 失败: {failCount}");
                    }
                }
            }
            catch (System.Exception ex)
            {
                ed.WriteMessage($"\n错误: {ex.Message}");
            }
        }
        
        // 删除单个图层
        private bool DeleteSingleLayer(LayerTable layerTable, string layerName, Transaction trans)
        {
            try
            {
                if (layerTable.Has(layerName))
                {
                    LayerTableRecord layer = trans.GetObject(layerTable[layerName], OpenMode.ForWrite) as LayerTableRecord;
                    layer.Erase(true);
                    return true;
                }
                return false;
            }
            catch (System.Exception)
            {
                return false;
            }
        }
        
        // 导出图层配置
        [CommandMethod("ExportLayersConfig")]
        public void ExportLayersConfig()
        {
            Document doc = Application.DocumentManager.MdiActiveDocument;
            Editor ed = doc.Editor;
            ExportLayersConfig(doc, ed);
        }
        
        private void ExportLayersConfig(Document doc, Editor ed)
        {
            try
            {
                // 获取输出文件路径
                string configFilePath = GetConfigFilePath(ed, "选择输出文件", "CSV文件 (*.csv)|*.csv|所有文件 (*.*)|*.*");
                if (string.IsNullOrEmpty(configFilePath)) return;
                
                // 导出图层配置
                using (DocumentLock docLock = doc.LockDocument())
                {
                    using (Transaction trans = doc.TransactionManager.StartTransaction())
                    {
                        Database db = doc.Database;
                        LayerTable layerTable = trans.GetObject(db.LayerTableId, OpenMode.ForRead) as LayerTable;
                        
                        using (StreamWriter writer = new StreamWriter(configFilePath))
                        {
                            // 写入标题行
                            writer.WriteLine("LayerName,Color,Linetype,LineWeight");
                            
                            // 写入图层数据
                            foreach (ObjectId layerId in layerTable)
                            {
                                LayerTableRecord layer = trans.GetObject(layerId, OpenMode.ForRead) as LayerTableRecord;
                                LayerData layerData = new LayerData
                                {
                                    Name = layer.Name,
                                    Color = layer.Color.ColorIndex,
                                    Linetype = layer.Linetype,
                                    LineWeight = (int)layer.LineWeight
                                };
                                
                                writer.WriteLine(FormatLayerData(layerData));
                            }
                        }
                        
                        ed.WriteMessage($"\n图层配置已导出到: {configFilePath}");
                    }
                }
            }
            catch (System.Exception ex)
            {
                ed.WriteMessage($"\n错误: {ex.Message}");
            }
        }
        
        // 格式化图层数据
        private string FormatLayerData(LayerData layerData)
        {
            return $"\"{layerData.Name}\",{layerData.Color},\"{layerData.Linetype}\",{layerData.LineWeight}";
        }
        
        // 导入图层配置
        [CommandMethod("ImportLayersConfig")]
        public void ImportLayersConfig()
        {
            Document doc = Application.DocumentManager.MdiActiveDocument;
            Editor ed = doc.Editor;
            ImportLayersConfig(doc, ed);
        }
        
        private void ImportLayersConfig(Document doc, Editor ed)
        {
            try
            {
                // 获取配置文件
                string configFilePath = GetConfigFilePath(ed, "选择配置文件", "CSV文件 (*.csv)|*.csv|所有文件 (*.*)|*.*");
                if (string.IsNullOrEmpty(configFilePath)) return;
                
                // 读取配置文件
                List<LayerData> layerList = ReadLayerConfig(configFilePath);
                if (layerList == null)
                {
                    ed.WriteMessage("\n配置文件读取失败");
                    return;
                }
                
                // 导入图层配置
                using (DocumentLock docLock = doc.LockDocument())
                {
                    using (Transaction trans = doc.TransactionManager.StartTransaction())
                    {
                        Database db = doc.Database;
                        LayerTable layerTable = trans.GetObject(db.LayerTableId, OpenMode.ForRead) as LayerTable;
                        
                        int successCount = 0;
                        int failCount = 0;
                        
                        foreach (LayerData layerData in layerList)
                        {
                            if (ImportSingleLayer(layerTable, layerData, trans))
                            {
                                successCount++;
                                ed.WriteMessage($"\n图层 '{layerData.Name}' 导入成功");
                            }
                            else
                            {
                                failCount++;
                                ed.WriteMessage($"\n图层 '{layerData.Name}' 导入失败");
                            }
                        }
                        
                        trans.Commit();
                        
                        // 显示结果
                        ed.WriteMessage($"\n图层配置导入完成! 成功: {successCount}, 失败: {failCount}");
                    }
                }
            }
            catch (System.Exception ex)
            {
                ed.WriteMessage($"\n错误: {ex.Message}");
            }
        }
        
        // 导入单个图层
        private bool ImportSingleLayer(LayerTable layerTable, LayerData layerData, Transaction trans)
        {
            try
            {
                if (layerTable.Has(layerData.Name))
                {
                    // 修改现有图层
                    LayerTableRecord layer = trans.GetObject(layerTable[layerData.Name], OpenMode.ForWrite) as LayerTableRecord;
                    
                    if (layerData.Color > 0) layer.Color = Color.FromColorIndex(ColorMethod.ByColor, layerData.Color);
                    if (!string.IsNullOrEmpty(layerData.Linetype)) layer.Linetype = layerData.Linetype;
                    if (layerData.LineWeight > 0) layer.LineWeight = layerData.LineWeight;
                }
                else
                {
                    // 创建新图层
                    return CreateSingleLayer(layerTable, layerData, trans);
                }
                
                return true;
            }
            catch (System.Exception)
            {
                return false;
            }
        }
        
        // 生成图层报告
        [CommandMethod("GenerateLayerReport")]
        public void GenerateLayerReport()
        {
            Document doc = Application.DocumentManager.MdiActiveDocument;
            Editor ed = doc.Editor;
            GenerateLayerReport(doc, ed);
        }
        
        private void GenerateLayerReport(Document doc, Editor ed)
        {
            try
            {
                // 获取报告文件路径
                string reportFilePath = GetConfigFilePath(ed, "选择报告文件", "文本文件 (*.txt)|*.txt|所有文件 (*.*)|*.*");
                if (string.IsNullOrEmpty(reportFilePath)) return;
                
                // 生成报告
                using (DocumentLock docLock = doc.LockDocument())
                {
                    using (Transaction trans = doc.TransactionManager.StartTransaction())
                    {
                        Database db = doc.Database;
                        LayerTable layerTable = trans.GetObject(db.LayerTableId, OpenMode.ForRead) as LayerTable;
                        
                        using (StreamWriter writer = new StreamWriter(reportFilePath))
                        {
                            // 写入报告标题
                            writer.WriteLine("AutoCAD 图层报告");
                            writer.WriteLine("================");
                            writer.WriteLine($"生成时间: {DateTime.Now}");
                            writer.WriteLine("");
                            
                            // 写入图层统计
                            writer.WriteLine($"总图层数: {layerTable.Count}");
                            writer.WriteLine("");
                            
                            // 写入图层详细信息
                            writer.WriteLine("图层详细信息:");
                            writer.WriteLine("--------------");
                            
                            foreach (ObjectId layerId in layerTable)
                            {
                                LayerTableRecord layer = trans.GetObject(layerId, OpenMode.ForRead) as LayerTableRecord;
                                LayerData layerData = new LayerData
                                {
                                    Name = layer.Name,
                                    Color = layer.Color.ColorIndex,
                                    Linetype = layer.Linetype,
                                    LineWeight = (int)layer.LineWeight
                                };
                                
                                writer.WriteLine(FormatLayerReport(layerData));
                            }
                        }
                        
                        ed.WriteMessage($"\n图层报告已生成: {reportFilePath}");
                    }
                }
            }
            catch (System.Exception ex)
            {
                ed.WriteMessage($"\n错误: {ex.Message}");
            }
        }
        
        // 格式化图层报告行
        private string FormatLayerReport(LayerData layerData)
        {
            return $"图层: {layerData.Name} | 颜色: {layerData.Color} | 线型: {layerData.Linetype} | 线宽: {layerData.LineWeight}";
        }
    }
}
```

#### 案例2：自动化绘图工具集
**目标**：创建一个综合性的自动化绘图工具集，包含多种常用绘图功能

**AutoLISP版本：**
```lisp
;;; 自动化绘图工具集
;;; 文件名: auto_drawing_tools.lsp
;;; 作者: CAD自动化专家
;;; 版本: 1.0.0

;;; 主函数：自动化绘图工具集
(defun c:AutoDrawingTools (/ action)
    ;;; 显示主菜单
    (setq action (show_drawing_menu))
    
    (cond
        ((= action "GRID") (draw_grid_pattern))
        ((= action "ARRAY") (draw_array_pattern))
        ((= action "POLYGON") (draw_regular_polygon))
        ((= action "SPIRAL") (draw_spiral))
        ((= action "STAR") (draw_star_pattern))
        (T (princ "\n操作取消"))
    )
    
    (princ)
)

;;; 显示绘图工具菜单
(defun show_drawing_menu (/ choice)
    (initget "Grid Array Polygon Spiral Star")
    (setq choice (getkword "\n选择绘图工具 [Grid/Array/Polygon/Spiral/Star]: "))
    (if choice (strcase choice) "CANCEL")
)

;;; 绘制网格图案
(defun draw_grid_pattern (/ rows cols spacing start-x start-y i j)
    ;;; 获取用户输入
    (setq rows (getint "输入行数: "))
    (setq cols (getint "输入列数: "))
    (setq spacing (getreal "输入间距: "))
    (setq start-x (getreal "输入起始X坐标: "))
    (setq start-y (getreal "输入起始Y坐标: "))
    
    ;;; 绘制网格
    (setq i 0)
    (repeat rows
        (setq y (+ start-y (* i spacing)))
        (setq j 0)
        (repeat cols
            (setq x (+ start-x (* j spacing)))
            ;;; 绘制圆点
            (command "circle" (list x y 0) (/ spacing 8))
            (setq j (1+ j))
        )
        (setq i (1+ i))
    )
    
    (princ "\n网格图案绘制完成")
)

;;; 绘制阵列图案
(defun draw_array_pattern (/ rows cols item-size spacing start-x start-y i j)
    ;;; 获取用户输入
    (setq rows (getint "输入行数: "))
    (setq cols (getint "输入列数: "))
    (setq item-size (getreal "输入项目大小: "))
    (setq spacing (getreal "输入间距: "))
    (setq start-x (getreal "输入起始X坐标: "))
    (setq start-y (getreal "输入起始Y坐标: "))
    
    ;;; 绘制阵列
    (setq i 0)
    (repeat rows
        (setq y (+ start-y (* i spacing)))
        (setq j 0)
        (repeat cols
            (setq x (+ start-x (* j spacing)))
            ;;; 绘制矩形
            (command "rectangle" 
                    (list (- x (/ item-size 2)) (- y (/ item-size 2)) 0)
                    (list (+ x (/ item-size 2)) (+ y (/ item-size 2)) 0)
            )
            (setq j (1+ j))
        )
        (setq i (1+ i))
    )
    
    (princ "\n阵列图案绘制完成")
)

;;; 绘制正多边形
(defun draw_regular_polygon (/ sides radius center-point inscribed)
    ;;; 获取用户输入
    (setq sides (getint "输入边数 (3-20): "))
    (if (or (< sides 3) (> sides 20))
        (progn
            (alert "边数必须在3-20之间")
            (exit)
        )
    )
    
    (setq radius (getreal "输入半径: "))
    (setq center-point (getpoint "\n选择中心点: "))
    (setq inscribed (getstring "内接圆? [Y/N]: "))
    
    ;;; 绘制多边形
    (command "polygon" sides center-point 
            (if (= (strcase inscribed) "Y") "I" "C") 
            radius
    )
    
    (princ "\n正多边形绘制完成")
)

;;; 绘制螺旋线
(defun draw_spiral (/ center-point start-radius end-radius turns points-per-turn i angle radius point)
    ;;; 获取用户输入
    (setq center-point (getpoint "\n选择中心点: "))
    (setq start-radius (getreal "输入起始半径: "))
    (setq end-radius (getreal "输入结束半径: "))
    (setq turns (getreal "输入圈数: "))
    (setq points-per-turn (getint "输入每圈点数: "))
    
    ;;; 绘制螺旋线
    (setq i 0)
    (setq total-points (* turns points-per-turn))
    (setq spiral-points '())
    
    (repeat total-points
        (setq angle (* i (/ (* 2 pi) points-per-turn)))
        (setq radius (+ start-radius (* (- end-radius start-radius) (/ i total-points))))
        (setq point (list 
                     (+ (car center-point) (* radius (cos angle)))
                     (+ (cadr center-point) (* radius (sin angle)))
                     0
                   ))
        (setq spiral-points (cons point spiral-points))
        (setq i (1+ i))
    )
    
    ;;; 创建多段线
    (command "pline")
    (foreach point (reverse spiral-points)
        (command point)
    )
    (command "")
    
    (princ "\n螺旋线绘制完成")
)

;;; 绘制星形图案
(defun draw_star_pattern (/ center-point outer-radius inner-radius points i angle outer-point inner-point)
    ;;; 获取用户输入
    (setq center-point (getpoint "\n选择中心点: "))
    (setq outer-radius (getreal "输入外径: "))
    (setq inner-radius (getreal "输入内径: "))
    (setq points (getint "输入角数: "))
    
    ;;; 绘制星形
    (setq star-points '())
    (setq i 0)
    
    (repeat (* points 2)
        (setq angle (* i (/ pi points)))
        (if (= (rem i 2) 0)
            ;;; 外点
            (setq outer-point (list 
                                (+ (car center-point) (* outer-radius (cos angle)))
                                (+ (cadr center-point) (* outer-radius (sin angle)))
                                0
                              ))
            ;;; 内点
            (setq inner-point (list 
                                (+ (car center-point) (* inner-radius (cos angle)))
                                (+ (cadr center-point) (* inner-radius (sin angle)))
                                0
                              ))
        )
        
        (if (= (rem i 2) 0)
            (setq star-points (cons outer-point star-points))
            (setq star-points (cons inner-point star-points))
        )
        
        (setq i (1+ i))
    )
    
    ;;; 创建闭合多段线
    (command "pline")
    (foreach point (reverse star-points)
        (command point)
    )
    (command "c")
    
    (princ "\n星形图案绘制完成")
)

;;; 使用示例：
;;; (c:AutoDrawingTools) - 启动绘图工具集
;;; (draw_grid_pattern) - 绘制网格图案
;;; (draw_array_pattern) - 绘制阵列图案
;;; (draw_regular_polygon) - 绘制正多边形
;;; (draw_spiral) - 绘制螺旋线
;;; (draw_star_pattern) - 绘制星形图案
```

**VBA版本：**
```vba
' 自动化绘图工具集
' 文件名: AutoDrawingTools.cls
' 作者: CAD自动化专家
' 版本: 1.0.0

Option Explicit

' 私有变量
Private m AcadApp As AcadApplication
Private m AcadDoc As AcadDocument
Private m ModelSpace As AcadModelSpace

' 初始化
Private Sub Class_Initialize()
    Set m AcadApp = Application
    Set m AcadDoc = ThisDrawing
    Set m ModelSpace = m AcadDoc.ModelSpace
End Sub

' 清理
Private Sub Class_Terminate()
    Set m ModelSpace = Nothing
    Set m AcadDoc = Nothing
    Set m AcadApp = Nothing
End Sub

' 主函数：自动化绘图工具集
Public Sub AutoDrawingTools()
    Dim action As String
    
    ' 显示主菜单
    action = ShowDrawingMenu()
    
    Select Case action
        Case "GRID"
            DrawGridPattern
        Case "ARRAY"
            DrawArrayPattern
        Case "POLYGON"
            DrawRegularPolygon
        Case "SPIRAL"
            DrawSpiral
        Case "STAR"
            DrawStarPattern
        Case Else
            MsgBox "操作已取消", vbInformation, "提示"
    End Select
End Sub

' 显示绘图工具菜单
Private Function ShowDrawingMenu() As String
    Dim form As Object
    Dim result As String
    
    ' 创建用户窗体
    Set form = CreateObject("UserForm")
    form.Caption = "自动化绘图工具集"
    form.Width = 300
    form.Height = 200
    
    ' 添加控件
    Dim lblTitle As Object
    Set lblTitle = form.Controls.Add("Forms.Label.1")
    lblTitle.Caption = "请选择绘图工具:"
    lblTitle.Left = 10
    lblTitle.Top = 10
    lblTitle.Width = 200
    
    Dim btnGrid As Object
    Set btnGrid = form.Controls.Add("Forms.CommandButton.1")
    btnGrid.Caption = "网格图案"
    btnGrid.Left = 10
    btnGrid.Top = 40
    btnGrid.Width = 100
    btnGrid.Tag = "GRID"
    
    Dim btnArray As Object
    Set btnArray = form.Controls.Add("Forms.CommandButton.1")
    btnArray.Caption = "阵列图案"
    btnArray.Left = 120
    btnArray.Top = 40
    btnArray.Width = 100
    btnArray.Tag = "ARRAY"
    
    Dim btnPolygon As Object
    Set btnPolygon = form.Controls.Add("Forms.CommandButton.1")
    btnPolygon.Caption = "正多边形"
    btnPolygon.Left = 10
    btnPolygon.Top = 80
    btnPolygon.Width = 100
    btnPolygon.Tag = "POLYGON"
    
    Dim btnSpiral As Object
    Set btnSpiral = form.Controls.Add("Forms.CommandButton.1")
    btnSpiral.Caption = "螺旋线"
    btnSpiral.Left = 120
    btnSpiral.Top = 80
    btnSpiral.Width = 100
    btnSpiral.Tag = "SPIRAL"
    
    Dim btnStar As Object
    Set btnStar = form.Controls.Add("Forms.CommandButton.1")
    btnStar.Caption = "星形图案"
    btnStar.Left = 65
    btnStar.Top = 120
    btnStar.Width = 100
    btnStar.Tag = "STAR"
    
    Dim btnCancel As Object
    Set btnCancel = form.Controls.Add("Forms.CommandButton.1")
    btnCancel.Caption = "取消"
    btnCancel.Left = 65
    btnCancel.Top = 160
    btnCancel.Width = 100
    btnCancel.Tag = "CANCEL"
    
    ' 显示窗体
    form.Show vbModal
    
    ShowDrawingMenu = result
End Function

' 绘制网格图案
Public Sub DrawGridPattern()
    On Error GoTo ErrorHandler
    
    Dim rows As Integer
    Dim cols As Integer
    Dim spacing As Double
    Dim startX As Double
    Dim startY As Double
    Dim i As Integer
    Dim j As Integer
    Dim circleObj As AcadCircle
    Dim centerPoint(0 To 2) As Double
    
    ' 获取用户输入
    rows = CInt(InputBox("输入行数:", "网格图案", "5"))
    cols = CInt(InputBox("输入列数:", "网格图案", "5"))
    spacing = CDbl(InputBox("输入间距:", "网格图案", "10"))
    startX = CDbl(InputBox("输入起始X坐标:", "网格图案", "0"))
    startY = CDbl(InputBox("输入起始Y坐标:", "网格图案", "0"))
    
    ' 禁用屏幕更新
    Application.ScreenUpdating = False
    
    ' 绘制网格
    For i = 0 To rows - 1
        For j = 0 To cols - 1
            centerPoint(0) = startX + j * spacing
            centerPoint(1) = startY + i * spacing
            centerPoint(2) = 0
            
            Set circleObj = m ModelSpace.AddCircle(centerPoint, spacing / 8)
            circleObj.Color = acByLayer
            circleObj.Linetype = "ByLayer"
        Next j
    Next i
    
    ' 恢复屏幕更新
    Application.ScreenUpdating = True
    m AcadDoc.Regen acAllViewports
    
    MsgBox "网格图案绘制完成!", vbInformation, "成功"
    
    Exit Sub
    
ErrorHandler:
    Application.ScreenUpdating = True
    MsgBox "错误: " & Err.Description, vbCritical, "错误"
End Sub

' 绘制阵列图案
Public Sub DrawArrayPattern()
    On Error GoTo ErrorHandler
    
    Dim rows As Integer
    Dim cols As Integer
    Dim itemSize As Double
    Dim spacing As Double
    Dim startX As Double
    Dim startY As Double
    Dim i As Integer
    Dim j As Integer
    Dim rectObj As AcadLWPolyline
    Dim points(0 To 7) As Double
    
    ' 获取用户输入
    rows = CInt(InputBox("输入行数:", "阵列图案", "3"))
    cols = CInt(InputBox("输入列数:", "阵列图案", "3"))
    itemSize = CDbl(InputBox("输入项目大小:", "阵列图案", "8"))
    spacing = CDbl(InputBox("输入间距:", "阵列图案", "15"))
    startX = CDbl(InputBox("输入起始X坐标:", "阵列图案", "0"))
    startY = CDbl(InputBox("输入起始Y坐标:", "阵列图案", "0"))
    
    ' 禁用屏幕更新
    Application.ScreenUpdating = False
    
    ' 绘制阵列
    For i = 0 To rows - 1
        For j = 0 To cols - 1
            ' 计算矩形顶点
            points(0) = startX + j * spacing - itemSize / 2
            points(1) = startY + i * spacing - itemSize / 2
            points(2) = startX + j * spacing + itemSize / 2
            points(3) = startY + i * spacing - itemSize / 2
            points(4) = startX + j * spacing + itemSize / 2
            points(5) = startY + i * spacing + itemSize / 2
            points(6) = startX + j * spacing - itemSize / 2
            points(7) = startY + i * spacing + itemSize / 2
            
            Set rectObj = m ModelSpace.AddLightWeightPolyline(points)
            rectObj.Closed = True
            rectObj.Color = acByLayer
            rectObj.Linetype = "ByLayer"
        Next j
    Next i
    
    ' 恢复屏幕更新
    Application.ScreenUpdating = True
    m AcadDoc.Regen acAllViewports
    
    MsgBox "阵列图案绘制完成!", vbInformation, "成功"
    
    Exit Sub
    
ErrorHandler:
    Application.ScreenUpdating = True
    MsgBox "错误: " & Err.Description, vbCritical, "错误"
End Sub

' 绘制正多边形
Public Sub DrawRegularPolygon()
    On Error GoTo ErrorHandler
    
    Dim sides As Integer
    Dim radius As Double
    Dim centerPoint As Variant
    Dim inscribed As String
    Dim polygonObj As AcadLWPolyline
    Dim points() As Double
    Dim i As Integer
    Dim angle As Double
    
    ' 获取用户输入
    sides = CInt(InputBox("输入边数 (3-20):", "正多边形", "6"))
    If sides < 3 Or sides > 20 Then
        MsgBox "边数必须在3-20之间", vbExclamation, "输入错误"
        Exit Sub
    End If
    
    radius = CDbl(InputBox("输入半径:", "正多边形", "10"))
    centerPoint = ThisDrawing.Utility.GetPoint(, "选择中心点: ")
    inscribed = InputBox("内接圆? [Y/N]:", "正多边形", "Y")
    
    ' 计算多边形顶点
    ReDim points(0 To sides * 2 - 1)
    
    For i = 0 To sides - 1
        angle = i * 2 * 3.14159265358979 / sides
        If UCase(inscribed) = "Y" Then
            points(i * 2) = centerPoint(0) + radius * Cos(angle)
            points(i * 2 + 1) = centerPoint(1) + radius * Sin(angle)
        Else
            points(i * 2) = centerPoint(0) + radius * Cos(angle) / Cos(3.14159265358979 / sides)
            points(i * 2 + 1) = centerPoint(1) + radius * Sin(angle) / Cos(3.14159265358979 / sides)
        End If
    Next i
    
    ' 创建多边形
    Set polygonObj = m ModelSpace.AddLightWeightPolyline(points)
    polygonObj.Closed = True
    polygonObj.Color = acByLayer
    polygonObj.Linetype = "ByLayer"
    
    m AcadDoc.Regen acAllViewports
    
    MsgBox "正多边形绘制完成!", vbInformation, "成功"
    
    Exit Sub
    
ErrorHandler:
    MsgBox "错误: " & Err.Description, vbCritical, "错误"
End Sub

' 绘制螺旋线
Public Sub DrawSpiral()
    On Error GoTo ErrorHandler
    
    Dim centerPoint As Variant
    Dim startRadius As Double
    Dim endRadius As Double
    Dim turns As Double
    Dim pointsPerTurn As Integer
    Dim spiralObj As AcadSpline
    Dim points() As Double
    Dim totalPoints As Integer
    Dim i As Integer
    Dim angle As Double
    Dim radius As Double
    
    ' 获取用户输入
    centerPoint = ThisDrawing.Utility.GetPoint(, "选择中心点: ")
    startRadius = CDbl(InputBox("输入起始半径:", "螺旋线", "5"))
    endRadius = CDbl(InputBox("输入结束半径:", "螺旋线", "20"))
    turns = CDbl(InputBox("输入圈数:", "螺旋线", "3"))
    pointsPerTurn = CInt(InputBox("输入每圈点数:", "螺旋线", "20"))
    
    ' 计算螺旋线点
    totalPoints = CInt(turns * pointsPerTurn)
    ReDim points(0 To totalPoints * 3 - 1)
    
    For i = 0 To totalPoints - 1
        angle = i * 2 * 3.14159265358979 / pointsPerTurn
        radius = startRadius + (endRadius - startRadius) * i / totalPoints
        
        points(i * 3) = centerPoint(0) + radius * Cos(angle)
        points(i * 3 + 1) = centerPoint(1) + radius * Sin(angle)
        points(i * 3 + 2) = 0
    Next i
    
    ' 创建螺旋线
    Set spiralObj = m ModelSpace.AddSpline(points, Empty, Empty, 1, 0)
    spiralObj.Color = acByLayer
    spiralObj.Linetype = "ByLayer"
    
    m AcadDoc.Regen acAllViewports
    
    MsgBox "螺旋线绘制完成!", vbInformation, "成功"
    
    Exit Sub
    
ErrorHandler:
    MsgBox "错误: " & Err.Description, vbCritical, "错误"
End Sub

' 绘制星形图案
Public Sub DrawStarPattern()
    On Error GoTo ErrorHandler
    
    Dim centerPoint As Variant
    Dim outerRadius As Double
    Dim innerRadius As Double
    Dim points As Integer
    Dim starObj As AcadLWPolyline
    Dim starPoints() As Double
    Dim i As Integer
    Dim angle As Double
    
    ' 获取用户输入
    centerPoint = ThisDrawing.Utility.GetPoint(, "选择中心点: ")
    outerRadius = CDbl(InputBox("输入外径:", "星形图案", "20"))
    innerRadius = CDbl(InputBox("输入内径:", "星形图案", "10"))
    points = CInt(InputBox("输入角数:", "星形图案", "5"))
    
    ' 计算星形顶点
    ReDim starPoints(0 To points * 4 - 1)
    
    For i = 0 To points * 2 - 1
        angle = i * 3.14159265358979 / points
        
        If i Mod 2 = 0 Then
            ' 外点
            starPoints(i * 2) = centerPoint(0) + outerRadius * Cos(angle)
            starPoints(i * 2 + 1) = centerPoint(1) + outerRadius * Sin(angle)
        Else
            ' 内点
            starPoints(i * 2) = centerPoint(0) + innerRadius * Cos(angle)
            starPoints(i * 2 + 1) = centerPoint(1) + innerRadius * Sin(angle)
        End If
    Next i
    
    ' 创建星形
    Set starObj = m ModelSpace.AddLightWeightPolyline(starPoints)
    starObj.Closed = True
    starObj.Color = acByLayer
    starObj.Linetype = "ByLayer"
    
    m AcadDoc.Regen acAllViewports
    
    MsgBox "星形图案绘制完成!", vbInformation, "成功"
    
    Exit Sub
    
ErrorHandler:
    MsgBox "错误: " & Err.Description, vbCritical, "错误"
End Sub

' 使用示例：
' Dim drawingTools As New AutoDrawingTools
' drawingTools.AutoDrawingTools
' drawingTools.DrawGridPattern
' drawingTools.DrawArrayPattern
' drawingTools.DrawRegularPolygon
' drawingTools.DrawSpiral
' drawingTools.DrawStarPattern
```

**C#版本：**
```csharp
using Autodesk.AutoCAD.Runtime;
using Autodesk.AutoCAD.ApplicationServices;
using Autodesk.AutoCAD.DatabaseServices;
using Autodesk.AutoCAD.EditorInput;
using Autodesk.AutoCAD.Geometry;
using System;

namespace CADAutomation
{
    public class AutoDrawingTools
    {
        // 主函数：自动化绘图工具集
        [CommandMethod("AutoDrawingTools")]
        public void AutoDrawingTools()
        {
            Document doc = Application.DocumentManager.MdiActiveDocument;
            Editor ed = doc.Editor;
            
            try
            {
                // 显示主菜单
                string action = ShowDrawingMenu(ed);
                
                switch (action.ToUpper())
                {
                    case "GRID":
                        DrawGridPattern(doc, ed);
                        break;
                    case "ARRAY":
                        DrawArrayPattern(doc, ed);
                        break;
                    case "POLYGON":
                        DrawRegularPolygon(doc, ed);
                        break;
                    case "SPIRAL":
                        DrawSpiral(doc, ed);
                        break;
                    case "STAR":
                        DrawStarPattern(doc, ed);
                        break;
                    default:
                        ed.WriteMessage("\n操作取消");
                        break;
                }
            }
            catch (System.Exception ex)
            {
                ed.WriteMessage($"\n错误: {ex.Message}");
            }
        }
        
        // 显示绘图工具菜单
        private string ShowDrawingMenu(Editor ed)
        {
            PromptKeywordOptions pko = new PromptKeywordOptions("\n选择绘图工具 [Grid/Array/Polygon/Spiral/Star]: ");
            pko.Keywords.Add("Grid");
            pko.Keywords.Add("Array");
            pko.Keywords.Add("Polygon");
            pko.Keywords.Add("Spiral");
            pko.Keywords.Add("Star");
            pko.AllowNone = true;
            
            PromptResult pr = ed.GetKeywords(pko);
            return pr.Status == PromptStatus.OK ? pr.StringResult : "CANCEL";
        }
        
        // 绘制网格图案
        [CommandMethod("DrawGridPattern")]
        public void DrawGridPattern()
        {
            Document doc = Application.DocumentManager.MdiActiveDocument;
            Editor ed = doc.Editor;
            DrawGridPattern(doc, ed);
        }
        
        private void DrawGridPattern(Document doc, Editor ed)
        {
            try
            {
                // 获取用户输入
                PromptIntegerOptions pioRows = new PromptIntegerOptions("\n输入行数: ");
                pioRows.DefaultValue = 5;
                PromptIntegerResult pirRows = ed.GetInteger(pioRows);
                if (pirRows.Status != PromptStatus.OK) return;
                
                PromptIntegerOptions pioCols = new PromptIntegerOptions("\n输入列数: ");
                pioCols.DefaultValue = 5;
                PromptIntegerResult pirCols = ed.GetInteger(pioCols);
                if (pirCols.Status != PromptStatus.OK) return;
                
                PromptDoubleOptions pdoSpacing = new PromptDoubleOptions("\n输入间距: ");
                pdoSpacing.DefaultValue = 10.0;
                PromptDoubleResult pdoSpacingResult = ed.GetDouble(pdoSpacing);
                if (pdoSpacingResult.Status != PromptStatus.OK) return;
                
                PromptDoubleOptions pdoStartX = new PromptDoubleOptions("\n输入起始X坐标: ");
                pdoStartX.DefaultValue = 0.0;
                PromptDoubleResult pdoStartXResult = ed.GetDouble(pdoStartX);
                if (pdoStartXResult.Status != PromptStatus.OK) return;
                
                PromptDoubleOptions pdoStartY = new PromptDoubleOptions("\n输入起始Y坐标: ");
                pdoStartY.DefaultValue = 0.0;
                PromptDoubleResult pdoStartYResult = ed.GetDouble(pdoStartY);
                if (pdoStartYResult.Status != PromptStatus.OK) return;
                
                int rows = pirRows.Value;
                int cols = pirCols.Value;
                double spacing = pdoSpacingResult.Value;
                double startX = pdoStartXResult.Value;
                double startY = pdoStartYResult.Value;
                
                // 绘制网格
                using (DocumentLock docLock = doc.LockDocument())
                {
                    using (Transaction trans = doc.TransactionManager.StartTransaction())
                    {
                        Database db = doc.Database;
                        BlockTableRecord btr = trans.GetObject(db.CurrentSpaceId, OpenMode.ForWrite) as BlockTableRecord;
                        
                        for (int i = 0; i < rows; i++)
                        {
                            for (int j = 0; j < cols; j++)
                            {
                                Point3d center = new Point3d(
                                    startX + j * spacing,
                                    startY + i * spacing,
                                    0
                                );
                                
                                Circle circle = new Circle(center, Vector3d.ZAxis, spacing / 8);
                                btr.AppendEntity(circle);
                                trans.AddNewlyCreatedDBObject(circle, true);
                            }
                        }
                        
                        trans.Commit();
                    }
                }
                
                ed.WriteMessage("\n网格图案绘制完成");
            }
            catch (System.Exception ex)
            {
                ed.WriteMessage($"\n错误: {ex.Message}");
            }
        }
        
        // 绘制阵列图案
        [CommandMethod("DrawArrayPattern")]
        public void DrawArrayPattern()
        {
            Document doc = Application.DocumentManager.MdiActiveDocument;
            Editor ed = doc.Editor;
            DrawArrayPattern(doc, ed);
        }
        
        private void DrawArrayPattern(Document doc, Editor ed)
        {
            try
            {
                // 获取用户输入
                PromptIntegerOptions pioRows = new PromptIntegerOptions("\n输入行数: ");
                pioRows.DefaultValue = 3;
                PromptIntegerResult pirRows = ed.GetInteger(pioRows);
                if (pirRows.Status != PromptStatus.OK) return;
                
                PromptIntegerOptions pioCols = new PromptIntegerOptions("\n输入列数: ");
                pioCols.DefaultValue = 3;
                PromptIntegerResult pirCols = ed.GetInteger(pioCols);
                if (pirCols.Status != PromptStatus.OK) return;
                
                PromptDoubleOptions pdoItemSize = new PromptDoubleOptions("\n输入项目大小: ");
                pdoItemSize.DefaultValue = 8.0;
                PromptDoubleResult pdoItemSizeResult = ed.GetDouble(pdoItemSize);
                if (pdoItemSizeResult.Status != PromptStatus.OK) return;
                
                PromptDoubleOptions pdoSpacing = new PromptDoubleOptions("\n输入间距: ");
                pdoSpacing.DefaultValue = 15.0;
                PromptDoubleResult pdoSpacingResult = ed.GetDouble(pdoSpacing);
                if (pdoSpacingResult.Status != PromptStatus.OK) return;
                
                PromptDoubleOptions pdoStartX = new PromptDoubleOptions("\n输入起始X坐标: ");
                pdoStartX.DefaultValue = 0.0;
                PromptDoubleResult pdoStartXResult = ed.GetDouble(pdoStartX);
                if (pdoStartXResult.Status != PromptStatus.OK) return;
                
                PromptDoubleOptions pdoStartY = new PromptDoubleOptions("\n输入起始Y坐标: ");
                pdoStartY.DefaultValue = 0.0;
                PromptDoubleResult pdoStartYResult = ed.GetDouble(pdoStartY);
                if (pdoStartYResult.Status != PromptStatus.OK) return;
                
                int rows = pirRows.Value;
                int cols = pirCols.Value;
                double itemSize = pdoItemSizeResult.Value;
                double spacing = pdoSpacingResult.Value;
                double startX = pdoStartXResult.Value;
                double startY = pdoStartYResult.Value;
                
                // 绘制阵列
                using (DocumentLock docLock = doc.LockDocument())
                {
                    using (Transaction trans = doc.TransactionManager.StartTransaction())
                    {
                        Database db = doc.Database;
                        BlockTableRecord btr = trans.GetObject(db.CurrentSpaceId, OpenMode.ForWrite) as BlockTableRecord;
                        
                        for (int i = 0; i < rows; i++)
                        {
                            for (int j = 0; j < cols; j++)
                            {
                                double x = startX + j * spacing;
                                double y = startY + i * spacing;
                                
                                Point2d[] vertices = new Point2d[]
                                {
                                    new Point2d(x - itemSize / 2, y - itemSize / 2),
                                    new Point2d(x + itemSize / 2, y - itemSize / 2),
                                    new Point2d(x + itemSize / 2, y + itemSize / 2),
                                    new Point2d(x - itemSize / 2, y + itemSize / 2)
                                };
                                
                                Polyline rect = new Polyline();
                                rect.CreatePolygon(vertices);
                                
                                btr.AppendEntity(rect);
                                trans.AddNewlyCreatedDBObject(rect, true);
                            }
                        }
                        
                        trans.Commit();
                    }
                }
                
                ed.WriteMessage("\n阵列图案绘制完成");
            }
            catch (System.Exception ex)
            {
                ed.WriteMessage($"\n错误: {ex.Message}");
            }
        }
        
        // 绘制正多边形
        [CommandMethod("DrawRegularPolygon")]
        public void DrawRegularPolygon()
        {
            Document doc = Application.DocumentManager.MdiActiveDocument;
            Editor ed = doc.Editor;
            DrawRegularPolygon(doc, ed);
        }
        
        private void DrawRegularPolygon(Document doc, Editor ed)
        {
            try
            {
                // 获取用户输入
                PromptIntegerOptions pioSides = new PromptIntegerOptions("\n输入边数 (3-20): ");
                pioSides.DefaultValue = 6;
                pioSides.LowerLimit = 3;
                pioSides.UpperLimit = 20;
                PromptIntegerResult pirSides = ed.GetInteger(pioSides);
                if (pirSides.Status != PromptStatus.OK) return;
                
                PromptDoubleOptions pdoRadius = new PromptDoubleOptions("\n输入半径: ");
                pdoRadius.DefaultValue = 10.0;
                PromptDoubleResult pdoRadiusResult = ed.GetDouble(pdoRadius);
                if (pdoRadiusResult.Status != PromptStatus.OK) return;
                
                PromptPointOptions ppoCenter = new PromptPointOptions("\n选择中心点: ");
                PromptPointResult pprCenter = ed.GetPoint(ppoCenter);
                if (pprCenter.Status != PromptStatus.OK) return;
                
                PromptKeywordOptions pkoInscribed = new PromptKeywordOptions("\n内接圆? [Yes/No]: ");
                pkoInscribed.Keywords.Add("Yes");
                pkoInscribed.Keywords.Add("No");
                pkoInscribed.AllowNone = true;
                pkoInscribed.DefaultValue = "Yes";
                PromptResult prInscribed = ed.GetKeywords(pkoInscribed);
                if (prInscribed.Status != PromptStatus.OK) return;
                
                int sides = pirSides.Value;
                double radius = pdoRadiusResult.Value;
                Point3d center = pprCenter.Value;
                bool inscribed = prInscribed.StringResult.ToUpper() == "YES";
                
                // 绘制多边形
                using (DocumentLock docLock = doc.LockDocument())
                {
                    using (Transaction trans = doc.TransactionManager.StartTransaction())
                    {
                        Database db = doc.Database;
                        BlockTableRecord btr = trans.GetObject(db.CurrentSpaceId, OpenMode.ForWrite) as BlockTableRecord;
                        
                        Point2d[] vertices = new Point2d[sides];
                        double angleStep = 2 * Math.PI / sides;
                        
                        for (int i = 0; i < sides; i++)
                        {
                            double angle = i * angleStep;
                            double r = inscribed ? radius : radius / Math.Cos(Math.PI / sides);
                            
                            vertices[i] = new Point2d(
                                center.X + r * Math.Cos(angle),
                                center.Y + r * Math.Sin(angle)
                            );
                        }
                        
                        Polyline polygon = new Polyline();
                        polygon.CreatePolygon(vertices);
                        
                        btr.AppendEntity(polygon);
                        trans.AddNewlyCreatedDBObject(polygon, true);
                        
                        trans.Commit();
                    }
                }
                
                ed.WriteMessage("\n正多边形绘制完成");
            }
            catch (System.Exception ex)
            {
                ed.WriteMessage($"\n错误: {ex.Message}");
            }
        }
        
        // 绘制螺旋线
        [CommandMethod("DrawSpiral")]
        public void DrawSpiral()
        {
            Document doc = Application.DocumentManager.MdiActiveDocument;
            Editor ed = doc.Editor;
            DrawSpiral(doc, ed);
        }
        
        private void DrawSpiral(Document doc, Editor ed)
        {
            try
            {
                // 获取用户输入
                PromptPointOptions ppoCenter = new PromptPointOptions("\n选择中心点: ");
                PromptPointResult pprCenter = ed.GetPoint(ppoCenter);
                if (pprCenter.Status != PromptStatus.OK) return;
                
                PromptDoubleOptions pdoStartRadius = new PromptDoubleOptions("\n输入起始半径: ");
                pdoStartRadius.DefaultValue = 5.0;
                PromptDoubleResult pdoStartRadiusResult = ed.GetDouble(pdoStartRadius);
                if (pdoStartRadiusResult.Status != PromptStatus.OK) return;
                
                PromptDoubleOptions pdoEndRadius = new PromptDoubleOptions("\n输入结束半径: ");
                pdoEndRadius.DefaultValue = 20.0;
                PromptDoubleResult pdoEndRadiusResult = ed.GetDouble(pdoEndRadius);
                if (pdoEndRadiusResult.Status != PromptStatus.OK) return;
                
                PromptDoubleOptions pdoTurns = new PromptDoubleOptions("\n输入圈数: ");
                pdoTurns.DefaultValue = 3.0;
                PromptDoubleResult pdoTurnsResult = ed.GetDouble(pdoTurns);
                if (pdoTurnsResult.Status != PromptStatus.OK) return;
                
                PromptIntegerOptions pioPointsPerTurn = new PromptIntegerOptions("\n输入每圈点数: ");
                pioPointsPerTurn.DefaultValue = 20;
                PromptIntegerResult pioPointsPerTurnResult = ed.GetInteger(pioPointsPerTurn);
                if (pioPointsPerTurnResult.Status != PromptStatus.OK) return;
                
                Point3d center = pprCenter.Value;
                double startRadius = pdoStartRadiusResult.Value;
                double endRadius = pdoEndRadiusResult.Value;
                double turns = pdoTurnsResult.Value;
                int pointsPerTurn = pioPointsPerTurnResult.Value;
                
                // 绘制螺旋线
                using (DocumentLock docLock = doc.LockDocument())
                {
                    using (Transaction trans = doc.TransactionManager.StartTransaction())
                    {
                        Database db = doc.Database;
                        BlockTableRecord btr = trans.GetObject(db.CurrentSpaceId, OpenMode.ForWrite) as BlockTableRecord;
                        
                        int totalPoints = (int)(turns * pointsPerTurn);
                        Point3dCollection points = new Point3dCollection();
                        
                        for (int i = 0; i < totalPoints; i++)
                        {
                            double angle = i * 2 * Math.PI / pointsPerTurn;
                            double radius = startRadius + (endRadius - startRadius) * i / totalPoints;
                            
                            Point3d point = new Point3d(
                                center.X + radius * Math.Cos(angle),
                                center.Y + radius * Math.Sin(angle),
                                0
                            );
                            
                            points.Add(point);
                        }
                        
                        Spline spiral = new Spline(points, 1, 0);
                        
                        btr.AppendEntity(spiral);
                        trans.AddNewlyCreatedDBObject(spiral, true);
                        
                        trans.Commit();
                    }
                }
                
                ed.WriteMessage("\n螺旋线绘制完成");
            }
            catch (System.Exception ex)
            {
                ed.WriteMessage($"\n错误: {ex.Message}");
            }
        }
        
        // 绘制星形图案
        [CommandMethod("DrawStarPattern")]
        public void DrawStarPattern()
        {
            Document doc = Application.DocumentManager.MdiActiveDocument;
            Editor ed = doc.Editor;
            DrawStarPattern(doc, ed);
        }
        
        private void DrawStarPattern(Document doc, Editor ed)
        {
            try
            {
                // 获取用户输入
                PromptPointOptions ppoCenter = new PromptPointOptions("\n选择中心点: ");
                PromptPointResult pprCenter = ed.GetPoint(ppoCenter);
                if (pprCenter.Status != PromptStatus.OK) return;
                
                PromptDoubleOptions pdoOuterRadius = new PromptDoubleOptions("\n输入外径: ");
                pdoOuterRadius.DefaultValue = 20.0;
                PromptDoubleResult pdoOuterRadiusResult = ed.GetDouble(pdoOuterRadius);
                if (pdoOuterRadiusResult.Status != PromptStatus.OK) return;
                
                PromptDoubleOptions pdoInnerRadius = new PromptDoubleOptions("\n输入内径: ");
                pdoInnerRadius.DefaultValue = 10.0;
                PromptDoubleResult pdoInnerRadiusResult = ed.GetDouble(pdoInnerRadius);
                if (pdoInnerRadiusResult.Status != PromptStatus.OK) return;
                
                PromptIntegerOptions pioPoints = new PromptIntegerOptions("\n输入角数: ");
                pioPoints.DefaultValue = 5;
                PromptIntegerResult pioPointsResult = ed.GetInteger(pioPoints);
                if (pioPointsResult.Status != PromptStatus.OK) return;
                
                Point3d center = pprCenter.Value;
                double outerRadius = pdoOuterRadiusResult.Value;
                double innerRadius = pdoInnerRadiusResult.Value;
                int points = pioPointsResult.Value;
                
                // 绘制星形
                using (DocumentLock docLock = doc.LockDocument())
                {
                    using (Transaction trans = doc.TransactionManager.StartTransaction())
                    {
                        Database db = doc.Database;
                        BlockTableRecord btr = trans.GetObject(db.CurrentSpaceId, OpenMode.ForWrite) as BlockTableRecord;
                        
                        Point2d[] vertices = new Point2d[points * 2];
                        double angleStep = Math.PI / points;
                        
                        for (int i = 0; i < points * 2; i++)
                        {
                            double angle = i * angleStep;
                            double radius = (i % 2 == 0) ? outerRadius : innerRadius;
                            
                            vertices[i] = new Point2d(
                                center.X + radius * Math.Cos(angle),
                                center.Y + radius * Math.Sin(angle)
                            );
                        }
                        
                        Polyline star = new Polyline();
                        star.CreatePolygon(vertices);
                        
                        btr.AppendEntity(star);
                        trans.AddNewlyCreatedDBObject(star, true);
                        
                        trans.Commit();
                    }
                }
                
                ed.WriteMessage("\n星形图案绘制完成");
            }
            catch (System.Exception ex)
            {
                ed.WriteMessage($"\n错误: {ex.Message}");
            }
        }
    }
}
```

#### 案例3：智能尺寸标注工具
**目标**：创建一个智能尺寸标注工具，可以自动识别图形并添加适当的标注

**AutoLISP版本：**
```lisp
;;; 智能尺寸标注工具
;;; 文件名: smart_dimension_tool.lsp
;;; 作者: CAD自动化专家
;;; 版本: 1.0.0

;;; 主函数：智能尺寸标注工具
(defun c:SmartDimensionTool (/ action)
    ;;; 显示主菜单
    (setq action (show_dimension_menu))
    
    (cond
        ((= action "AUTO") (auto_dimension_selection))
        ((= action "LINEAR") (batch_linear_dimension))
        ((= action "RADIAL") (batch_radial_dimension))
        ((= action "ANGULAR") (batch_angular_dimension))
        ((= action "ALIGN") (batch_aligned_dimension))
        ((= action "CLEAN") (clean_dimensions))
        (T (princ "\n操作取消"))
    )
    
    (princ)
)

;;; 显示标注工具菜单
(defun show_dimension_menu (/ choice)
    (initget "Auto Linear Radial Angular Align Clean")
    (setq choice (getkword "\n选择标注工具 [Auto/Linear/Radial/Angular/Align/Clean]: "))
    (if choice (strcase choice) "CANCEL")
)

;;; 自动标注选择
(defun auto_dimension_selection (/ selection-set entity-type entity-data)
    ;;; 选择要标注的对象
    (setq selection-set (ssget))
    (if (not selection-set)
        (progn
            (princ "\n未选择对象")
            (exit)
        )
    )
    
    ;;; 分析每个对象并添加适当的标注
    (setq index 0)
    (repeat (sslength selection-set)
        (setq entity (ssname selection-set index))
        (setq entity-data (entget entity))
        (setq entity-type (cdr (assoc 0 entity-data)))
        
        (cond
            ((= entity-type "LINE")
                (add_linear_dimension entity)
            )
            ((= entity-type "CIRCLE")
                (add_radial_dimension entity)
            )
            ((= entity-type "ARC")
                (add_radial_dimension entity)
            )
            ((= entity-type "LWPOLYLINE")
                (add_polyline_dimensions entity)
            )
            ((= entity-type "POLYLINE")
                (add_polyline_dimensions entity)
            )
            (T
                (princ (strcat "\n跳过不支持的实体类型: " entity-type))
            )
        )
        
        (setq index (1+ index))
    )
    
    (princ "\n自动标注完成")
)

;;; 添加线性标注
(defun add_linear_dimension (entity / entity-data start-point end-point dimension-point)
    (setq entity-data (entget entity))
    (setq start-point (cdr (assoc 10 entity-data)))
    (setq end-point (cdr (assoc 11 entity-data)))
    
    ;;; 计算标注位置
    (setq dimension-point (calculate_dimension_position start-point end-point))
    
    ;;; 添加线性标注
    (command "dimlinear" start-point end-point dimension-point)
)

;;; 添加径向标注
(defun add_radial_dimension (entity / entity-data center-point radius dimension-point)
    (setq entity-data (entget entity))
    (setq center-point (cdr (assoc 10 entity-data)))
    (setq radius (cdr (assoc 40 entity-data)))
    
    ;;; 计算标注位置
    (setq dimension-point (list 
                           (+ (car center-point) radius) 
                           (cadr center-point) 
                           0
                         ))
    
    ;;; 添加径向标注
    (command "dimradius" entity dimension-point)
)

;;; 添加多段线标注
(defun add_polyline_dimensions (entity / entity-data vertices vertex-count i)
    (setq entity-data (entget entity))
    (setq vertices (get_polyline_vertices entity-data))
    (setq vertex-count (length vertices))
    
    ;;; 为每个线段添加标注
    (setq i 0)
    (repeat (- vertex-count 1)
        (setq start-point (nth i vertices))
        (setq end-point (nth (1+ i) vertices))
        
        ;;; 添加线性标注
        (add_linear_segment_dimension start-point end-point)
        
        (setq i (1+ i))
    )
)

;;; 获取多段线顶点
(defun get_polyline_vertices (entity-data / vertices vertex-count i vertex)
    (setq vertices '())
    (setq vertex-count (cdr (assoc 90 entity-data)))
    
    (setq i 0)
    (repeat vertex-count
        (setq vertex (list 
                      (cdr (assoc 10 (nth i entity-data)))
                      (cdr (assoc 20 (nth i entity-data)))
                      (cdr (assoc 30 (nth i entity-data)))
                    ))
        (setq vertices (cons vertex vertices))
        (setq i (1+ i))
    )
    
    (reverse vertices)
)

;;; 添加线段标注
(defun add_linear_segment_dimension (start-point end-point / dimension-point)
    (setq dimension-point (calculate_dimension_position start-point end-point))
    (command "dimaligned" start-point end-point dimension-point)
)

;;; 计算标注位置
(defun calculate_dimension_position (start-point end-point / mid-point offset direction)
    (setq mid-point (list 
                      (/ (+ (car start-point) (car end-point)) 2)
                      (/ (+ (cadr start-point) (cadr end-point)) 2)
                      0
                    ))
    
    ;;; 计算垂直偏移方向
    (setq direction (list 
                      (- (cadr end-point) (cadr start-point))
                      (- (car start-point) (car end-point))
                      0
                    ))
    
    ;;; 归一化方向向量
    (setq length (sqrt (+ (* (car direction) (car direction)) (* (cadr direction) (cadr direction)))))
    (if (> length 0)
        (setq direction (list 
                          (/ (car direction) length)
                          (/ (cadr direction) length)
                          0
                        ))
        (setq direction '(0 1 0))
    )
    
    ;;; 计算偏移点
    (setq offset 5) ; 标注偏移距离
    (list 
      (+ (car mid-point) (* offset (car direction)))
      (+ (cadr mid-point) (* offset (cadr direction)))
      0
    )
)

;;; 批量线性标注
(defun batch_linear_dimension (/ selection-set index entity)
    ;;; 选择直线对象
    (setq selection-set (ssget '((0 . "LINE"))))
    (if (not selection-set)
        (progn
            (princ "\n未选择直线对象")
            (exit)
        )
    )
    
    ;;; 批量添加线性标注
    (setq index 0)
    (repeat (sslength selection-set)
        (setq entity (ssname selection-set index))
        (add_linear_dimension entity)
        (setq index (1+ index))
    )
    
    (princ "\n批量线性标注完成")
)

;;; 批量径向标注
(defun batch_radial_dimension (/ selection-set index entity)
    ;;; 选择圆形和圆弧对象
    (setq selection-set (ssget '((0 . "CIRCLE,ARC"))))
    (if (not selection-set)
        (progn
            (princ "\n未选择圆形或圆弧对象")
            (exit)
        )
    )
    
    ;;; 批量添加径向标注
    (setq index 0)
    (repeat (sslength selection-set)
        (setq entity (ssname selection-set index))
        (add_radial_dimension entity)
        (setq index (1+ index))
    )
    
    (princ "\n批量径向标注完成")
)

;;; 批量角度标注
(defun batch_angular_dimension (/ selection-set index entity)
    ;;; 选择直线对象（用于角度标注）
    (setq selection-set (ssget '((0 . "LINE"))))
    (if (not selection-set)
        (progn
            (princ "\n未选择直线对象")
            (exit)
        )
    )
    
    ;;; 批量添加角度标注
    (setq index 0)
    (repeat (sslength selection-set)
        (setq entity (ssname selection-set index))
        (add_angular_dimension entity selection-set index)
        (setq index (1+ index))
    )
    
    (princ "\n批量角度标注完成")
)

;;; 添加角度标注
(defun add_angular_dimension (entity selection-set index / entity-data start-point end-point next-entity next-entity-data next-start-point angle-point)
    (setq entity-data (entget entity))
    (setq start-point (cdr (assoc 10 entity-data)))
    (setq end-point (cdr (assoc 11 entity-data)))
    
    ;;; 寻找相邻的直线
    (if (< (1+ index) (sslength selection-set))
        (progn
            (setq next-entity (ssname selection-set (1+ index)))
            (setq next-entity-data (entget next-entity))
            (setq next-start-point (cdr (assoc 10 next-entity-data)))
            
            ;;; 检查是否相邻
            (if (or (equal start-point next-start-point 0.001)
                    (equal end-point next-start-point 0.001))
                (progn
                    ;;; 计算角度标注位置
                    (setq angle-point (calculate_angle_position start-point end-point next-start-point))
                    
                    ;;; 添加角度标注
                    (command "dimangular" start-point end-point next-start-point angle-point)
                )
            )
        )
    )
)

;;; 计算角度标注位置
(defun calculate_angle_position (point1 point2 point3 / mid-point offset)
    (setq mid-point (list 
                      (/ (+ (car point1) (car point2) (car point3)) 3)
                      (/ (+ (cadr point1) (cadr point2) (cadr point3)) 3)
                      0
                    ))
    
    (setq offset 10) ; 角度标注偏移距离
    (list 
      (+ (car mid-point) offset)
      (+ (cadr mid-point) offset)
      0
    )
)

;;; 批量对齐标注
(defun batch_aligned_dimension (/ selection-set index entity)
    ;;; 选择直线对象
    (setq selection-set (ssget '((0 . "LINE"))))
    (if (not selection-set)
        (progn
            (princ "\n未选择直线对象")
            (exit)
        )
    )
    
    ;;; 批量添加对齐标注
    (setq index 0)
    (repeat (sslength selection-set)
        (setq entity (ssname selection-set index))
        (add_aligned_dimension entity)
        (setq index (1+ index))
    )
    
    (princ "\n批量对齐标注完成")
)

;;; 添加对齐标注
(defun add_aligned_dimension (entity / entity-data start-point end-point dimension-point)
    (setq entity-data (entget entity))
    (setq start-point (cdr (assoc 10 entity-data)))
    (setq end-point (cdr (assoc 11 entity-data)))
    
    ;;; 计算标注位置
    (setq dimension-point (calculate_dimension_position start-point end-point))
    
    ;;; 添加对齐标注
    (command "dimaligned" start-point end-point dimension-point)
)

;;; 清理标注
(defun clean_dimensions (/ selection-set index entity dimension-data)
    ;;; 选择所有标注对象
    (setq selection-set (ssget '((0 . "DIMENSION"))))
    (if (not selection-set)
        (progn
            (princ "\n未选择标注对象")
            (exit)
        )
    )
    
    ;;; 清理重复和无效标注
    (setq index 0)
    (repeat (sslength selection-set)
        (setq entity (ssname selection-set index))
        (setq dimension-data (entget entity))
        
        ;;; 检查标注是否有效
        (if (is_valid_dimension dimension-data)
            (progn
                ;;; 标准化标注样式
                (standardize_dimension_style entity)
            )
            (progn
                ;;; 删除无效标注
                (entdel entity)
                (princ (strcat "\n删除无效标注: " (cdr (assoc 5 dimension-data))))
            )
        )
        
        (setq index (1+ index))
    )
    
    (princ "\n标注清理完成")
)

;;; 检查标注是否有效
(defun is_valid_dimension (dimension-data / valid)
    (setq valid T)
    
    ;;; 检查必要的组码
    (if (not (assoc 10 dimension-data))
        (setq valid nil)
    )
    
    (if (not (assoc 13 dimension-data))
        (setq valid nil)
    )
    
    (if (not (assoc 14 dimension-data))
        (setq valid nil)
    )
    
    valid
)

;;; 标准化标注样式
(defun standardize_dimension_style (entity / dimension-data)
    (setq dimension-data (entget entity))
    
    ;;; 设置标注样式属性
    (setq dimension-data (subst (cons 3 "Standard") (assoc 3 dimension-data) dimension-data))
    (setq dimension-data (subst (cons 40 2.5) (assoc 40 dimension-data) dimension-data)) ; 文字高度
    (setq dimension-data (subst (cons 41 1.0) (assoc 41 dimension-data) dimension-data)) ; 箭头大小
    (setq dimension-data (subst (cons 42 0.625) (assoc 42 dimension-data) dimension-data)) ; 箭头大小
    
    ;;; 更新实体
    (entmod dimension-data)
)

;;; 使用示例：
;;; (c:SmartDimensionTool) - 启动智能标注工具
;;; (auto_dimension_selection) - 自动标注选择的对象
;;; (batch_linear_dimension) - 批量线性标注
;;; (batch_radial_dimension) - 批量径向标注
;;; (batch_angular_dimension) - 批量角度标注
;;; (batch_aligned_dimension) - 批量对齐标注
;;; (clean_dimensions) - 清理标注
```

[由于篇幅限制，VBA和C#版本的智能尺寸标注工具代码以及后续章节的内容将在下一部分继续提供。当前内容已经包含了完整的第一章和第二章的部分内容，展示了CURSOR+AUTOCAD自动化的基础概念和实际应用案例。]
