export interface CursorAPI {
    window: {
        showInformationMessage(message: string): void;
        showErrorMessage(message: string): void;
        showWarningMessage(message: string): void;
        createStatusBarItem(): any;
        createWebviewPanel(): any;
    };
    commands: {
        registerCommand(command: string, callback: Function): void;
        executeCommand(command: string, ...args: any[]): Promise<any>;
    };
    workspace: {
        getConfiguration(): any;
        onDidChangeConfiguration(callback: Function): void;
        getWorkspaceFolder(): any;
    };
    languages: {
        registerCompletionItemProvider(): any;
        registerHoverProvider(): any;
    };
}
export declare class CursorIntegration {
    private cursor;
    private logger;
    private registeredCommands;
    constructor(cursorApi: CursorAPI);
    initialize(): Promise<void>;
    private registerCommands;
    private registerUIComponents;
    private setupEventListeners;
    private registerWebviewPanel;
    private getWebviewContent;
    private refreshEngines;
    private executeProcess;
    private showProcesses;
    private openSettings;
    private debugProcess;
    private handleConfigurationChange;
    dispose(): Promise<void>;
}
export declare function createMockCursorAPI(): CursorAPI;
//# sourceMappingURL=cursor-integration.d.ts.map