"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PluginLifecycleManager = void 0;
const config_1 = require("../../config");
const logger_1 = require("../../utils/logger");
const events_1 = __importDefault(require("events"));
class PluginLifecycleManager extends events_1.default {
    constructor() {
        super();
        this.logger = (0, logger_1.createLogger)('PluginLifecycleManager');
        this.activationPromise = null;
        this.config = (0, config_1.loadConfig)();
        this.state = {
            status: 'inactive'
        };
    }
    async activate(context) {
        if (this.activationPromise) {
            return this.activationPromise;
        }
        this.activationPromise = this.performActivation(context);
        return this.activationPromise;
    }
    async performActivation(context) {
        try {
            this.updateState('activating');
            this.emitEvent('activating');
            this.logger.info('Starting plugin activation...');
            if (!this.config.enabled) {
                throw new Error('Plugin is disabled in configuration');
            }
            await this.validateDependencies();
            await this.initializeServices(context);
            await this.registerHooks();
            this.updateState('active');
            this.state.activationTime = new Date();
            this.emitEvent('activated');
            this.logger.info('Plugin activated successfully');
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : String(error);
            this.updateState('error', errorMessage);
            this.emitEvent('error', errorMessage);
            this.logger.error('Plugin activation failed:', error);
            throw error;
        }
        finally {
            this.activationPromise = null;
        }
    }
    async deactivate() {
        try {
            if (this.state.status !== 'active') {
                this.logger.warn('Plugin is not active, skipping deactivation');
                return;
            }
            this.updateState('deactivating');
            this.emitEvent('deactivating');
            this.logger.info('Starting plugin deactivation...');
            await this.unregisterHooks();
            await this.cleanupServices();
            this.updateState('inactive');
            delete this.state.activationTime;
            this.emitEvent('deactivated');
            this.logger.info('Plugin deactivated successfully');
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : String(error);
            this.updateState('error', errorMessage);
            this.emitEvent('error', errorMessage);
            this.logger.error('Plugin deactivation failed:', error);
            throw error;
        }
    }
    async restart(context) {
        this.logger.info('Restarting plugin...');
        try {
            await this.deactivate();
            await this.activate(context);
            this.logger.info('Plugin restarted successfully');
        }
        catch (error) {
            this.logger.error('Plugin restart failed:', error);
            throw error;
        }
    }
    getState() {
        return { ...this.state };
    }
    getConfig() {
        return { ...this.config };
    }
    async updateConfig(newConfig) {
        try {
            this.config = { ...this.config, ...newConfig };
            await (0, config_1.saveConfig)(this.config);
            this.logger.info('Configuration updated');
            if (this.state.status === 'active' && !this.config.enabled) {
                await this.deactivate();
            }
            else if (this.state.status === 'inactive' && this.config.enabled) {
                await this.activate();
            }
        }
        catch (error) {
            this.logger.error('Failed to update configuration:', error);
            throw error;
        }
    }
    async validateDependencies() {
        const requiredDeps = ['axios', 'ws', 'sqlite3', 'winston'];
        for (const dep of requiredDeps) {
            try {
                require(dep);
            }
            catch (error) {
                throw new Error(`Missing required dependency: ${dep}`);
            }
        }
        this.logger.debug('All dependencies validated');
    }
    async initializeServices(context) {
        this.logger.debug('Initializing services...');
        if (context?.workspace) {
            this.logger.info(`Initializing for workspace: ${context.workspace}`);
        }
        await this.initializeDirectories();
        await this.initializeDatabase();
        this.logger.debug('Services initialized');
    }
    async initializeDirectories() {
        const fs = require('fs');
        const path = require('path');
        const dirs = [
            path.join(process.cwd(), 'logs'),
            path.join(process.cwd(), 'data'),
            path.join(process.cwd(), 'temp')
        ];
        for (const dir of dirs) {
            if (!fs.existsSync(dir)) {
                fs.mkdirSync(dir, { recursive: true });
            }
        }
    }
    async initializeDatabase() {
        // Initialize SQLite database for plugin data
        const sqlite3 = require('sqlite3').verbose();
        const path = require('path');
        return new Promise((resolve, reject) => {
            const dbPath = path.join(process.cwd(), 'data', 'plugin.db');
            const db = new sqlite3.Database(dbPath, (err) => {
                if (err) {
                    reject(err);
                }
                else {
                    db.close();
                    resolve();
                }
            });
        });
    }
    async registerHooks() {
        this.logger.debug('Registering hooks...');
        // Register event listeners, context menus, etc.
        // This will be implemented based on Cursor's extension API
    }
    async unregisterHooks() {
        this.logger.debug('Unregistering hooks...');
        // Clean up registered hooks
    }
    async cleanupServices() {
        this.logger.debug('Cleaning up services...');
        // Clean up any running services, connections, etc.
    }
    updateState(status, error) {
        this.state = {
            ...this.state,
            status,
            lastError: error
        };
    }
    emitEvent(type, error) {
        const event = {
            type,
            timestamp: new Date(),
            error
        };
        this.emit('activation-change', event);
    }
    isActive() {
        return this.state.status === 'active';
    }
    isActivating() {
        return this.state.status === 'activating';
    }
    getActivationTime() {
        return this.state.activationTime;
    }
    getLastError() {
        return this.state.lastError;
    }
}
exports.PluginLifecycleManager = PluginLifecycleManager;
//# sourceMappingURL=lifecycle-manager.js.map