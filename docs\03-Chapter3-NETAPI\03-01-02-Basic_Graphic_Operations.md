# 基础图形操作

## 3.2.1 数据库和事务处理

**数据库基本操作：**
```csharp
[CommandMethod("DatabaseOperations")]
public void DatabaseOperations()
{
    Document doc = Application.DocumentManager.MdiActiveDocument;
    Database db = doc.Database;
    Editor ed = doc.Editor;
    
    try
    {
        // 开始事务
        using (Transaction trans = db.TransactionManager.StartTransaction())
        {
            // 获取块表
            BlockTable bt = trans.GetObject(db.BlockTableId, OpenMode.ForRead) as BlockTable;
            
            // 获取模型空间
            BlockTableRecord btr = trans.GetObject(bt[BlockTableRecord.ModelSpace], OpenMode.ForWrite) as BlockTableRecord;
            
            // 创建图形对象
            Line line = new Line(new Point3d(0, 0, 0), new Point3d(100, 100, 0));
            line.Color = Color.FromColorIndex(ColorMethod.ByColor, 1); // 红色
            
            // 添加到模型空间
            btr.AppendEntity(line);
            trans.AddNewlyCreatedDBObject(line, true);
            
            // 提交事务
            trans.Commit();
        }
        
        ed.WriteMessage("\n图形创建成功！");
    }
    catch (System.Exception ex)
    {
        ed.WriteMessage("\n错误: " + ex.Message);
    }
}
```

**事务处理最佳实践：**
```csharp
[CommandMethod("TransactionExample")]
public void TransactionExample()
{
    Document doc = Application.DocumentManager.MdiActiveDocument;
    Database db = doc.Database;
    Editor ed = doc.Editor;
    
    // 使用using语句确保事务正确释放
    using (Transaction trans = db.TransactionManager.StartTransaction())
    {
        try
        {
            // 获取块表
            BlockTable bt = trans.GetObject(db.BlockTableId, OpenMode.ForRead) as BlockTable;
            BlockTableRecord btr = trans.GetObject(bt[BlockTableRecord.ModelSpace], OpenMode.ForWrite) as BlockTableRecord;
            
            // 批量创建对象
            for (int i = 0; i < 10; i++)
            {
                Circle circle = new Circle(new Point3d(i * 20, 0, 0), Vector3d.ZAxis, 5);
                circle.Color = Color.FromColorIndex(ColorMethod.ByColor, (short)(i % 256));
                btr.AppendEntity(circle);
                trans.AddNewlyCreatedDBObject(circle, true);
            }
            
            // 提交事务
            trans.Commit();
            ed.WriteMessage("\n成功创建10个圆！");
        }
        catch (System.Exception ex)
        {
            // 事务会自动回滚
            ed.WriteMessage("\n操作失败: " + ex.Message);
        }
    }
}
```

## 3.2.2 基本图形实体创建

**直线和曲线：**
```csharp
[CommandMethod("CreateBasicEntities")]
public void CreateBasicEntities()
{
    Document doc = Application.DocumentManager.MdiActiveDocument;
    Database db = doc.Database;
    
    using (Transaction trans = db.TransactionManager.StartTransaction())
    {
        BlockTable bt = trans.GetObject(db.BlockTableId, OpenMode.ForRead) as BlockTable;
        BlockTableRecord btr = trans.GetObject(bt[BlockTableRecord.ModelSpace], OpenMode.ForWrite) as BlockTableRecord;
        
        // 创建直线
        Line line = new Line(new Point3d(0, 0, 0), new Point3d(100, 50, 0));
        line.Color = Color.FromColorIndex(ColorMethod.ByColor, 1);
        btr.AppendEntity(line);
        trans.AddNewlyCreatedDBObject(line, true);
        
        // 创建圆
        Circle circle = new Circle(new Point3d(150, 50, 0), Vector3d.ZAxis, 30);
        circle.Color = Color.FromColorIndex(ColorMethod.ByColor, 2);
        btr.AppendEntity(circle);
        trans.AddNewlyCreatedDBObject(circle, true);
        
        // 创建圆弧
        Arc arc = new Arc(new Point3d(250, 50, 0), 25, 0, Math.PI);
        arc.Color = Color.FromColorIndex(ColorMethod.ByColor, 3);
        btr.AppendEntity(arc);
        trans.AddNewlyCreatedDBObject(arc, true);
        
        // 创建椭圆
        Ellipse ellipse = new Ellipse(new Point3d(350, 50, 0), Vector3d.ZAxis, 
                                    new Vector3d(40, 0, 0), 0.5, 0, 2 * Math.PI);
        ellipse.Color = Color.FromColorIndex(ColorMethod.ByColor, 4);
        btr.AppendEntity(ellipse);
        trans.AddNewlyCreatedDBObject(ellipse, true);
        
        // 创建多段线
        Polyline polyline = new Polyline();
        polyline.AddVertexAt(0, new Point2d(0, 100), 0, 0, 0);
        polyline.AddVertexAt(1, new Point2d(50, 150), 0, 0, 0);
        polyline.AddVertexAt(2, new Point2d(100, 100), 0, 0, 0);
        polyline.AddVertexAt(3, new Point2d(100, 50), 0, 0, 0);
        polyline.Closed = true;
        polyline.Color = Color.FromColorIndex(ColorMethod.ByColor, 5);
        btr.AppendEntity(polyline);
        trans.AddNewlyCreatedDBObject(polyline, true);
        
        // 创建样条曲线
        Point3dCollection points = new Point3dCollection();
        points.Add(new Point3d(150, 100, 0));
        points.Add(new Point3d(200, 150, 0));
        points.Add(new Point3d(250, 100, 0));
        points.Add(new Point3d(300, 150, 0));
        
        Spline spline = new Spline(points, 1, 0);
        spline.Color = Color.FromColorIndex(ColorMethod.ByColor, 6);
        btr.AppendEntity(spline);
        trans.AddNewlyCreatedDBObject(spline, true);
        
        trans.Commit();
    }
}
```

**文字和标注：**
```csharp
[CommandMethod("CreateTextAndDimensions")]
public void CreateTextAndDimensions()
{
    Document doc = Application.DocumentManager.MdiActiveDocument;
    Database db = doc.Database;
    
    using (Transaction trans = db.TransactionManager.StartTransaction())
    {
        BlockTable bt = trans.GetObject(db.BlockTableId, OpenMode.ForRead) as BlockTable;
        BlockTableRecord btr = trans.GetObject(bt[BlockTableRecord.ModelSpace], OpenMode.ForWrite) as BlockTableRecord;
        
        // 创建单行文字
        DBText text = new DBText();
        text.Position = new Point3d(0, 0, 0);
        text.TextString = "AutoCAD .NET API";
        text.Height = 10;
        text.Color = Color.FromColorIndex(ColorMethod.ByColor, 1);
        btr.AppendEntity(text);
        trans.AddNewlyCreatedDBObject(text, true);
        
        // 创建多行文字
        MText mtext = new MText();
        mtext.Location = new Point3d(0, 50, 0);
        mtext.Width = 200;
        mtext.Height = 10;
        mtext.Color = Color.FromColorIndex(ColorMethod.ByColor, 2);
        mtext.Contents = "多行文字示例\n支持多行文本和格式化";
        btr.AppendEntity(mtext);
        trans.AddNewlyCreatedDBObject(mtext, true);
        
        // 创建线性标注
        Point3d startPt = new Point3d(0, 100, 0);
        Point3d endPt = new Point3d(100, 100, 0);
        Point3d textPt = new Point3d(50, 120, 0);
        
        AlignedDimension dim = new AlignedDimension(startPt, endPt, textPt, "", db.Dimstyle);
        dim.Color = Color.FromColorIndex(ColorMethod.ByColor, 3);
        btr.AppendEntity(dim);
        trans.AddNewlyCreatedDBObject(dim, true);
        
        // 创建半径标注
        Circle circle = new Circle(new Point3d(150, 100, 0), Vector3d.ZAxis, 25);
        btr.AppendEntity(circle);
        trans.AddNewlyCreatedDBObject(circle, true);
        
        RadialDimension radiusDim = new RadialDimension(circle.Center, 
                                                        new Point3d(175, 100, 0), 
                                                        25, "", db.Dimstyle);
        radiusDim.Color = Color.FromColorIndex(ColorMethod.ByColor, 4);
        btr.AppendEntity(radiusDim);
        trans.AddNewlyCreatedDBObject(radiusDim, true);
        
        // 创建角度标注
        Line line1 = new Line(new Point3d(200, 50, 0), new Point3d(250, 100, 0));
        Line line2 = new Line(new Point3d(200, 50, 0), new Point3d(250, 0, 0));
        btr.AppendEntity(line1);
        trans.AddNewlyCreatedDBObject(line1, true);
        btr.AppendEntity(line2);
        trans.AddNewlyCreatedDBObject(line2, true);
        
        Point3d anglePt = new Point3d(230, 70, 0);
        AngularDimension angleDim = new AngularDimension(new Point3d(200, 50, 0), 
                                                        line1.EndPoint, 
                                                        line2.EndPoint, 
                                                        anglePt, "", db.Dimstyle);
        angleDim.Color = Color.FromColorIndex(ColorMethod.ByColor, 5);
        btr.AppendEntity(angleDim);
        trans.AddNewlyCreatedDBObject(angleDim, true);
        
        trans.Commit();
    }
}
```

## 3.2.3 图层和样式管理

**图层操作：**
```csharp
[CommandMethod("LayerManagement")]
public void LayerManagement()
{
    Document doc = Application.DocumentManager.MdiActiveDocument;
    Database db = doc.Database;
    
    using (Transaction trans = db.TransactionManager.StartTransaction())
    {
        LayerTable lt = trans.GetObject(db.LayerTableId, OpenMode.ForWrite) as LayerTable;
        
        // 创建新图层
        if (!lt.Has("WALLS"))
        {
            LayerTableRecord wallLayer = new LayerTableRecord();
            wallLayer.Name = "WALLS";
            wallLayer.Color = Color.FromColorIndex(ColorMethod.ByColor, 1); // 红色
            wallLayer.LineWeight = LineWeight.LineWeight050;
            
            lt.Add(wallLayer);
            trans.AddNewlyCreatedDBObject(wallLayer, true);
        }
        
        // 创建门窗图层
        if (!lt.Has("DOORS"))
        {
            LayerTableRecord doorLayer = new LayerTableRecord();
            doorLayer.Name = "DOORS";
            doorLayer.Color = Color.FromColorIndex(ColorMethod.ByColor, 2); // 黄色
            doorLayer.LineWeight = LineWeight.LineWeight025;
            
            lt.Add(doorLayer);
            trans.AddNewlyCreatedDBObject(doorLayer, true);
        }
        
        if (!lt.Has("WINDOWS"))
        {
            LayerTableRecord windowLayer = new LayerTableRecord();
            windowLayer.Name = "WINDOWS";
            windowLayer.Color = Color.FromColorIndex(ColorMethod.ByColor, 3); // 绿色
            windowLayer.LineWeight = LineWeight.LineWeight025;
            
            lt.Add(windowLayer);
            trans.AddNewlyCreatedDBObject(windowLayer, true);
        }
        
        // 设置当前图层
        db.Clayer = lt["WALLS"];
        
        trans.Commit();
        
        Editor ed = doc.Editor;
        ed.WriteMessage("\n图层创建完成！");
    }
}
```

**线型和文字样式：**
```csharp
[CommandMethod("StyleManagement")]
public void StyleManagement()
{
    Document doc = Application.DocumentManager.MdiActiveDocument;
    Database db = doc.Database;
    
    using (Transaction trans = db.TransactionManager.StartTransaction())
    {
        // 线型管理
        LinetypeTable ltt = trans.GetObject(db.LinetypeTableId, OpenMode.ForWrite) as LinetypeTable;
        
        // 加载线型
        if (!ltt.Has("CENTER"))
        {
            db.LoadLineTypeFile("CENTER", "acad.lin");
        }
        
        if (!ltt.Has("DASHED"))
        {
            db.LoadLineTypeFile("DASHED", "acad.lin");
        }
        
        // 文字样式管理
        TextStyleTable tst = trans.GetObject(db.TextStyleTableId, OpenMode.ForWrite) as TextStyleTable;
        
        if (!tst.Has("MyTextStyle"))
        {
            TextStyleTableRecord textStyle = new TextStyleTableRecord();
            textStyle.Name = "MyTextStyle";
            textStyle.FileName = "simplex.shx";
            textStyle.TextSize = 5.0;
            
            tst.Add(textStyle);
            trans.AddNewlyCreatedDBObject(textStyle, true);
        }
        
        // 标注样式管理
        DimStyleTable dst = trans.GetObject(db.DimStyleTableId, OpenMode.ForWrite) as DimStyleTable;
        
        if (!dst.Has("MyDimStyle"))
        {
            DimStyleTableRecord dimStyle = new DimStyleTableRecord();
            dimStyle.Name = "MyDimStyle";
            dimStyle.DimScale = 1.0;
            dimStyle.Dimtxt = 3.5;
            dimStyle.Dimasz = 2.5;
            
            dst.Add(dimStyle);
            trans.AddNewlyCreatedDBObject(dimStyle, true);
            
            // 设置当前标注样式
            db.Dimstyle = dst["MyDimStyle"];
        }
        
        trans.Commit();
        
        Editor ed = doc.Editor;
        ed.WriteMessage("\n样式创建完成！");
    }
}
```

## 3.2.4 坐标系和变换

**坐标系操作：**
```csharp
[CommandMethod("CoordinateSystem")]
public void CoordinateSystem()
{
    Document doc = Application.DocumentManager.MdiActiveDocument;
    Database db = doc.Database;
    Editor ed = doc.Editor;
    
    try
    {
        // 获取当前UCS
        Matrix3d ucsMatrix = ed.CurrentUserCoordinateSystem;
        
        // 创建点
        Point3d pt1 = new Point3d(0, 0, 0);
        Point3d pt2 = new Point3d(100, 50, 0);
        
        // 转换到WCS
        Point3d wcsPt1 = pt1.TransformBy(ucsMatrix);
        Point3d wcsPt2 = pt2.TransformBy(ucsMatrix);
        
        ed.WriteMessage("\nUCS点1: " + pt1.ToString());
        ed.WriteMessage("\nWCS点1: " + wcsPt1.ToString());
        
        // 创建坐标系
        using (Transaction trans = db.TransactionManager.StartTransaction())
        {
            BlockTable bt = trans.GetObject(db.BlockTableId, OpenMode.ForRead) as BlockTable;
            BlockTableRecord btr = trans.GetObject(bt[BlockTableRecord.ModelSpace], OpenMode.ForWrite) as BlockTableRecord;
            
            // 创建X轴
            Line xAxis = new Line(new Point3d(0, 0, 0), new Point3d(50, 0, 0));
            xAxis.Color = Color.FromColorIndex(ColorMethod.ByColor, 1); // 红色
            btr.AppendEntity(xAxis);
            trans.AddNewlyCreatedDBObject(xAxis, true);
            
            // 创建Y轴
            Line yAxis = new Line(new Point3d(0, 0, 0), new Point3d(0, 50, 0));
            yAxis.Color = Color.FromColorIndex(ColorMethod.ByColor, 3); // 绿色
            btr.AppendEntity(yAxis);
            trans.AddNewlyCreatedDBObject(yAxis, true);
            
            // 创建Z轴
            Line zAxis = new Line(new Point3d(0, 0, 0), new Point3d(0, 0, 50));
            zAxis.Color = Color.FromColorIndex(ColorMethod.ByColor, 5); // 蓝色
            btr.AppendEntity(zAxis);
            trans.AddNewlyCreatedDBObject(zAxis, true);
            
            // 添加轴标签
            DBText xLabel = new DBText();
            xLabel.Position = new Point3d(55, 0, 0);
            xLabel.TextString = "X";
            xLabel.Color = Color.FromColorIndex(ColorMethod.ByColor, 1);
            btr.AppendEntity(xLabel);
            trans.AddNewlyCreatedDBObject(xLabel, true);
            
            DBText yLabel = new DBText();
            yLabel.Position = new Point3d(0, 55, 0);
            yLabel.TextString = "Y";
            yLabel.Color = Color.FromColorIndex(ColorMethod.ByColor, 3);
            btr.AppendEntity(yLabel);
            trans.AddNewlyCreatedDBObject(yLabel, true);
            
            DBText zLabel = new DBText();
            zLabel.Position = new Point3d(0, 0, 55);
            zLabel.TextString = "Z";
            zLabel.Color = Color.FromColorIndex(ColorMethod.ByColor, 5);
            btr.AppendEntity(zLabel);
            trans.AddNewlyCreatedDBObject(zLabel, true);
            
            trans.Commit();
        }
        
        ed.WriteMessage("\n坐标系创建完成！");
    }
    catch (System.Exception ex)
    {
        ed.WriteMessage("\n错误: " + ex.Message);
    }
}
```

**几何变换：**
```csharp
[CommandMethod("GeometricTransformations")]
public void GeometricTransformations()
{
    Document doc = Application.DocumentManager.MdiActiveDocument;
    Database db = doc.Database;
    
    using (Transaction trans = db.TransactionManager.StartTransaction())
    {
        BlockTable bt = trans.GetObject(db.BlockTableId, OpenMode.ForRead) as BlockTable;
        BlockTableRecord btr = trans.GetObject(bt[BlockTableRecord.ModelSpace], OpenMode.ForWrite) as BlockTableRecord;
        
        // 创建原始矩形
        Polyline originalRect = new Polyline();
        originalRect.AddVertexAt(0, new Point2d(0, 0), 0, 0, 0);
        originalRect.AddVertexAt(1, new Point2d(50, 0), 0, 0, 0);
        originalRect.AddVertexAt(2, new Point2d(50, 30), 0, 0, 0);
        originalRect.AddVertexAt(3, new Point2d(0, 30), 0, 0, 0);
        originalRect.Closed = true;
        originalRect.Color = Color.FromColorIndex(ColorMethod.ByColor, 1);
        btr.AppendEntity(originalRect);
        trans.AddNewlyCreatedDBObject(originalRect, true);
        
        // 平移变换
        Matrix3d translationMatrix = Matrix3d.Displacement(new Vector3d(100, 0, 0));
        Polyline translatedRect = originalRect.Clone() as Polyline;
        translatedRect.TransformBy(translationMatrix);
        translatedRect.Color = Color.FromColorIndex(ColorMethod.ByColor, 2);
        btr.AppendEntity(translatedRect);
        trans.AddNewlyCreatedDBObject(translatedRect, true);
        
        // 旋转变换
        Matrix3d rotationMatrix = Matrix3d.Rotation(Math.PI / 4, Vector3d.ZAxis, new Point3d(200, 15, 0));
        Polyline rotatedRect = originalRect.Clone() as Polyline;
        rotatedRect.TransformBy(rotationMatrix);
        rotatedRect.Color = Color.FromColorIndex(ColorMethod.ByColor, 3);
        btr.AppendEntity(rotatedRect);
        trans.AddNewlyCreatedDBObject(rotatedRect, true);
        
        // 缩放变换
        Matrix3d scalingMatrix = Matrix3d.Scaling(1.5, new Point3d(300, 15, 0));
        Polyline scaledRect = originalRect.Clone() as Polyline;
        scaledRect.TransformBy(scalingMatrix);
        scaledRect.Color = Color.FromColorIndex(ColorMethod.ByColor, 4);
        btr.AppendEntity(scaledRect);
        trans.AddNewlyCreatedDBObject(scaledRect, true);
        
        // 镜像变换
        Line mirrorLine = new Line(new Point3d(400, 0, 0), new Point3d(400, 60, 0));
        mirrorLine.Color = Color.FromColorIndex(ColorMethod.ByColor, 6);
        btr.AppendEntity(mirrorLine);
        trans.AddNewlyCreatedDBObject(mirrorLine, true);
        
        Matrix3d mirrorMatrix = Matrix3d.Mirroring(new Line3d(mirrorLine.StartPoint, mirrorLine.EndPoint));
        Polyline mirroredRect = originalRect.Clone() as Polyline;
        mirroredRect.TransformBy(mirrorMatrix);
        mirroredRect.Color = Color.FromColorIndex(ColorMethod.ByColor, 5);
        btr.AppendEntity(mirroredRect);
        trans.AddNewlyCreatedDBObject(mirroredRect, true);
        
        trans.Commit();
    }
}
```

---

**CURSOR提示词模板：**
```
请生成AutoCAD .NET代码，实现[具体功能]，要求：
1. 使用事务处理
2. 遵循AutoCAD .NET API最佳实践
3. 包含错误处理
4. 提供完整的代码示例
```