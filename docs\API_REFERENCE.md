# API 参考文档

## 概述

影刀RPA引擎提供完整的RESTful API，支持流程管理、执行控制、变量管理等功能。

## 基础信息

- **Base URL**: `http://localhost:8080`
- **协议**: HTTP/1.1
- **数据格式**: JSON
- **编码**: UTF-8
- **CORS**: 已启用

## 认证

当前版本无需认证，生产环境建议添加API Key认证。

```http
Authorization: Bearer your-api-key
```

## 响应格式

### 成功响应
```json
{
  "success": true,
  "data": {},
  "message": "操作成功",
  "timestamp": "2025-07-31T00:00:00.000Z"
}
```

### 错误响应
```json
{
  "success": false,
  "error": {
    "code": "RESOURCE_NOT_FOUND",
    "message": "资源未找到",
    "details": {}
  },
  "timestamp": "2025-07-31T00:00:00.000Z"
}
```

## API 端点

### 1. 基础服务

#### GET /health
检查引擎服务健康状态。

**响应示例**:
```json
{
  "status": "healthy",
  "timestamp": "2025-07-31T00:00:00.000Z",
  "uptime": 1234.567
}
```

**状态码**:
- 200: 服务正常
- 503: 服务异常

---

#### GET /info
获取引擎详细信息。

**响应示例**:
```json
{
  "name": "Yingdao RPA Engine",
  "version": "2.0.0",
  "capabilities": [
    "process_execution",
    "debug_mode",
    "variable_management",
    "logging",
    "web_automation",
    "data_extraction",
    "file_operations",
    "api_integration"
  ],
  "status": "running"
}
```

---

### 2. 流程管理

#### GET /processes
获取所有流程列表。

**查询参数**:
- `status` (可选): 过滤状态 (idle, running, completed, error)
- `limit` (可选): 返回数量限制 (默认: 50)
- `offset` (可选): 偏移量 (默认: 0)

**响应示例**:
```json
{
  "processes": [
    {
      "id": "process_1234567890_abc123",
      "name": "Web数据提取",
      "description": "从网页提取数据",
      "status": "idle",
      "created": "2025-07-31T00:00:00.000Z",
      "modified": "2025-07-31T00:00:00.000Z",
      "variables": {
        "url": "https://example.com",
        "selectors": ["title", "content"]
      }
    }
  ]
}
```

---

#### POST /processes
创建新流程。

**请求体**:
```json
{
  "name": "流程名称 (必需)",
  "description": "流程描述 (可选)",
  "variables": {
    "variable_name": "variable_value"
  }
}
```

**响应示例**:
```json
{
  "id": "process_1234567890_abc123",
  "name": "新流程",
  "description": "流程描述",
  "status": "idle",
  "created": "2025-07-31T00:00:00.000Z",
  "modified": "2025-07-31T00:00:00.000Z",
  "variables": {}
}
```

**状态码**:
- 201: 创建成功
- 400: 请求参数错误
- 409: 流程名称冲突

---

#### GET /processes/{id}
获取特定流程详情。

**路径参数**:
- `id`: 流程ID

**响应示例**:
```json
{
  "id": "process_1234567890_abc123",
  "name": "流程名称",
  "description": "流程描述",
  "status": "idle",
  "created": "2025-07-31T00:00:00.000Z",
  "modified": "2025-07-31T00:00:00.000Z",
  "variables": {
    "url": "https://example.com",
    "outputFormat": "json"
  }
}
```

**状态码**:
- 200: 成功
- 404: 流程不存在

---

#### PUT /processes/{id}
更新流程信息。

**路径参数**:
- `id`: 流程ID

**请求体**:
```json
{
  "name": "新的流程名称 (可选)",
  "description": "新的描述 (可选)",
  "variables": {
    "new_variable": "new_value"
  }
}
```

**响应示例**:
```json
{
  "id": "process_1234567890_abc123",
  "name": "更新的流程名称",
  "description": "更新的描述",
  "status": "idle",
  "created": "2025-07-31T00:00:00.000Z",
  "modified": "2025-07-31T00:00:00.000Z",
  "variables": {
    "url": "https://example.com",
    "new_variable": "new_value"
  }
}
```

**状态码**:
- 200: 更新成功
- 400: 请求参数错误
- 404: 流程不存在

---

#### DELETE /processes/{id}
删除流程。

**路径参数**:
- `id`: 流程ID

**状态码**:
- 204: 删除成功
- 404: 流程不存在
- 409: 流程正在执行，无法删除

---

### 3. 执行控制

#### POST /processes/{id}/execute
启动流程执行。

**路径参数**:
- `id`: 流程ID

**请求体**:
```json
{
  "parameters": {
    "input_data": "参数值"
  },
  "debugMode": false,
  "timeout": 300000
}
```

**响应示例**:
```json
{
  "executionId": "exec_1234567890_def456",
  "processId": "process_1234567890_abc123",
  "status": "started",
  "message": "Process execution started"
}
```

**状态码**:
- 200: 启动成功
- 400: 请求参数错误
- 404: 流程不存在
- 409: 流程正在执行

---

#### GET /executions/{id}
获取执行状态。

**路径参数**:
- `id`: 执行ID

**响应示例**:
```json
{
  "id": "exec_1234567890_def456",
  "processId": "process_1234567890_abc123",
  "status": "running",
  "startTime": "2025-07-31T00:00:00.000Z",
  "parameters": {
    "input_data": "测试数据"
  },
  "debugMode": true,
  "progress": {
    "currentStep": 2,
    "totalSteps": 5,
    "percentage": 40,
    "currentActivity": "Processing data..."
  },
  "logs": [
    {
      "id": "log_1234567890_ghi789",
      "timestamp": "2025-07-31T00:00:01.000Z",
      "level": "info",
      "message": "Step 1: Initialize"
    }
  ]
}
```

**执行状态**:
- `running`: 正在执行
- `completed`: 执行完成
- `error`: 执行错误
- `stopped`: 手动停止

**状态码**:
- 200: 成功
- 404: 执行不存在

---

#### POST /executions/{id}/stop
停止执行。

**路径参数**:
- `id`: 执行ID

**响应示例**:
```json
{
  "message": "Execution stopped"
}
```

**状态码**:
- 200: 停止成功
- 404: 执行不存在
- 409: 执行已经停止

---

### 4. 变量管理

#### GET /processes/{id}/variables
获取流程变量。

**路径参数**:
- `id`: 流程ID

**响应示例**:
```json
{
  "url": "https://example.com",
  "outputFormat": "json",
  "lastUpdated": "2025-07-31T00:00:00.000Z"
}
```

**状态码**:
- 200: 成功
- 404: 流程不存在

---

#### PUT /processes/{id}/variables
更新流程变量。

**路径参数**:
- `id`: 流程ID

**请求体**:
```json
{
  "variables": {
    "new_variable": "new_value",
    "existing_variable": "updated_value"
  }
}
```

**响应示例**:
```json
{
  "url": "https://example.com",
  "outputFormat": "json",
  "new_variable": "new_value",
  "existing_variable": "updated_value"
}
```

**状态码**:
- 200: 更新成功
- 400: 请求参数错误
- 404: 流程不存在

---

## WebSocket API

### 连接

```javascript
const ws = new WebSocket('ws://localhost:8080');
```

### 消息格式

#### 客户端消息

```json
{
  "type": "subscribe",
  "data": {}
}
```

#### 服务器消息

##### 执行开始
```json
{
  "type": "execution_started",
  "executionId": "exec_1234567890_def456",
  "processId": "process_1234567890_abc123",
  "timestamp": "2025-07-31T00:00:00.000Z"
}
```

##### 执行进度
```json
{
  "type": "execution_progress",
  "executionId": "exec_1234567890_def456",
  "processId": "process_1234567890_abc123",
  "progress": {
    "currentStep": 3,
    "totalSteps": 5,
    "percentage": 60,
    "currentActivity": "Validating results..."
  },
  "timestamp": "2025-07-31T00:00:00.000Z"
}
```

##### 执行完成
```json
{
  "type": "execution_completed",
  "executionId": "exec_1234567890_def456",
  "processId": "process_1234567890_abc123",
  "result": {
    "success": true,
    "executionTime": 8567,
    "output": {
      "message": "Process completed successfully",
      "processedItems": 25,
      "variables": {}
    }
  },
  "timestamp": "2025-07-31T00:00:00.000Z"
}
```

##### 执行错误
```json
{
  "type": "execution_error",
  "executionId": "exec_1234567890_def456",
  "processId": "process_1234567890_abc123",
  "error": "Execution failed: Timeout exceeded",
  "timestamp": "2025-07-31T00:00:00.000Z"
}
```

## 错误码

| 错误码 | 说明 | HTTP状态码 |
|--------|------|-----------|
| `VALIDATION_ERROR` | 请求参数验证失败 | 400 |
| `RESOURCE_NOT_FOUND` | 资源不存在 | 404 |
| `RESOURCE_CONFLICT` | 资源冲突 | 409 |
| `EXECUTION_TIMEOUT` | 执行超时 | 408 |
| `ENGINE_ERROR` | 引擎内部错误 | 500 |
| `SERVICE_UNAVAILABLE` | 服务不可用 | 503 |

## 限制和配额

- **最大并发执行数**: 10
- **单个执行超时**: 30分钟
- **最大流程数量**: 1000
- **最大变量大小**: 1MB
- **最大日志条数**: 1000条/执行

## 最佳实践

1. **错误处理**: 始终检查HTTP状态码和响应体中的错误信息
2. **重试机制**: 对于网络错误，实现指数退避重试
3. **资源清理**: 及时删除不需要的流程和执行记录
4. **监控**: 使用WebSocket实时监控执行状态
5. **参数验证**: 在发送请求前验证参数格式

## 示例代码

完整的示例代码请参考：
- [JavaScript示例](../examples/javascript/)
- [Python示例](../examples/python/)
- [curl脚本](../examples/curl/)

## 版本历史

### v1.0.0
- 初始版本
- 支持基本的流程管理和执行
- WebSocket实时通信
- 变量管理功能