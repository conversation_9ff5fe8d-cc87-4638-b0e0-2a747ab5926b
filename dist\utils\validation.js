"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.validateExecutionConfig = exports.validateConnectionConfig = exports.validateRPAEngine = exports.validate = exports.executionConfigSchema = exports.connectionConfigSchema = exports.rpaEngineSchema = void 0;
const joi_1 = __importDefault(require("joi"));
exports.rpaEngineSchema = joi_1.default.object({
    id: joi_1.default.string().required(),
    name: joi_1.default.string().required(),
    version: joi_1.default.string().required(),
    status: joi_1.default.string().valid('running', 'stopped', 'error').required(),
    endpoint: joi_1.default.string().uri().required(),
    capabilities: joi_1.default.array().items(joi_1.default.string()).required()
});
exports.connectionConfigSchema = joi_1.default.object({
    host: joi_1.default.string().hostname().required(),
    port: joi_1.default.number().port().required(),
    username: joi_1.default.string().optional(),
    password: joi_1.default.string().optional(),
    apiKey: joi_1.default.string().optional(),
    timeout: joi_1.default.number().min(1000).default(30000),
    retryAttempts: joi_1.default.number().min(0).default(3)
});
exports.executionConfigSchema = joi_1.default.object({
    engineId: joi_1.default.string().required(),
    processId: joi_1.default.string().required(),
    parameters: joi_1.default.object().default({}),
    timeout: joi_1.default.number().min(1000).optional(),
    retryCount: joi_1.default.number().min(0).default(0),
    debugMode: joi_1.default.boolean().default(false)
});
const validate = (schema, data) => {
    const { error, value } = schema.validate(data);
    if (error) {
        throw new Error(`Validation error: ${error.details[0].message}`);
    }
    return value;
};
exports.validate = validate;
const validateRPAEngine = (data) => (0, exports.validate)(exports.rpaEngineSchema, data);
exports.validateRPAEngine = validateRPAEngine;
const validateConnectionConfig = (data) => (0, exports.validate)(exports.connectionConfigSchema, data);
exports.validateConnectionConfig = validateConnectionConfig;
const validateExecutionConfig = (data) => (0, exports.validate)(exports.executionConfigSchema, data);
exports.validateExecutionConfig = validateExecutionConfig;
//# sourceMappingURL=validation.js.map