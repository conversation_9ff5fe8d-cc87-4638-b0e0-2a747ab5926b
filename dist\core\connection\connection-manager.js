"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RPAConnectionManager = void 0;
const engine_detector_1 = require("./engine-detector");
const logger_1 = require("../../utils/logger");
const events_1 = __importDefault(require("events"));
class RPAConnectionManager extends events_1.default {
    constructor() {
        super();
        this.connections = new Map();
        this.engines = new Map();
        this.detector = new engine_detector_1.RPAEngineDetector();
        this.logger = (0, logger_1.createLogger)('RPAConnectionManager');
        this.reconnectTimers = new Map();
    }
    async initialize() {
        this.logger.info('Initializing RPA connection manager...');
        try {
            const localEngines = await this.detector.detectLocalEngines();
            for (const engine of localEngines) {
                await this.addEngine(engine);
            }
            this.logger.info(`Initialized with ${localEngines.length} engines`);
        }
        catch (error) {
            this.logger.error('Failed to initialize connection manager:', error);
            throw error;
        }
    }
    async addEngine(engine) {
        try {
            const isValid = await this.detector.validateEngine(engine);
            if (!isValid) {
                throw new Error(`Engine ${engine.id} is not valid or accessible`);
            }
            const config = {
                host: new URL(engine.endpoint).hostname,
                port: parseInt(new URL(engine.endpoint).port) || 80,
                timeout: 30000,
                retryAttempts: 3
            };
            const connection = new engine_detector_1.RPAConnection(config);
            this.connections.set(engine.id, connection);
            this.engines.set(engine.id, engine);
            this.emit('connection', {
                type: 'connected',
                engineId: engine.id
            });
            this.logger.info(`Added engine: ${engine.name} (${engine.id})`);
        }
        catch (error) {
            this.logger.error(`Failed to add engine ${engine.id}:`, error);
            this.emit('connection', {
                type: 'error',
                engineId: engine.id,
                error: error instanceof Error ? error.message : String(error)
            });
            throw error;
        }
    }
    async removeEngine(engineId) {
        const connection = this.connections.get(engineId);
        if (connection) {
            this.clearReconnectTimer(engineId);
            this.connections.delete(engineId);
            this.engines.delete(engineId);
            this.emit('connection', {
                type: 'disconnected',
                engineId: engineId
            });
            this.logger.info(`Removed engine: ${engineId}`);
        }
    }
    getConnection(engineId) {
        return this.connections.get(engineId);
    }
    getEngine(engineId) {
        return this.engines.get(engineId);
    }
    getAllEngines() {
        return Array.from(this.engines.values());
    }
    getAvailableEngines() {
        return this.getAllEngines().filter(engine => engine.status === 'running');
    }
    async testEngine(engineId) {
        const connection = this.connections.get(engineId);
        if (!connection) {
            return false;
        }
        try {
            const isConnected = await connection.testConnection();
            const engine = this.engines.get(engineId);
            if (engine) {
                engine.status = isConnected ? 'running' : 'error';
                this.engines.set(engineId, engine);
            }
            if (!isConnected) {
                this.scheduleReconnect(engineId);
            }
            return isConnected;
        }
        catch (error) {
            this.logger.error(`Engine test failed for ${engineId}:`, error);
            this.scheduleReconnect(engineId);
            return false;
        }
    }
    async refreshEngines() {
        this.logger.info('Refreshing engine list...');
        try {
            const localEngines = await this.detector.detectLocalEngines();
            const currentEngineIds = new Set(this.engines.keys());
            for (const engine of localEngines) {
                if (!currentEngineIds.has(engine.id)) {
                    await this.addEngine(engine);
                }
            }
            for (const engineId of currentEngineIds) {
                const exists = localEngines.some(e => e.id === engineId);
                if (!exists) {
                    await this.removeEngine(engineId);
                }
            }
        }
        catch (error) {
            this.logger.error('Failed to refresh engines:', error);
        }
    }
    scheduleReconnect(engineId) {
        this.clearReconnectTimer(engineId);
        const timer = setTimeout(async () => {
            try {
                await this.testEngine(engineId);
            }
            catch (error) {
                this.logger.error(`Reconnect failed for ${engineId}:`, error);
            }
        }, 30000); // 30 seconds
        this.reconnectTimers.set(engineId, timer);
    }
    clearReconnectTimer(engineId) {
        const timer = this.reconnectTimers.get(engineId);
        if (timer) {
            clearTimeout(timer);
            this.reconnectTimers.delete(engineId);
        }
    }
    dispose() {
        for (const timer of this.reconnectTimers.values()) {
            clearTimeout(timer);
        }
        this.reconnectTimers.clear();
        this.connections.clear();
        this.engines.clear();
        this.removeAllListeners();
    }
}
exports.RPAConnectionManager = RPAConnectionManager;
//# sourceMappingURL=connection-manager.js.map