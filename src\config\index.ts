import { PluginConfig } from '../types';

export const defaultConfig: PluginConfig = {
  id: 'cursor-rpa-integration',
  name: 'Cursor RPA Integration',
  version: '1.0.0',
  enabled: true,
  settings: {
    rpaEngine: {
      host: 'localhost',
      port: 8080,
      timeout: 30000,
      retryAttempts: 3
    },
    ai: {
      model: 'gpt-3.5-turbo',
      temperature: 0.7,
      maxTokens: 1000,
      timeout: 30000
    },
    debug: {
      enabled: false,
      logLevel: 'info'
    }
  }
};

export const loadConfig = (): PluginConfig => {
  try {
    const fs = require('fs');
    const path = require('path');
    const configPath = path.join(process.cwd(), 'config.json');
    
    if (fs.existsSync(configPath)) {
      const configData = fs.readFileSync(configPath, 'utf8');
      return { ...defaultConfig, ...JSON.parse(configData) };
    }
  } catch (error) {
    console.warn('Failed to load config file, using defaults:', error);
  }
  
  return defaultConfig;
};

export const saveConfig = (config: PluginConfig): void => {
  try {
    const fs = require('fs');
    const path = require('path');
    const configPath = path.join(process.cwd(), 'config.json');
    
    fs.writeFileSync(configPath, JSON.stringify(config, null, 2));
  } catch (error) {
    console.error('Failed to save config file:', error);
    throw error;
  }
};