import { RPAConnectionManager } from './connection';
import { RPAProcessManager } from './execution';
export declare class RPAEngine {
    private connectionManager;
    private processManager;
    private logger;
    private initialized;
    constructor();
    initialize(): Promise<void>;
    shutdown(): Promise<void>;
    getConnectionManager(): RPAConnectionManager;
    getProcessManager(): RPAProcessManager;
    isInitialized(): boolean;
}
export declare const rpaEngine: RPAEngine;
//# sourceMappingURL=index.d.ts.map