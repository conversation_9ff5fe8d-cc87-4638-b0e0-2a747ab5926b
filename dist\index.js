"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.main = main;
exports.shutdown = shutdown;
const activation_1 = require("./plugin/activation");
const core_1 = require("./core");
const engine_1 = require("./engine");
const logger_1 = require("./utils/logger");
const logger = (0, logger_1.createLogger)('main');
async function main() {
    try {
        logger.info('Starting Cursor RPA Integration...');
        // Start Yingdao RPA Engine service first
        logger.info('Starting Yingdao RPA Engine service...');
        await engine_1.engineServiceManager.start();
        // Wait a moment for engine to start
        await new Promise(resolve => setTimeout(resolve, 2000));
        // Initialize RPA Engine
        await core_1.rpaEngine.initialize();
        // Activate plugin
        await activation_1.pluginActivator.activate();
        logger.info('Cursor RPA Integration started successfully');
        logger.info(`Yingdao RPA Engine running on port ${engine_1.engineServiceManager.getPort()}`);
        // Keep the process running
        process.on('SIGINT', async () => {
            logger.info('Received SIGINT, shutting down...');
            await shutdown();
            process.exit(0);
        });
        process.on('SIGTERM', async () => {
            logger.info('Received SIGTERM, shutting down...');
            await shutdown();
            process.exit(0);
        });
        // For development, let it run longer to test the engine
        setTimeout(async () => {
            logger.info('Development timeout reached, shutting down...');
            await shutdown();
            process.exit(0);
        }, 30000); // Run for 30 seconds
    }
    catch (error) {
        logger.error('Failed to start application:', error);
        process.exit(1);
    }
}
async function shutdown() {
    try {
        logger.info('Shutting down...');
        // Deactivate plugin
        await activation_1.pluginActivator.deactivate();
        // Shutdown RPA Engine
        await core_1.rpaEngine.shutdown();
        // Stop engine service
        if (engine_1.engineServiceManager.isServiceRunning()) {
            await engine_1.engineServiceManager.stop();
        }
        logger.info('Shutdown completed');
    }
    catch (error) {
        logger.error('Error during shutdown:', error);
    }
}
// Start the application
main().catch(error => {
    logger.error('Unhandled error:', error);
    process.exit(1);
});
//# sourceMappingURL=index.js.map