import { YingdaoRPAServer } from './services/yingdao-server';
import { createLogger } from '../utils/logger';

export class EngineServiceManager {
  private server: YingdaoRPAServer;
  private logger = createLogger('EngineServiceManager');
  private isRunning = false;

  constructor(port: number = 8080) {
    this.server = new YingdaoRPAServer(port);
  }

  async start(): Promise<void> {
    if (this.isRunning) {
      this.logger.warn('Engine service is already running');
      return;
    }

    try {
      await this.server.start();
      this.isRunning = true;
      this.logger.info(`Engine service started on port ${this.server.getPort()}`);
    } catch (error) {
      this.logger.error('Failed to start engine service:', error);
      throw error;
    }
  }

  async stop(): Promise<void> {
    if (!this.isRunning) {
      this.logger.warn('Engine service is not running');
      return;
    }

    try {
      await this.server.stop();
      this.isRunning = false;
      this.logger.info('Engine service stopped');
    } catch (error) {
      this.logger.error('Failed to stop engine service:', error);
      throw error;
    }
  }

  isServiceRunning(): boolean {
    return this.isRunning;
  }

  getServer(): YingdaoRPAServer {
    return this.server;
  }

  getPort(): number {
    return this.server.getPort();
  }
}

export const engineServiceManager = new EngineServiceManager();