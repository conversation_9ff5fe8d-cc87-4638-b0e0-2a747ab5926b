export interface YingdaoEngineInfo {
    name: string;
    version: string;
    capabilities: string[];
    status: 'running' | 'stopped' | 'error';
}
export interface ProcessInfo {
    id: string;
    name: string;
    description: string;
    status: 'idle' | 'running' | 'completed' | 'error';
    created: Date;
    modified: Date;
    variables: Record<string, any>;
}
export declare class YingdaoRPAServer {
    private app;
    private server;
    private wss;
    private logger;
    private port;
    private processes;
    private activeExecutions;
    constructor(port?: number);
    private setupMiddleware;
    private setupRoutes;
    private setupWebSocket;
    private handleWebSocketMessage;
    private executeProcess;
    private broadcastEvent;
    private initializeSampleProcesses;
    private delay;
    start(): Promise<void>;
    stop(): Promise<void>;
    getPort(): number;
    getActiveProcesses(): ProcessInfo[];
    getActiveExecutions(): any[];
}
//# sourceMappingURL=yingdao-server.d.ts.map