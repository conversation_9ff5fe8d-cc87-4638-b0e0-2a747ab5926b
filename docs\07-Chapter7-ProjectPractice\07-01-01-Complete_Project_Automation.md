# 项目实战与案例分析

## 7.1 综合项目实战

### 7.1.1 完整的建筑设计自动化项目

**项目概述：**
本节将介绍一个完整的建筑设计自动化项目，涵盖从图纸创建到标准输出的全流程自动化。

**项目架构：**
```csharp
using Autodesk.AutoCAD.Runtime;
using Autodesk.AutoCAD.ApplicationServices;
using Autodesk.AutoCAD.DatabaseServices;
using Autodesk.AutoCAD.Geometry;
using Autodesk.AutoCAD.EditorInput;
using System;
using System.Collections.Generic;
using System.IO;
using System.Xml.Serialization;

namespace AutoCAD.Architecture.CompleteProject
{
    // 项目配置管理器
    public class ProjectConfigurationManager
    {
        private static ProjectConfiguration _currentConfig;
        
        [CommandMethod("LOADPROJECTCONFIG")]
        public void LoadProjectConfig()
        {
            Document doc = Application.DocumentManager.MdiActiveDocument;
            Editor ed = doc.Editor;
            
            try
            {
                // 选择配置文件
                string configPath = SelectConfigFile(ed);
                if (string.IsNullOrEmpty(configPath))
                    return;
                
                // 加载配置
                _currentConfig = LoadConfigurationFromFile(configPath);
                
                ed.WriteMessage($"\n项目配置加载成功：{_currentConfig.ProjectName}");
                ed.WriteMessage($"\n项目路径：{_currentConfig.ProjectPath}");
                ed.WriteMessage($"\n标准图层：{_currentConfig.StandardLayers.Count}个");
                ed.WriteMessage($"\n图框模板：{_currentConfig.TitleBlockTemplate}");
            }
            catch (System.Exception ex)
            {
                ed.WriteMessage($"\n加载项目配置失败：{ex.Message}");
            }
        }
        
        [CommandMethod("SAVEPROJECTCONFIG")]
        public void SaveProjectConfig()
        {
            Document doc = Application.DocumentManager.MdiActiveDocument;
            Editor ed = doc.Editor;
            
            try
            {
                if (_currentConfig == null)
                {
                    ed.WriteMessage("\n请先加载或创建项目配置");
                    return;
                }
                
                // 保存配置
                string configPath = Path.Combine(
                    _currentConfig.ProjectPath,
                    $"{_currentConfig.ProjectName}_config.xml");
                
                SaveConfigurationToFile(_currentConfig, configPath);
                
                ed.WriteMessage($"\n项目配置已保存到：{configPath}");
            }
            catch (System.Exception ex)
            {
                ed.WriteMessage($"\n保存项目配置失败：{ex.Message}");
            }
        }
        
        private string SelectConfigFile(Editor ed)
        {
            using (var dialog = new System.Windows.Forms.OpenFileDialog())
            {
                dialog.Filter = "项目配置文件 (*.xml)|*.xml|所有文件 (*.*)|*.*";
                dialog.Title = "选择项目配置文件";
                
                if (dialog.ShowDialog() == System.Windows.Forms.DialogResult.OK)
                {
                    return dialog.FileName;
                }
            }
            
            return null;
        }
        
        private ProjectConfiguration LoadConfigurationFromFile(string filePath)
        {
            var serializer = new XmlSerializer(typeof(ProjectConfiguration));
            
            using (var reader = new StreamReader(filePath))
            {
                return (ProjectConfiguration)serializer.Deserialize(reader);
            }
        }
        
        private void SaveConfigurationToFile(ProjectConfiguration config, string filePath)
        {
            var serializer = new XmlSerializer(typeof(ProjectConfiguration));
            
            using (var writer = new StreamWriter(filePath))
            {
                serializer.Serialize(writer, config);
            }
        }
    }
    
    // 完整的自动化绘图系统
    public class CompleteDrawingAutomationSystem
    {
        [CommandMethod("CREATECOMPLETEPROJECT")]
        public void CreateCompleteProject()
        {
            Document doc = Application.DocumentManager.MdiActiveDocument;
            Database db = doc.Database;
            Editor ed = doc.Editor;
            
            try
            {
                var config = ProjectConfigurationManager.GetCurrentConfig();
                if (config == null)
                {
                    ed.WriteMessage("\n请先加载项目配置");
                    return;
                }
                
                // 创建项目目录结构
                CreateProjectDirectoryStructure(config);
                
                // 设置标准图层
                SetupStandardLayers(db, config);
                
                // 创建图框模板
                CreateTitleBlockTemplate(db, config);
                
                // 创建标准图块库
                CreateStandardBlockLibrary(db, config);
                
                // 设置标注样式
                SetupDimensionStyles(db, config);
                
                // 设置文字样式
                SetupTextStyles(db, config);
                
                ed.WriteMessage("\n完整项目创建完成！");
                ed.WriteMessage($"\n项目名称：{config.ProjectName}");
                ed.WriteMessage($"\n项目路径：{config.ProjectPath}");
            }
            catch (System.Exception ex)
            {
                ed.WriteMessage($"\n创建完整项目失败：{ex.Message}");
            }
        }
        
        private void CreateProjectDirectoryStructure(ProjectConfiguration config)
        {
            // 创建项目目录结构
            string[] directories = {
                config.ProjectPath,
                Path.Combine(config.ProjectPath, "Drawings"),
                Path.Combine(config.ProjectPath, "Blocks"),
                Path.Combine(config.ProjectPath, "Templates"),
                Path.Combine(config.ProjectPath, "Standards"),
                Path.Combine(config.ProjectPath, "Output")
            };
            
            foreach (string dir in directories)
            {
                if (!Directory.Exists(dir))
                {
                    Directory.CreateDirectory(dir);
                }
            }
        }
        
        private void SetupStandardLayers(Database db, ProjectConfiguration config)
        {
            using (Transaction trans = db.TransactionManager.StartTransaction())
            {
                LayerTable lt = trans.GetObject(db.LayerTableId, OpenMode.ForWrite) as LayerTable;
                
                foreach (var layerConfig in config.StandardLayers)
                {
                    if (!lt.Has(layerConfig.Name))
                    {
                        LayerTableRecord layer = new LayerTableRecord();
                        layer.Name = layerConfig.Name;
                        layer.Color = Color.FromColorIndex(ColorMethod.ByColor, layerConfig.Color);
                        layer.LineWeight = layerConfig.LineWeight;
                        layer.Description = layerConfig.Description;
                        
                        lt.Add(layer);
                        trans.AddNewlyCreatedDBObject(layer, true);
                    }
                }
                
                trans.Commit();
            }
        }
        
        private void CreateTitleBlockTemplate(Database db, ProjectConfiguration config)
        {
            // 创建图框模板
            var titleBlockGenerator = new TitleBlockGenerator();
            titleBlockGenerator.CreateTemplate(db, config.TitleBlockTemplate);
        }
        
        private void CreateStandardBlockLibrary(Database db, ProjectConfiguration config)
        {
            // 创建标准图块库
            var blockLibrary = new StandardBlockLibrary();
            blockLibrary.CreateLibrary(db, config.BlockLibraryPath);
        }
        
        private void SetupDimensionStyles(Database db, ProjectConfiguration config)
        {
            // 设置标注样式
            var dimensionSetup = new DimensionStyleSetup();
            dimensionSetup.SetupStyles(db, config.DimensionStyles);
        }
        
        private void SetupTextStyles(Database db, ProjectConfiguration config)
        {
            // 设置文字样式
            var textStyleSetup = new TextStyleSetup();
            textStyleSetup.SetupStyles(db, config.TextStyles);
        }
    }
    
    // 批量图纸处理系统
    public class BatchDrawingProcessingSystem
    {
        [CommandMethod("BATCHPROCESSPROJECT")]
        public void BatchProcessProject()
        {
            Document doc = Application.DocumentManager.MdiActiveDocument;
            Editor ed = doc.Editor;
            
            try
            {
                var config = ProjectConfigurationManager.GetCurrentConfig();
                if (config == null)
                {
                    ed.WriteMessage("\n请先加载项目配置");
                    return;
                }
                
                // 获取处理参数
                var processParams = GetBatchProcessParameters(ed);
                if (processParams == null)
                    return;
                
                // 批量处理图纸
                var results = BatchProcessDrawings(config, processParams);
                
                // 生成处理报告
                GenerateProcessingReport(results, config);
                
                ed.WriteMessage("\n批量处理完成！");
                ed.WriteMessage($"\n成功处理：{results.SuccessCount}个文件");
                ed.WriteMessage($"\n失败处理：{results.FailureCount}个文件");
            }
            catch (System.Exception ex)
            {
                ed.WriteMessage($"\n批量处理失败：{ex.Message}");
            }
        }
        
        private BatchProcessParameters GetBatchProcessParameters(Editor ed)
        {
            // 选择处理模式
            PromptKeywordOptions modeOpt = new PromptKeywordOptions("\n选择处理模式: ");
            modeOpt.Keywords.Add("STANDARDIZE");
            modeOpt.Keywords.Add("PLOT");
            modeOpt.Keywords.Add("EXPORT");
            modeOpt.Keywords.Add("VALIDATE");
            modeOpt.Default = "STANDARDIZE";
            
            PromptResult modeResult = ed.GetKeywords(modeOpt);
            if (modeResult.Status != PromptStatus.OK)
                return null;
            
            // 获取处理范围
            PromptKeywordOptions scopeOpt = new PromptKeywordOptions("\n选择处理范围: ");
            scopeOpt.Keywords.Add("CURRENT");
            scopeOpt.Keywords.Add("FOLDER");
            scopeOpt.Keywords.Add("PROJECT");
            scopeOpt.Default = "CURRENT";
            
            PromptResult scopeResult = ed.GetKeywords(scopeOpt);
            if (scopeResult.Status != PromptStatus.OK)
                return null;
            
            return new BatchProcessParameters
            {
                ProcessMode = modeResult.StringResult,
                ProcessScope = scopeResult.StringResult
            };
        }
        
        private BatchProcessResults BatchProcessDrawings(ProjectConfiguration config, BatchProcessParameters parameters)
        {
            var results = new BatchProcessResults();
            var drawingFiles = GetDrawingFiles(config, parameters);
            
            foreach (string filePath in drawingFiles)
            {
                try
                {
                    ProcessSingleDrawing(filePath, parameters);
                    results.SuccessCount++;
                }
                catch (System.Exception ex)
                {
                    results.FailureCount++;
                    results.ErrorMessages.Add($"{Path.GetFileName(filePath)}: {ex.Message}");
                }
            }
            
            return results;
        }
        
        private List<string> GetDrawingFiles(ProjectConfiguration config, BatchProcessParameters parameters)
        {
            var files = new List<string>();
            
            switch (parameters.ProcessScope)
            {
                case "CURRENT":
                    files.Add(Application.DocumentManager.MdiActiveDocument.Name);
                    break;
                case "FOLDER":
                    string folderPath = SelectFolder();
                    if (!string.IsNullOrEmpty(folderPath))
                    {
                        files.AddRange(Directory.GetFiles(folderPath, "*.dwg"));
                    }
                    break;
                case "PROJECT":
                    string drawingsPath = Path.Combine(config.ProjectPath, "Drawings");
                    if (Directory.Exists(drawingsPath))
                    {
                        files.AddRange(Directory.GetFiles(drawingsPath, "*.dwg"));
                    }
                    break;
            }
            
            return files;
        }
        
        private void ProcessSingleDrawing(string filePath, BatchProcessParameters parameters)
        {
            using (var db = new Database(false, true))
            {
                db.ReadDwgFile(filePath, FileOpenMode.OpenForReadAndWriteNoShare, false, null);
                
                switch (parameters.ProcessMode)
                {
                    case "STANDARDIZE":
                        StandardizeDrawing(db);
                        break;
                    case "PLOT":
                        PlotDrawing(db);
                        break;
                    case "EXPORT":
                        ExportDrawing(db);
                        break;
                    case "VALIDATE":
                        ValidateDrawing(db);
                        break;
                }
                
                db.SaveAs(filePath, DwgVersion.Current);
            }
        }
        
        private void StandardizeDrawing(Database db)
        {
            // 标准化图纸
            var standardizer = new DrawingStandardizer();
            standardizer.Standardize(db);
        }
        
        private void PlotDrawing(Database db)
        {
            // 打印图纸
            var plotter = new DrawingPlotter();
            plotter.Plot(db);
        }
        
        private void ExportDrawing(Database db)
        {
            // 导出图纸
            var exporter = new DrawingExporter();
            exporter.Export(db);
        }
        
        private void ValidateDrawing(Database db)
        {
            // 验证图纸
            var validator = new DrawingValidator();
            validator.Validate(db);
        }
        
        private string SelectFolder()
        {
            using (var dialog = new System.Windows.Forms.FolderBrowserDialog())
            {
                dialog.Description = "选择包含DWG文件的文件夹";
                
                if (dialog.ShowDialog() == System.Windows.Forms.DialogResult.OK)
                {
                    return dialog.SelectedPath;
                }
            }
            
            return null;
        }
        
        private void GenerateProcessingReport(BatchProcessResults results, ProjectConfiguration config)
        {
            string reportPath = Path.Combine(config.ProjectPath, "Output", 
                $"BatchProcessingReport_{DateTime.Now:yyyyMMdd_HHmmss}.txt");
            
            using (var writer = new StreamWriter(reportPath))
            {
                writer.WriteLine("=== 批量处理报告 ===");
                writer.WriteLine($"处理时间：{DateTime.Now:yyyy-MM-dd HH:mm:ss}");
                writer.WriteLine($"项目名称：{config.ProjectName}");
                writer.WriteLine();
                writer.WriteLine($"成功处理：{results.SuccessCount}个文件");
                writer.WriteLine($"失败处理：{results.FailureCount}个文件");
                
                if (results.ErrorMessages.Count > 0)
                {
                    writer.WriteLine();
                    writer.WriteLine("错误详情：");
                    foreach (string error in results.ErrorMessages)
                    {
                        writer.WriteLine($"  - {error}");
                    }
                }
            }
        }
    }
    
    // 项目数据结构
    public class ProjectConfiguration
    {
        public string ProjectName { get; set; }
        public string ProjectPath { get; set; }
        public List<LayerConfiguration> StandardLayers { get; set; }
        public string TitleBlockTemplate { get; set; }
        public string BlockLibraryPath { get; set; }
        public List<DimensionStyleConfiguration> DimensionStyles { get; set; }
        public List<TextStyleConfiguration> TextStyles { get; set; }
        public ProjectStandards Standards { get; set; }
    }
    
    public class LayerConfiguration
    {
        public string Name { get; set; }
        public short Color { get; set; }
        public LineWeight LineWeight { get; set; }
        public string Description { get; set; }
    }
    
    public class DimensionStyleConfiguration
    {
        public string Name { get; set; }
        public double Scale { get; set; }
        public double TextHeight { get; set; }
        public double ArrowSize { get; set; }
    }
    
    public class TextStyleConfiguration
    {
        public string Name { get; set; }
        public string Font { get; set; }
        public double Height { get; set; }
    }
    
    public class ProjectStandards
    {
        public double DrawingScale { get; set; }
        public string Units { get; set; }
        public double LinetypeScale { get; set; }
    }
    
    public class BatchProcessParameters
    {
        public string ProcessMode { get; set; }
        public string ProcessScope { get; set; }
    }
    
    public class BatchProcessResults
    {
        public int SuccessCount { get; set; }
        public int FailureCount { get; set; }
        public List<string> ErrorMessages { get; set; } = new List<string>();
    }
    
    // 项目配置管理器扩展
    public static class ProjectConfigurationManagerExtensions
    {
        public static ProjectConfiguration GetCurrentConfig()
        {
            // 这里可以实现配置的获取逻辑
            return new ProjectConfiguration
            {
                ProjectName = "示例项目",
                ProjectPath = @"C:\Projects\SampleProject",
                StandardLayers = new List<LayerConfiguration>
                {
                    new LayerConfiguration { Name = "WALLS", Color = 1, LineWeight = LineWeight.LineWeight050, Description = "墙体" },
                    new LayerConfiguration { Name = "DOORS", Color = 2, LineWeight = LineWeight.LineWeight025, Description = "门" },
                    new LayerConfiguration { Name = "WINDOWS", Color = 3, LineWeight = LineWeight.LineWeight025, Description = "窗" }
                },
                TitleBlockTemplate = "A3_TitleBlock",
                BlockLibraryPath = @"C:\Projects\SampleProject\Blocks",
                Standards = new ProjectStandards
                {
                    DrawingScale = 1.0,
                    Units = "Millimeters",
                    LinetypeScale = 1.0
                }
            };
        }
    }
}
```

### 7.1.2 项目质量控制系统

**质量控制实现：**
```csharp
using Autodesk.AutoCAD.Runtime;
using Autodesk.AutoCAD.ApplicationServices;
using Autodesk.AutoCAD.DatabaseServices;
using Autodesk.AutoCAD.Geometry;
using Autodesk.AutoCAD.EditorInput;
using System;
using System.Collections.Generic;
using System.Linq;

namespace AutoCAD.Architecture.QualityControl
{
    // 质量控制系统
    public class ProjectQualityControlSystem
    {
        [CommandMethod("RUNQUALITYCHECK")]
        public void RunQualityCheck()
        {
            Document doc = Application.DocumentManager.MdiActiveDocument;
            Database db = doc.Database;
            Editor ed = doc.Editor;
            
            try
            {
                ed.WriteMessage("\n开始质量检查...");
                
                // 执行全面质量检查
                var report = PerformComprehensiveQualityCheck(db);
                
                // 显示检查结果
                DisplayQualityReport(ed, report);
                
                // 保存检查报告
                SaveQualityReport(report, doc.Name);
                
                ed.WriteMessage("\n质量检查完成！");
            }
            catch (System.Exception ex)
            {
                ed.WriteMessage($"\n质量检查失败：{ex.Message}");
            }
        }
        
        private QualityReport PerformComprehensiveQualityCheck(Database db)
        {
            var report = new QualityReport();
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();
            
            // 图层标准检查
            report.LayerCheckResult = CheckLayerStandards(db);
            
            // 图块标准检查
            report.BlockCheckResult = CheckBlockStandards(db);
            
            // 标注标准检查
            report.DimensionCheckResult = CheckDimensionStandards(db);
            
            // 文字标准检查
            report.TextCheckResult = CheckTextStandards(db);
            
            // 图纸完整性检查
            report.IntegrityCheckResult = CheckDrawingIntegrity(db);
            
            // 性能检查
            report.PerformanceCheckResult = CheckDrawingPerformance(db);
            
            stopwatch.Stop();
            report.CheckTime = stopwatch.Elapsed;
            
            // 计算总体评分
            report.OverallScore = CalculateOverallScore(report);
            
            return report;
        }
        
        private LayerCheckResult CheckLayerStandards(Database db)
        {
            var result = new LayerCheckResult();
            
            using (Transaction trans = db.TransactionManager.StartTransaction())
            {
                LayerTable lt = trans.GetObject(db.LayerTableId, OpenMode.ForRead) as LayerTable;
                
                foreach (ObjectId layerId in lt)
                {
                    LayerTableRecord layer = trans.GetObject(layerId, OpenMode.ForRead) as LayerTableRecord;
                    
                    // 检查图层名称
                    if (!IsValidLayerName(layer.Name))
                    {
                        result.Issues.Add(new LayerIssue
                        {
                            LayerName = layer.Name,
                            IssueType = "命名不规范",
                            Severity = IssueSeverity.Warning
                        });
                    }
                    
                    // 检查图层颜色
                    if (layer.Color.ColorIndex < 1 || layer.Color.ColorIndex > 255)
                    {
                        result.Issues.Add(new LayerIssue
                        {
                            LayerName = layer.Name,
                            IssueType = "颜色无效",
                            Severity = IssueSeverity.Error
                        });
                    }
                    
                    // 检查图层线宽
                    if (layer.LineWeight == LineWeight.ByLayer || layer.LineWeight == LineWeight.ByBlock)
                    {
                        result.Issues.Add(new LayerIssue
                        {
                            LayerName = layer.Name,
                            IssueType = "线宽未设置",
                            Severity = IssueSeverity.Warning
                        });
                    }
                }
                
                trans.Commit();
            }
            
            return result;
        }
        
        private BlockCheckResult CheckBlockStandards(Database db)
        {
            var result = new BlockCheckResult();
            
            using (Transaction trans = db.TransactionManager.StartTransaction())
            {
                BlockTable bt = trans.GetObject(db.BlockTableId, OpenMode.ForRead) as BlockTable;
                
                foreach (ObjectId blockId in bt)
                {
                    BlockTableRecord block = trans.GetObject(blockId, OpenMode.ForRead) as BlockTableRecord;
                    
                    if (block.IsLayout || block.IsAnonymous)
                        continue;
                    
                    // 检查块定义
                    if (block.Name.StartsWith("*"))
                        continue;
                    
                    // 检查块使用情况
                    if (!IsBlockUsed(db, block.Name))
                    {
                        result.UnusedBlocks.Add(block.Name);
                    }
                    
                    // 检查块定义完整性
                    if (block.GetHandOverTo().Count == 0)
                    {
                        result.Issues.Add(new BlockIssue
                        {
                            BlockName = block.Name,
                            IssueType = "空块定义",
                            Severity = IssueSeverity.Warning
                        });
                    }
                }
                
                trans.Commit();
            }
            
            return result;
        }
        
        private DimensionCheckResult CheckDimensionStandards(Database db)
        {
            var result = new DimensionCheckResult();
            
            using (Transaction trans = db.TransactionManager.StartTransaction())
            {
                BlockTable bt = trans.GetObject(db.BlockTableId, OpenMode.ForRead) as BlockTable;
                BlockTableRecord modelSpace = trans.GetObject(bt[BlockTableRecord.ModelSpace], OpenMode.ForRead) as BlockTableRecord;
                
                foreach (ObjectId objId in modelSpace)
                {
                    Entity ent = trans.GetObject(objId, OpenMode.ForRead) as Entity;
                    
                    if (ent is Dimension dim)
                    {
                        // 检查标注样式
                        if (string.IsNullOrEmpty(dim.DimensionStyleName))
                        {
                            result.Issues.Add(new DimensionIssue
                            {
                                EntityHandle = ent.Handle.ToString(),
                                IssueType = "标注样式未设置",
                                Severity = IssueSeverity.Error
                            });
                        }
                        
                        // 检查标注文字
                        if (dim is RotatedDimension || dim is AlignedDimension)
                        {
                            if (dim.DimensionText == "<>" || string.IsNullOrEmpty(dim.DimensionText))
                            {
                                result.Issues.Add(new DimensionIssue
                                {
                                    EntityHandle = ent.Handle.ToString(),
                                    IssueType = "标注文字为空",
                                    Severity = IssueSeverity.Warning
                                });
                            }
                        }
                    }
                }
                
                trans.Commit();
            }
            
            return result;
        }
        
        private TextCheckResult CheckTextStandards(Database db)
        {
            var result = new TextCheckResult();
            
            using (Transaction trans = db.TransactionManager.StartTransaction())
            {
                BlockTable bt = trans.GetObject(db.BlockTableId, OpenMode.ForRead) as BlockTable;
                BlockTableRecord modelSpace = trans.GetObject(bt[BlockTableRecord.ModelSpace], OpenMode.ForRead) as BlockTableRecord;
                
                foreach (ObjectId objId in modelSpace)
                {
                    Entity ent = trans.GetObject(objId, OpenMode.ForRead) as Entity;
                    
                    if (ent is DBText text)
                    {
                        // 检查文字高度
                        if (text.Height <= 0)
                        {
                            result.Issues.Add(new TextIssue
                            {
                                EntityHandle = ent.Handle.ToString(),
                                IssueType = "文字高度无效",
                                Severity = IssueSeverity.Error
                            });
                        }
                        
                        // 检查文字内容
                        if (string.IsNullOrEmpty(text.TextString))
                        {
                            result.Issues.Add(new TextIssue
                            {
                                EntityHandle = ent.Handle.ToString(),
                                IssueType = "文字内容为空",
                                Severity = IssueSeverity.Warning
                            });
                        }
                    }
                    else if (ent is MText mtext)
                    {
                        // 检查多行文字
                        if (mtext.Height <= 0)
                        {
                            result.Issues.Add(new TextIssue
                            {
                                EntityHandle = ent.Handle.ToString(),
                                IssueType = "多行文字高度无效",
                                Severity = IssueSeverity.Error
                            });
                        }
                    }
                }
                
                trans.Commit();
            }
            
            return result;
        }
        
        private IntegrityCheckResult CheckDrawingIntegrity(Database db)
        {
            var result = new IntegrityCheckResult();
            
            try
            {
                // 检查数据库完整性
                using (Transaction trans = db.TransactionManager.StartTransaction())
                {
                    // 检查是否有损坏的对象
                    db.Audit(true);
                    
                    // 检查未使用的对象
                    int purgedCount = db.Purge();
                    result.PurgedObjectCount = purgedCount;
                    
                    trans.Commit();
                }
                
                // 检查文件大小
                var fileInfo = new System.IO.FileInfo(db.Filename);
                result.FileSizeMB = fileInfo.Length / (1024.0 * 1024.0);
                
                // 检查对象数量
                result.TotalObjectCount = CountTotalObjects(db);
                
            }
            catch (System.Exception ex)
            {
                result.HasErrors = true;
                result.ErrorMessage = ex.Message;
            }
            
            return result;
        }
        
        private PerformanceCheckResult CheckDrawingPerformance(Database db)
        {
            var result = new PerformanceCheckResult();
            
            // 检查对象数量
            result.ObjectCount = CountTotalObjects(db);
            
            // 检查图层数量
            result.LayerCount = CountLayers(db);
            
            // 检查块数量
            result.BlockCount = CountBlocks(db);
            
            // 检查复杂对象数量
            result.ComplexObjectCount = CountComplexObjects(db);
            
            // 性能评分
            result.PerformanceScore = CalculatePerformanceScore(result);
            
            return result;
        }
        
        private int CountTotalObjects(Database db)
        {
            int count = 0;
            
            using (Transaction trans = db.TransactionManager.StartTransaction())
            {
                BlockTable bt = trans.GetObject(db.BlockTableId, OpenMode.ForRead) as BlockTable;
                BlockTableRecord modelSpace = trans.GetObject(bt[BlockTableRecord.ModelSpace], OpenMode.ForRead) as BlockTableRecord;
                
                foreach (ObjectId objId in modelSpace)
                {
                    count++;
                }
                
                trans.Commit();
            }
            
            return count;
        }
        
        private int CountLayers(Database db)
        {
            int count = 0;
            
            using (Transaction trans = db.TransactionManager.StartTransaction())
            {
                LayerTable lt = trans.GetObject(db.LayerTableId, OpenMode.ForRead) as LayerTable;
                
                foreach (ObjectId layerId in lt)
                {
                    count++;
                }
                
                trans.Commit();
            }
            
            return count;
        }
        
        private int CountBlocks(Database db)
        {
            int count = 0;
            
            using (Transaction trans = db.TransactionManager.StartTransaction())
            {
                BlockTable bt = trans.GetObject(db.BlockTableId, OpenMode.ForRead) as BlockTable;
                
                foreach (ObjectId blockId in bt)
                {
                    BlockTableRecord block = trans.GetObject(blockId, OpenMode.ForRead) as BlockTableRecord;
                    if (!block.IsLayout && !block.IsAnonymous)
                    {
                        count++;
                    }
                }
                
                trans.Commit();
            }
            
            return count;
        }
        
        private int CountComplexObjects(Database db)
        {
            int count = 0;
            
            using (Transaction trans = db.TransactionManager.StartTransaction())
            {
                BlockTable bt = trans.GetObject(db.BlockTableId, OpenMode.ForRead) as BlockTable;
                BlockTableRecord modelSpace = trans.GetObject(bt[BlockTableRecord.ModelSpace], OpenMode.ForRead) as BlockTableRecord;
                
                foreach (ObjectId objId in modelSpace)
                {
                    Entity ent = trans.GetObject(objId, OpenMode.ForRead) as Entity;
                    if (ent is Hatch || ent is Polyline || ent is BlockReference)
                    {
                        count++;
                    }
                }
                
                trans.Commit();
            }
            
            return count;
        }
        
        private double CalculatePerformanceScore(PerformanceCheckResult performance)
        {
            double score = 100.0;
            
            // 对象数量扣分
            if (performance.ObjectCount > 10000)
                score -= 20;
            else if (performance.ObjectCount > 5000)
                score -= 10;
            
            // 图层数量扣分
            if (performance.LayerCount > 100)
                score -= 15;
            else if (performance.LayerCount > 50)
                score -= 5;
            
            // 复杂对象扣分
            if (performance.ComplexObjectCount > 1000)
                score -= 25;
            else if (performance.ComplexObjectCount > 500)
                score -= 10;
            
            return Math.Max(0, score);
        }
        
        private double CalculateOverallScore(QualityReport report)
        {
            double totalScore = 0.0;
            int weightCount = 0;
            
            // 图层检查权重
            totalScore += (report.LayerCheckResult.Issues.Count == 0 ? 100 : 50) * 0.2;
            weightCount++;
            
            // 图块检查权重
            totalScore += (report.BlockCheckResult.Issues.Count == 0 ? 100 : 60) * 0.2;
            weightCount++;
            
            // 标注检查权重
            totalScore += (report.DimensionCheckResult.Issues.Count == 0 ? 100 : 70) * 0.2;
            weightCount++;
            
            // 文字检查权重
            totalScore += (report.TextCheckResult.Issues.Count == 0 ? 100 : 80) * 0.15;
            weightCount++;
            
            // 完整性检查权重
            totalScore += (report.IntegrityCheckResult.HasErrors ? 0 : 100) * 0.15;
            weightCount++;
            
            // 性能检查权重
            totalScore += report.PerformanceCheckResult.PerformanceScore * 0.1;
            weightCount++;
            
            return totalScore / weightCount;
        }
        
        private void DisplayQualityReport(Editor ed, QualityReport report)
        {
            ed.WriteMessage("\n=== 质量检查报告 ===");
            ed.WriteMessage($"\n检查时间：{report.CheckTime.TotalMilliseconds:F2}ms");
            ed.WriteMessage($"\n总体评分：{report.OverallScore:F1}分");
            
            // 图层检查结果
            ed.WriteMessage("\n--- 图层检查 ---");
            ed.WriteMessage($"\n问题数量：{report.LayerCheckResult.Issues.Count}");
            foreach (var issue in report.LayerCheckResult.Issues.Take(5))
            {
                ed.WriteMessage($"\n  {issue.LayerName}: {issue.IssueType}");
            }
            
            // 图块检查结果
            ed.WriteMessage("\n--- 图块检查 ---");
            ed.WriteMessage($"\n未使用块数：{report.BlockCheckResult.UnusedBlocks.Count}");
            ed.WriteMessage($"\n问题数量：{report.BlockCheckResult.Issues.Count}");
            
            // 标注检查结果
            ed.WriteMessage("\n--- 标注检查 ---");
            ed.WriteMessage($"\n问题数量：{report.DimensionCheckResult.Issues.Count}");
            
            // 文字检查结果
            ed.WriteMessage("\n--- 文字检查 ---");
            ed.WriteMessage($"\n问题数量：{report.TextCheckResult.Issues.Count}");
            
            // 完整性检查结果
            ed.WriteMessage("\n--- 完整性检查 ---");
            ed.WriteMessage($"\n清理对象数：{report.IntegrityCheckResult.PurgedObjectCount}");
            ed.WriteMessage($"\n文件大小：{report.IntegrityCheckResult.FileSizeMB:F2}MB");
            
            // 性能检查结果
            ed.WriteMessage("\n--- 性能检查 ---");
            ed.WriteMessage($"\n对象总数：{report.PerformanceCheckResult.ObjectCount}");
            ed.WriteMessage($"\n图层数量：{report.PerformanceCheckResult.LayerCount}");
            ed.WriteMessage($"\n性能评分：{report.PerformanceCheckResult.PerformanceScore:F1}分");
        }
        
        private void SaveQualityReport(QualityReport report, string documentName)
        {
            string fileName = $"QualityReport_{Path.GetFileNameWithoutExtension(documentName)}_{DateTime.Now:yyyyMMdd_HHmmss}.txt";
            string filePath = Path.Combine(
                Environment.GetFolderPath(Environment.SpecialFolder.Desktop),
                fileName);
            
            using (var writer = new StreamWriter(filePath))
            {
                writer.WriteLine("=== 质量检查报告 ===");
                writer.WriteLine($"文档名称：{documentName}");
                writer.WriteLine($"检查时间：{DateTime.Now:yyyy-MM-dd HH:mm:ss}");
                writer.WriteLine($"检查耗时：{report.CheckTime.TotalMilliseconds:F2}ms");
                writer.WriteLine($"总体评分：{report.OverallScore:F1}分");
                writer.WriteLine();
                
                // 详细结果
                WriteDetailedReport(writer, report);
            }
        }
        
        private void WriteDetailedReport(StreamWriter writer, QualityReport report)
        {
            // 图层检查详情
            writer.WriteLine("--- 图层检查详情 ---");
            foreach (var issue in report.LayerCheckResult.Issues)
            {
                writer.WriteLine($"图层：{issue.LayerName}, 问题：{issue.IssueType}, 严重程度：{issue.Severity}");
            }
            writer.WriteLine();
            
            // 图块检查详情
            writer.WriteLine("--- 图块检查详情 ---");
            writer.WriteLine("未使用的块：");
            foreach (var block in report.BlockCheckResult.UnusedBlocks)
            {
                writer.WriteLine($"  - {block}");
            }
            foreach (var issue in report.BlockCheckResult.Issues)
            {
                writer.WriteLine($"块：{issue.BlockName}, 问题：{issue.IssueType}, 严重程度：{issue.Severity}");
            }
            writer.WriteLine();
            
            // 标注检查详情
            writer.WriteLine("--- 标注检查详情 ---");
            foreach (var issue in report.DimensionCheckResult.Issues)
            {
                writer.WriteLine($"实体：{issue.EntityHandle}, 问题：{issue.IssueType}, 严重程度：{issue.Severity}");
            }
            writer.WriteLine();
            
            // 文字检查详情
            writer.WriteLine("--- 文字检查详情 ---");
            foreach (var issue in report.TextCheckResult.Issues)
            {
                writer.WriteLine($"实体：{issue.EntityHandle}, 问题：{issue.IssueType}, 严重程度：{issue.Severity}");
            }
        }
        
        private bool IsValidLayerName(string layerName)
        {
            if (string.IsNullOrEmpty(layerName) || layerName.Length > 255)
                return false;
            
            char[] invalidChars = { '<', '>', '/', '\\', ':', '*', '?', '"', '|', '=', '`' };
            return layerName.IndexOfAny(invalidChars) < 0;
        }
        
        private bool IsBlockUsed(Database db, string blockName)
        {
            using (Transaction trans = db.TransactionManager.StartTransaction())
            {
                BlockTable bt = trans.GetObject(db.BlockTableId, OpenMode.ForRead) as BlockTable;
                BlockTableRecord modelSpace = trans.GetObject(bt[BlockTableRecord.ModelSpace], OpenMode.ForRead) as BlockTableRecord;
                
                foreach (ObjectId objId in modelSpace)
                {
                    Entity ent = trans.GetObject(objId, OpenMode.ForRead) as Entity;
                    if (ent is BlockReference blockRef)
                    {
                        BlockTableRecord block = trans.GetObject(blockRef.BlockTableRecord, OpenMode.ForRead) as BlockTableRecord;
                        if (block.Name == blockName)
                            return true;
                    }
                }
                
                return false;
            }
        }
    }
    
    // 质量检查数据结构
    public class QualityReport
    {
        public TimeSpan CheckTime { get; set; }
        public double OverallScore { get; set; }
        public LayerCheckResult LayerCheckResult { get; set; }
        public BlockCheckResult BlockCheckResult { get; set; }
        public DimensionCheckResult DimensionCheckResult { get; set; }
        public TextCheckResult TextCheckResult { get; set; }
        public IntegrityCheckResult IntegrityCheckResult { get; set; }
        public PerformanceCheckResult PerformanceCheckResult { get; set; }
    }
    
    public class LayerCheckResult
    {
        public List<LayerIssue> Issues { get; set; } = new List<LayerIssue>();
    }
    
    public class BlockCheckResult
    {
        public List<string> UnusedBlocks { get; set; } = new List<string>();
        public List<BlockIssue> Issues { get; set; } = new List<BlockIssue>();
    }
    
    public class DimensionCheckResult
    {
        public List<DimensionIssue> Issues { get; set; } = new List<DimensionIssue>();
    }
    
    public class TextCheckResult
    {
        public List<TextIssue> Issues { get; set; } = new List<TextIssue>();
    }
    
    public class IntegrityCheckResult
    {
        public bool HasErrors { get; set; }
        public string ErrorMessage { get; set; }
        public int PurgedObjectCount { get; set; }
        public double FileSizeMB { get; set; }
        public int TotalObjectCount { get; set; }
    }
    
    public class PerformanceCheckResult
    {
        public int ObjectCount { get; set; }
        public int LayerCount { get; set; }
        public int BlockCount { get; set; }
        public int ComplexObjectCount { get; set; }
        public double PerformanceScore { get; set; }
    }
    
    public class LayerIssue
    {
        public string LayerName { get; set; }
        public string IssueType { get; set; }
        public IssueSeverity Severity { get; set; }
    }
    
    public class BlockIssue
    {
        public string BlockName { get; set; }
        public string IssueType { get; set; }
        public IssueSeverity Severity { get; set; }
    }
    
    public class DimensionIssue
    {
        public string EntityHandle { get; set; }
        public string IssueType { get; set; }
        public IssueSeverity Severity { get; set; }
    }
    
    public class TextIssue
    {
        public string EntityHandle { get; set; }
        public string IssueType { get; set; }
        public IssueSeverity Severity { get; set; }
    }
    
    public enum IssueSeverity
    {
        Info,
        Warning,
        Error,
        Critical
    }
}
```

---

**CURSOR提示词模板：**
```
请为AutoCAD项目实战提供完整的解决方案，包括：
1. 综合项目架构设计
2. 项目配置管理系统
3. 批量图纸处理
4. 质量控制和检查
5. 完整的项目工作流程
```