import { PluginConfig, PluginContext, PluginState, PluginEvent } from '../../types';
import { loadConfig, saveConfig } from '../../config';
import { createLogger } from '../../utils/logger';
import EventEmitter from 'events';

export interface ActivationEvent {
  type: 'activating' | 'activated' | 'deactivating' | 'deactivated' | 'error';
  timestamp: Date;
  error?: string;
}

export class PluginLifecycleManager extends EventEmitter {
  private config: PluginConfig;
  private state: PluginState;
  private logger = createLogger('PluginLifecycleManager');
  private activationPromise: Promise<void> | null = null;

  constructor() {
    super();
    this.config = loadConfig();
    this.state = {
      status: 'inactive'
    };
  }

  async activate(context?: Partial<PluginContext>): Promise<void> {
    if (this.activationPromise) {
      return this.activationPromise;
    }

    this.activationPromise = this.performActivation(context);
    return this.activationPromise;
  }

  private async performActivation(context?: Partial<PluginContext>): Promise<void> {
    try {
      this.updateState('activating');
      this.emitEvent('activating');

      this.logger.info('Starting plugin activation...');

      if (!this.config.enabled) {
        throw new Error('Plugin is disabled in configuration');
      }

      await this.validateDependencies();
      await this.initializeServices(context);
      await this.registerHooks();

      this.updateState('active');
      this.state.activationTime = new Date();
      
      this.emitEvent('activated');
      this.logger.info('Plugin activated successfully');

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      
      this.updateState('error', errorMessage);
      this.emitEvent('error', errorMessage);
      
      this.logger.error('Plugin activation failed:', error);
      throw error;
    } finally {
      this.activationPromise = null;
    }
  }

  async deactivate(): Promise<void> {
    try {
      if (this.state.status !== 'active') {
        this.logger.warn('Plugin is not active, skipping deactivation');
        return;
      }

      this.updateState('deactivating');
      this.emitEvent('deactivating');

      this.logger.info('Starting plugin deactivation...');

      await this.unregisterHooks();
      await this.cleanupServices();

      this.updateState('inactive');
      delete this.state.activationTime;
      
      this.emitEvent('deactivated');
      this.logger.info('Plugin deactivated successfully');

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      
      this.updateState('error', errorMessage);
      this.emitEvent('error', errorMessage);
      
      this.logger.error('Plugin deactivation failed:', error);
      throw error;
    }
  }

  async restart(context?: Partial<PluginContext>): Promise<void> {
    this.logger.info('Restarting plugin...');
    
    try {
      await this.deactivate();
      await this.activate(context);
      
      this.logger.info('Plugin restarted successfully');
    } catch (error) {
      this.logger.error('Plugin restart failed:', error);
      throw error;
    }
  }

  getState(): PluginState {
    return { ...this.state };
  }

  getConfig(): PluginConfig {
    return { ...this.config };
  }

  async updateConfig(newConfig: Partial<PluginConfig>): Promise<void> {
    try {
      this.config = { ...this.config, ...newConfig };
      await saveConfig(this.config);
      
      this.logger.info('Configuration updated');
      
      if (this.state.status === 'active' && !this.config.enabled) {
        await this.deactivate();
      } else if (this.state.status === 'inactive' && this.config.enabled) {
        await this.activate();
      }
    } catch (error) {
      this.logger.error('Failed to update configuration:', error);
      throw error;
    }
  }

  private async validateDependencies(): Promise<void> {
    const requiredDeps = ['axios', 'ws', 'sqlite3', 'winston'];
    
    for (const dep of requiredDeps) {
      try {
        require(dep);
      } catch (error) {
        throw new Error(`Missing required dependency: ${dep}`);
      }
    }
    
    this.logger.debug('All dependencies validated');
  }

  private async initializeServices(context?: Partial<PluginContext>): Promise<void> {
    this.logger.debug('Initializing services...');
    
    if (context?.workspace) {
      this.logger.info(`Initializing for workspace: ${context.workspace}`);
    }
    
    await this.initializeDirectories();
    await this.initializeDatabase();
    
    this.logger.debug('Services initialized');
  }

  private async initializeDirectories(): Promise<void> {
    const fs = require('fs');
    const path = require('path');
    
    const dirs = [
      path.join(process.cwd(), 'logs'),
      path.join(process.cwd(), 'data'),
      path.join(process.cwd(), 'temp')
    ];
    
    for (const dir of dirs) {
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
      }
    }
  }

  private async initializeDatabase(): Promise<void> {
    // Initialize SQLite database for plugin data
    const sqlite3 = require('sqlite3').verbose();
    const path = require('path');
    
    return new Promise<void>((resolve, reject) => {
      const dbPath = path.join(process.cwd(), 'data', 'plugin.db');
      const db = new sqlite3.Database(dbPath, (err: any) => {
        if (err) {
          reject(err);
        } else {
          db.close();
          resolve();
        }
      });
    });
  }

  private async registerHooks(): Promise<void> {
    this.logger.debug('Registering hooks...');
    
    // Register event listeners, context menus, etc.
    // This will be implemented based on Cursor's extension API
  }

  private async unregisterHooks(): Promise<void> {
    this.logger.debug('Unregistering hooks...');
    
    // Clean up registered hooks
  }

  private async cleanupServices(): Promise<void> {
    this.logger.debug('Cleaning up services...');
    
    // Clean up any running services, connections, etc.
  }

  private updateState(status: PluginState['status'], error?: string): void {
    this.state = {
      ...this.state,
      status,
      lastError: error
    };
  }

  private emitEvent(type: ActivationEvent['type'], error?: string): void {
    const event: ActivationEvent = {
      type,
      timestamp: new Date(),
      error
    };
    
    this.emit('activation-change', event);
  }

  isActive(): boolean {
    return this.state.status === 'active';
  }

  isActivating(): boolean {
    return this.state.status === 'activating';
  }

  getActivationTime(): Date | undefined {
    return this.state.activationTime;
  }

  getLastError(): string | undefined {
    return this.state.lastError;
  }
}