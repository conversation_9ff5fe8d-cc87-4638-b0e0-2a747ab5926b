# 最佳实践指南

## 概述

本文档提供了使用影刀RPA引擎的最佳实践建议，帮助您构建高效、可靠、可维护的RPA解决方案。

## 🏗️ 架构设计

### 1. 模块化设计

#### 好的做法 ✅
```javascript
// 将复杂流程拆分为多个子流程
const mainProcess = {
  name: "订单处理主流程",
  steps: [
    { type: "subprocess", name: "数据验证", process: "validate_order" },
    { type: "subprocess", name: "库存检查", process: "check_inventory" },
    { type: "subprocess", name: "支付处理", process: "process_payment" },
    { type: "subprocess", name: "物流安排", process: "arrange_shipping" }
  ]
};
```

#### 不好的做法 ❌
```javascript
// 将所有逻辑放在一个巨大的流程中
const monolithicProcess = {
  name: "订单处理",
  steps: [
    // 100+ 个步骤的复杂逻辑...
  ]
};
```

### 2. 配置驱动

#### 好的做法 ✅
```javascript
// 使用配置文件
const config = {
  endpoints: {
    orders: "https://api.example.com/orders",
    inventory: "https://inventory.example.com"
  },
  timeouts: {
    api: 30000,
    database: 10000
  },
  retryPolicy: {
    maxAttempts: 3,
    delay: 1000
  }
};

// 流程使用配置
const process = {
  name: "数据同步",
  variables: {
    apiEndpoint: config.endpoints.orders,
    timeout: config.timeouts.api
  }
};
```

#### 不好的做法 ❌
```javascript
// 硬编码配置
const process = {
  name: "数据同步",
  variables: {
    apiEndpoint: "https://api.example.com/orders", // 硬编码
    timeout: 30000 // 硬编码
  }
};
```

## 🔧 错误处理

### 1. 全面的错误处理

#### 好的做法 ✅
```javascript
async function robustProcessExecution(processId, parameters) {
  try {
    // 预验证
    await validateParameters(parameters);
    
    // 执行流程
    const execution = await executeProcess(processId, {
      parameters,
      timeout: 300000,
      retryCount: 3
    });
    
    // 监控执行
    const result = await monitorExecution(execution.executionId);
    
    return {
      success: true,
      result,
      executionId: execution.executionId
    };
    
  } catch (error) {
    // 错误分类和处理
    if (error.code === 'VALIDATION_ERROR') {
      return {
        success: false,
        error: '参数验证失败',
        details: error.message
      };
    } else if (error.code === 'TIMEOUT_ERROR') {
      return {
        success: false,
        error: '执行超时',
        action: 'retry_later'
      };
    } else {
      return {
        success: false,
        error: '未知错误',
        details: error.message
      };
    }
  }
}
```

#### 不好的做法 ❌
```javascript
// 简单的错误处理
async function simpleExecution(processId, parameters) {
  const result = await executeProcess(processId, parameters);
  return result; // 可能包含未处理的错误
}
```

### 2. 重试机制

#### 好的做法 ✅
```javascript
class RetryHandler {
  constructor(maxAttempts = 3, baseDelay = 1000) {
    this.maxAttempts = maxAttempts;
    this.baseDelay = baseDelay;
  }

  async execute(operation) {
    let lastError;
    
    for (let attempt = 1; attempt <= this.maxAttempts; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error;
        
        if (attempt === this.maxAttempts) {
          break;
        }
        
        // 指数退避
        const delay = this.baseDelay * Math.pow(2, attempt - 1);
        await this.sleep(delay);
      }
    }
    
    throw new Error(`操作失败，已重试 ${this.maxAttempts} 次: ${lastError.message}`);
  }

  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}
```

## 📊 监控和日志

### 1. 结构化日志

#### 好的做法 ✅
```javascript
class ProcessLogger {
  constructor(processId) {
    this.processId = processId;
  }

  log(level, message, data = {}) {
    const logEntry = {
      timestamp: new Date().toISOString(),
      processId: this.processId,
      level,
      message,
      data,
      correlationId: this.generateCorrelationId()
    };
    
    // 发送到日志系统
    console.log(JSON.stringify(logEntry));
  }

  info(message, data) {
    this.log('info', message, data);
  }

  warn(message, data) {
    this.log('warn', message, data);
  }

  error(message, error, data) {
    this.log('error', message, {
      ...data,
      error: {
        message: error.message,
        stack: error.stack,
        code: error.code
      }
    });
  }

  generateCorrelationId() {
    return `corr_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}
```

#### 不好的做法 ❌
```javascript
// 非结构化日志
console.log("Processing started");
console.log("Error occurred");
console.log("Processing finished");
```

### 2. 性能监控

#### 好的做法 ✅
```javascript
class PerformanceMonitor {
  constructor() {
    this.metrics = new Map();
  }

  startTimer(operation) {
    const key = `${operation}_${Date.now()}`;
    this.metrics.set(key, {
      startTime: Date.now(),
      operation
    });
    return key;
  }

  endTimer(timerKey) {
    const metric = this.metrics.get(timerKey);
    if (metric) {
      const duration = Date.now() - metric.startTime;
      this.metrics.delete(timerKey);
      
      // 记录性能指标
      this.recordMetric({
        operation: metric.operation,
        duration,
        timestamp: new Date().toISOString()
      });
      
      return duration;
    }
    return 0;
  }

  recordMetric(metric) {
    // 发送到监控系统
    console.log(`METRIC: ${JSON.stringify(metric)}`);
  }
}

// 使用示例
const monitor = new PerformanceMonitor();
const timer = monitor.startTimer('process_execution');

// 执行操作
await executeProcess(processId, parameters);

const duration = monitor.endTimer(timer);
console.log(`执行耗时: ${duration}ms`);
```

## 🔐 安全性

### 1. 敏感数据保护

#### 好的做法 ✅
```javascript
class SecureConfigManager {
  constructor() {
    this.secrets = new Map();
  }

  async loadSecrets() {
    // 从安全存储加载密钥
    const apiKey = await this.getFromVault('api_key');
    const dbPassword = await this.getFromVault('db_password');
    
    this.secrets.set('API_KEY', apiKey);
    this.secrets.set('DB_PASSWORD', dbPassword);
  }

  async getFromVault(secretKey) {
    // 实现从密钥管理系统获取密钥
    // 这里使用环境变量作为示例
    return process.env[secretKey];
  }

  getSecret(key) {
    return this.secrets.get(key);
  }

  // 数据脱敏
  sanitizeData(data) {
    const sensitiveFields = ['password', 'apiKey', 'token', 'secret'];
    const sanitized = { ...data };
    
    sensitiveFields.forEach(field => {
      if (sanitized[field]) {
        sanitized[field] = '***masked***';
      }
    });
    
    return sanitized;
  }
}
```

#### 不好的做法 ❌
```javascript
// 硬编码敏感信息
const config = {
  apiKey: "sk-1234567890abcdef", // 硬编码API密钥
  database: {
    password: "plain_text_password" // 硬编码密码
  }
};
```

### 2. 输入验证

#### 好的做法 ✅
```javascript
class InputValidator {
  static validateProcessInput(input) {
    const errors = [];
    
    // 验证必需字段
    if (!input.name || typeof input.name !== 'string') {
      errors.push('流程名称是必需的字符串');
    }
    
    if (input.name && input.name.length > 100) {
      errors.push('流程名称不能超过100个字符');
    }
    
    // 验证变量类型
    if (input.variables) {
      if (typeof input.variables !== 'object') {
        errors.push('变量必须是对象');
      } else {
        // 验证变量值
        Object.entries(input.variables).forEach(([key, value]) => {
          if (typeof value === 'object' && value !== null) {
            errors.push(`变量 '${key}' 不支持嵌套对象`);
          }
        });
      }
    }
    
    return {
      isValid: errors.length === 0,
      errors
    };
  }

  static sanitizeInput(input) {
    // 移除潜在的危险字符
    const sanitize = (str) => {
      if (typeof str !== 'string') return str;
      return str.replace(/[<>\"']/g, '');
    };
    
    const sanitized = { ...input };
    
    if (sanitized.name) {
      sanitized.name = sanitize(sanitized.name);
    }
    
    if (sanitized.description) {
      sanitized.description = sanitize(sanitized.description);
    }
    
    return sanitized;
  }
}
```

## 🚀 性能优化

### 1. 批量处理

#### 好的做法 ✅
```javascript
class BatchProcessor {
  constructor(batchSize = 10) {
    this.batchSize = batchSize;
  }

  async processItems(items, processFunction) {
    const results = [];
    
    for (let i = 0; i < items.length; i += this.batchSize) {
      const batch = items.slice(i, i + this.batchSize);
      
      try {
        const batchResults = await Promise.all(
          batch.map(item => processFunction(item))
        );
        
        results.push(...batchResults);
        
        // 批次间延迟，避免过载
        if (i + this.batchSize < items.length) {
          await this.delay(100);
        }
        
      } catch (error) {
        console.error(`批次 ${Math.floor(i / this.batchSize) + 1} 处理失败:`, error);
        // 继续处理下一批次
      }
    }
    
    return results;
  }

  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}
```

### 2. 缓存策略

#### 好的做法 ✅
```javascript
class CacheManager {
  constructor(ttl = 300000) { // 5分钟TTL
    this.cache = new Map();
    this.ttl = ttl;
  }

  set(key, value) {
    const expiry = Date.now() + this.ttl;
    this.cache.set(key, { value, expiry });
  }

  get(key) {
    const item = this.cache.get(key);
    
    if (!item) {
      return null;
    }
    
    if (Date.now() > item.expiry) {
      this.cache.delete(key);
      return null;
    }
    
    return item.value;
  }

  has(key) {
    return this.get(key) !== null;
  }

  clear() {
    this.cache.clear();
  }

  // 定期清理过期缓存
  startCleanup(interval = 60000) {
    setInterval(() => {
      const now = Date.now();
      for (const [key, item] of this.cache.entries()) {
        if (now > item.expiry) {
          this.cache.delete(key);
        }
      }
    }, interval);
  }
}
```

## 🧪 测试策略

### 1. 单元测试

#### 好的做法 ✅
```javascript
// process.test.js
const { createProcess, executeProcess } = require('./rpa-client');

describe('RPA Process Management', () => {
  let client;
  let testProcessId;

  beforeAll(async () => {
    client = new RPAEngineClient();
  });

  afterAll(async () => {
    // 清理测试数据
    if (testProcessId) {
      await client.deleteProcess(testProcessId);
    }
  });

  test('should create process successfully', async () => {
    const process = await client.createProcess({
      name: 'Test Process',
      description: 'Test process for unit testing',
      variables: { testVar: 'testValue' }
    });

    expect(process.id).toBeDefined();
    expect(process.name).toBe('Test Process');
    expect(process.status).toBe('idle');
    
    testProcessId = process.id;
  });

  test('should execute process and complete successfully', async () => {
    const execution = await client.executeProcess(testProcessId, {
      parameters: { testData: 'unit test' }
    });

    expect(execution.executionId).toBeDefined();

    // 监控执行完成
    const result = await monitorExecution(client, execution.executionId);
    expect(result.status).toBe('completed');
  });

  test('should handle invalid process ID gracefully', async () => {
    await expect(client.getProcess('invalid-id')).rejects.toThrow();
  });
});
```

### 2. 集成测试

#### 好的做法 ✅
```javascript
// integration.test.js
describe('RPA Engine Integration Tests', () => {
  beforeAll(async () => {
    // 确保引擎服务运行
    await ensureEngineRunning();
  });

  test('end-to-end process workflow', async () => {
    const client = new RPAEngineClient();
    
    // 1. 创建流程
    const process = await client.createProcess({
      name: 'Integration Test Process',
      variables: { input: 'test data' }
    });

    // 2. 执行流程
    const execution = await client.executeProcess(process.id, {
      parameters: { action: 'test' }
    });

    // 3. 监控执行
    const finalStatus = await waitForExecutionCompletion(client, execution.executionId);
    
    // 4. 验证结果
    expect(finalStatus.status).toBe('completed');
    
    // 5. 清理
    await client.deleteProcess(process.id);
  });

  test('concurrent execution handling', async () => {
    const client = new RPAEngineClient();
    
    // 创建多个流程
    const processes = await Promise.all([
      client.createProcess({ name: 'Concurrent Test 1' }),
      client.createProcess({ name: 'Concurrent Test 2' }),
      client.createProcess({ name: 'Concurrent Test 3' })
    ]);

    // 并发执行
    const executions = await Promise.all(
      processes.map(p => client.executeProcess(p.id))
    );

    // 等待所有执行完成
    const results = await Promise.all(
      executions.map(e => waitForExecutionCompletion(client, e.executionId))
    );

    // 验证所有执行都成功
    results.forEach(result => {
      expect(['completed', 'error']).toContain(result.status);
    });

    // 清理
    await Promise.all(processes.map(p => client.deleteProcess(p.id)));
  });
});
```

## 📋 部署最佳实践

### 1. 容器化部署

#### Dockerfile 示例
```dockerfile
FROM node:18-alpine

# 设置工作目录
WORKDIR /app

# 复制依赖文件
COPY package*.json ./

# 安装依赖
RUN npm ci --only=production

# 复制应用代码
COPY . .

# 创建日志目录
RUN mkdir -p logs && chown -R node:node /app

# 切换用户
USER node

# 暴露端口
EXPOSE 8080

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:8080/health || exit 1

# 启动命令
CMD ["npm", "start"]
```

### 2. 环境配置

#### docker-compose.yml 示例
```yaml
version: '3.8'

services:
  rpa-engine:
    build: .
    ports:
      - "8080:8080"
    environment:
      - NODE_ENV=production
      - RPA_ENGINE_PORT=8080
      - LOG_LEVEL=info
    volumes:
      - ./logs:/app/logs
      - ./data:/app/data
    restart: unless-stopped
    depends_on:
      - redis
      - postgres

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    restart: unless-stopped

  postgres:
    image: postgres:15-alpine
    environment:
      POSTGRES_DB: rpa_engine
      POSTGRES_USER: rpa_user
      POSTGRES_PASSWORD: secure_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    restart: unless-stopped

volumes:
  postgres_data:
```

## 🎯 总结

### 关键原则

1. **模块化**: 将复杂流程拆分为可重用的组件
2. **配置驱动**: 使用外部配置，避免硬编码
3. **错误处理**: 实现全面的错误处理和重试机制
4. **监控日志**: 使用结构化日志和性能监控
5. **安全第一**: 保护敏感数据，验证输入
6. **性能优化**: 使用批量处理和缓存策略
7. **测试覆盖**: 编写单元测试和集成测试
8. **部署规范**: 使用容器化和环境配置

### 检查清单

- [ ] 所有敏感数据都已加密或从密钥管理系统获取
- [ ] 实现了适当的错误处理和重试机制
- [ ] 使用结构化日志记录重要事件
- [ ] 添加了性能监控和指标收集
- [ ] 实现了输入验证和数据清理
- [ ] 编写了全面的单元测试和集成测试
- [ ] 使用配置文件管理环境特定设置
- [ ] 实现了适当的缓存策略
- [ ] 添加了健康检查和监控端点
- [ ] 文档化API和配置选项

遵循这些最佳实践将帮助您构建可靠、安全、高性能的RPA解决方案。