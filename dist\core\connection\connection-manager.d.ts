import { RPAEngine } from '../../types';
import { RPAConnection } from './engine-detector';
import EventEmitter from 'events';
export interface ConnectionEvent {
    type: 'connected' | 'disconnected' | 'error';
    engineId: string;
    error?: string;
}
export declare class RPAConnectionManager extends EventEmitter {
    private connections;
    private engines;
    private detector;
    private logger;
    private reconnectTimers;
    constructor();
    initialize(): Promise<void>;
    addEngine(engine: RPAEngine): Promise<void>;
    removeEngine(engineId: string): Promise<void>;
    getConnection(engineId: string): RPAConnection | undefined;
    getEngine(engineId: string): RPAEngine | undefined;
    getAllEngines(): RPAEngine[];
    getAvailableEngines(): RPAEngine[];
    testEngine(engineId: string): Promise<boolean>;
    refreshEngines(): Promise<void>;
    private scheduleReconnect;
    private clearReconnectTimer;
    dispose(): void;
}
//# sourceMappingURL=connection-manager.d.ts.map