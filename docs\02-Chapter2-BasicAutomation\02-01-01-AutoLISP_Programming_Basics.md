# AutoLISP编程基础

## 2.1.1 LISP语言语法基础

**基本语法结构：**
```lisp
; 注释以分号开头
; 函数定义语法：(defun 函数名 (参数) 函数体)

(defun hello-world ()
  (princ "Hello, AutoCAD!")
  (princ) ; 清空命令行
)

; 变量赋值
(setq x 10)
(setq y 20.5)
(setq text "Hello World")

; 列表操作
(setq point1 (list 0 0 0))
(setq point2 (list 100 100 0))

; 条件语句
(if (> x 5)
  (princ "x大于5")
  (princ "x小于等于5")
)

; 循环语句
(repeat 5
  (princ "循环次数")
)
```

**数据类型：**
- **整数**：10, -5, 100
- **实数**：3.14, -2.5, 0.001
- **字符串**："Hello", "AutoCAD"
- **列表**：(0 0 0), ("Layer1" "Layer2")
- **符号**：x, y, point1

## 2.1.2 AutoLISP函数库

**数学函数：**
```lisp
(+ 10 20 30)    ; 加法，返回60
(- 100 50)      ; 减法，返回50
(* 3 4)         ; 乘法，返回12
(/ 100 4)       ; 除法，返回25
(sqrt 16)       ; 平方根，返回4.0
(sin 1.57)      ; 正弦函数
(cos 0)         ; 余弦函数
(atan 1)        ; 反正切函数
```

**几何函数：**
```lisp
(distance p1 p2)          ; 计算两点距离
(angle p1 p2)            ; 计算两点角度
(polar p1 angle distance) ; 极坐标计算
(intersectWith obj1 obj2) ; 求交点
```

**实体操作函数：**
```lisp
(entlast)                ; 获取最后一个实体
(entget ename)           ; 获取实体数据
(entmod elist)           ; 修改实体数据
(entdel ename)           ; 删除实体
```

**用户输入函数：**
```lisp
(getpoint [pt] [msg])    ; 获取点
(getdist [pt] [msg])     ; 获取距离
(getangle [pt] [msg])    ; 获取角度
(getint [msg])           ; 获取整数
(getreal [msg])          ; 获取实数
(getstring [cr] [msg])   ; 获取字符串
(getkword [msg])         ; 获取关键字
```

## 2.1.3 图形对象操作

**创建基本图形：**
```lisp
; 绘制直线
(defun draw-line (p1 p2)
  (command "LINE" p1 p2 "")
)

; 绘制圆
(defun draw-circle (center radius)
  (command "CIRCLE" center radius)
)

; 绘制矩形
(defun draw-rectangle (p1 p2)
  (command "RECTANG" p1 p2)
)

; 绘制多段线
(defun draw-polyline (points)
  (command "PLINE")
  (foreach pt points
    (command pt)
  )
  (command "")
)
```

**修改图形对象：**
```lisp
; 移动对象
(defun move-object (obj from to)
  (command "MOVE" obj "" from to)
)

; 复制对象
(defun copy-object (obj from to)
  (command "COPY" obj "" from to)
)

; 旋转对象
(defun rotate-object (obj base angle)
  (command "ROTATE" obj "" base angle)
)

; 缩放对象
(defun scale-object (obj base factor)
  (command "SCALE" obj "" base factor)
)
```

## 2.1.4 实例：简单绘图自动化

**完整示例：自动绘制建筑平面图**
```lisp
(defun draw-building-outline (/ width length wall-thickness)
  ; 设置变量
  (setq width 10000)
  (setq length 8000)
  (setq wall-thickness 200)
  
  ; 设置当前图层
  (command "LAYER" "M" "WALLS" "C" "1" "" "")
  
  ; 绘制外墙
  (command "RECTANG" (list 0 0) (list width length))
  
  ; 绘制内墙
  (command "LAYER" "M" "INNER_WALLS" "C" "2" "" "")
  (command "LINE" (list wall-thickness wall-thickness) 
          (list (- width wall-thickness) wall-thickness) "")
  (command "LINE" (list wall-thickness wall-thickness) 
          (list wall-thickness (- length wall-thickness)) "")
  
  ; 绘制门窗
  (command "LAYER" "M" "DOORS" "C" "3" "" "")
  (command "RECTANG" (list 2000 0) (list 2800 wall-thickness))
  
  (command "LAYER" "M" "WINDOWS" "C" "4" "" "")
  (command "RECTANG" (list 5000 0) (list 6000 wall-thickness))
  
  ; 添加标注
  (command "LAYER" "M" "DIMENSIONS" "C" "5" "" "")
  (command "DIMLINEAR" (list 0 -500) (list width -500) 
          (list (/ width 2) -1000))
  
  (princ "建筑平面图绘制完成")
  (princ)
)

; 调用函数
(draw-building-outline)
```

**CURSOR提示词模板：**
```
请生成一个AutoLISP脚本，实现[具体功能]，要求：
1. 使用AutoLISP基本语法
2. 包含错误处理
3. 添加用户交互
4. 符合AutoCAD绘图标准
```