"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.engineServiceManager = exports.EngineServiceManager = void 0;
const yingdao_server_1 = require("./services/yingdao-server");
const logger_1 = require("../utils/logger");
class EngineServiceManager {
    constructor(port = 8080) {
        this.logger = (0, logger_1.createLogger)('EngineServiceManager');
        this.isRunning = false;
        this.server = new yingdao_server_1.YingdaoRPAServer(port);
    }
    async start() {
        if (this.isRunning) {
            this.logger.warn('Engine service is already running');
            return;
        }
        try {
            await this.server.start();
            this.isRunning = true;
            this.logger.info(`Engine service started on port ${this.server.getPort()}`);
        }
        catch (error) {
            this.logger.error('Failed to start engine service:', error);
            throw error;
        }
    }
    async stop() {
        if (!this.isRunning) {
            this.logger.warn('Engine service is not running');
            return;
        }
        try {
            await this.server.stop();
            this.isRunning = false;
            this.logger.info('Engine service stopped');
        }
        catch (error) {
            this.logger.error('Failed to stop engine service:', error);
            throw error;
        }
    }
    isServiceRunning() {
        return this.isRunning;
    }
    getServer() {
        return this.server;
    }
    getPort() {
        return this.server.getPort();
    }
}
exports.EngineServiceManager = EngineServiceManager;
exports.engineServiceManager = new EngineServiceManager();
//# sourceMappingURL=index.js.map