Option Explicit

Sub PrintAllPDFsInCurrentFolder()
    Dim folderPath As String
    Dim fileName As String
    Dim pdfCount As Integer
    Dim response As Integer
    
    On Error GoTo ErrorHandler
    
    ' 获取当前文件夹路径
    folderPath = ThisWorkbook.Path
    If folderPath = "" Then
        MsgBox "请先保存工作簿！", vbExclamation, "错误"
        Exit Sub
    End If
    
    ' 确保路径以反斜杠结尾
    If Right(folderPath, 1) <> "\" Then
        folderPath = folderPath & "\"
    End If
    
    ' 查找PDF文件
    fileName = Dir(folderPath & "*.pdf")
    pdfCount = 0
    
    If fileName = "" Then
        MsgBox "当前文件夹中没有找到PDF文件！", vbInformation, "提示"
        Exit Sub
    End If
    
    ' 询问用户是否继续
    response = MsgBox("找到PDF文件，准备打印。是否继续？", vbQuestion + vbYesNo, "确认打印")
    If response = vbNo Then
        Exit Sub
    End If
    
    ' 打印所有PDF文件
    Application.ScreenUpdating = False
    Application.DisplayAlerts = False
    
    Do While fileName <> ""
        pdfCount = pdfCount + 1
        Call PrintPDFFile(folderPath & fileName)
        fileName = Dir()
    Loop
    
    Application.ScreenUpdating = True
    Application.DisplayAlerts = True
    
    MsgBox "成功打印了 " & pdfCount & " 个PDF文件！", vbInformation, "完成"
    
    Exit Sub
    
ErrorHandler:
    Application.ScreenUpdating = True
    Application.DisplayAlerts = True
    MsgBox "发生错误：" & Err.Description, vbCritical, "错误"
End Sub

Private Sub PrintPDFFile(filePath As String)
    Dim shellApp As Object
    Dim folder As Object
    Dim file As Object
    
    On Error Resume Next
    
    ' 使用Shell.Application打印PDF文件
    Set shellApp = CreateObject("Shell.Application")
    Set folder = shellApp.NameSpace(Left(filePath, InStrRev(filePath, "\") - 1))
    Set file = folder.ParseName(Mid(filePath, InStrRev(filePath, "\") + 1))
    
    ' 右键菜单中的"打印"命令
    file.InvokeVerb ("print")
    
    ' 等待打印开始
    Application.Wait Now + TimeValue("00:00:02")
    
    Set file = Nothing
    Set folder = Nothing
    Set shellApp = Nothing
    
    On Error GoTo 0
End Sub

Sub PrintAllPDFsWithRS()
    ' 使用Remote Scripting的替代方法
    Dim folderPath As String
    Dim fso As Object
    Dim folder As Object
    Dim files As Object
    Dim file As Object
    Dim pdfCount As Integer
    Dim response As Integer
    
    On Error GoTo ErrorHandler
    
    ' 获取当前文件夹路径
    folderPath = ThisWorkbook.Path
    If folderPath = "" Then
        MsgBox "请先保存工作簿！", vbExclamation, "错误"
        Exit Sub
    End If
    
    ' 创建FileSystemObject
    Set fso = CreateObject("Scripting.FileSystemObject")
    Set folder = fso.GetFolder(folderPath)
    Set files = folder.Files
    
    ' 统计PDF文件数量
    pdfCount = 0
    For Each file In files
        If LCase(fso.GetExtensionName(file.Name)) = "pdf" Then
            pdfCount = pdfCount + 1
        End If
    Next file
    
    If pdfCount = 0 Then
        MsgBox "当前文件夹中没有找到PDF文件！", vbInformation, "提示"
        Exit Sub
    End If
    
    ' 询问用户是否继续
    response = MsgBox("找到 " & pdfCount & " 个PDF文件，准备打印。是否继续？", vbQuestion + vbYesNo, "确认打印")
    If response = vbNo Then
        Exit Sub
    End If
    
    ' 打印所有PDF文件
    Application.ScreenUpdating = False
    Application.DisplayAlerts = False
    
    pdfCount = 0
    For Each file In files
        If LCase(fso.GetExtensionName(file.Name)) = "pdf" Then
            pdfCount = pdfCount + 1
            Call PrintPDFWithShell(file.Path)
            ' 添加延迟以避免打印机队列过载
            Application.Wait Now + TimeValue("00:00:03")
        End If
    Next file
    
    Application.ScreenUpdating = True
    Application.DisplayAlerts = True
    
    MsgBox "成功打印了 " & pdfCount & " 个PDF文件！", vbInformation, "完成"
    
    Set file = Nothing
    Set files = Nothing
    Set folder = Nothing
    Set fso = Nothing
    
    Exit Sub
    
ErrorHandler:
    Application.ScreenUpdating = True
    Application.DisplayAlerts = True
    MsgBox "发生错误：" & Err.Description, vbCritical, "错误"
End Sub

Private Sub PrintPDFWithShell(filePath As String)
    Dim wsh As Object
    
    On Error Resume Next
    
    ' 使用WScript.Shell打印PDF文件
    Set wsh = CreateObject("WScript.Shell")
    wsh.Run """C:\Program Files\Adobe\Acrobat Reader\AcroRd32.exe"" /p /h """ & filePath & """", 0, False
    
    ' 等待打印开始
    Application.Wait Now + TimeValue("00:00:02")
    
    Set wsh = Nothing
    
    On Error GoTo 0
End Sub