"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.rpaEngine = exports.RPAEngine = void 0;
const connection_1 = require("./connection");
const execution_1 = require("./execution");
const logger_1 = require("../utils/logger");
class RPAEngine {
    constructor() {
        this.logger = (0, logger_1.createLogger)('RPAEngine');
        this.initialized = false;
        this.connectionManager = new connection_1.RPAConnectionManager();
        this.processManager = new execution_1.RPAProcessManager(this.connectionManager);
    }
    async initialize() {
        if (this.initialized) {
            return;
        }
        try {
            this.logger.info('Initializing RPA Engine...');
            await this.connectionManager.initialize();
            this.initialized = true;
            this.logger.info('RPA Engine initialized successfully');
        }
        catch (error) {
            this.logger.error('Failed to initialize RPA Engine:', error);
            throw error;
        }
    }
    async shutdown() {
        try {
            this.logger.info('Shutting down RPA Engine...');
            this.connectionManager.dispose();
            this.initialized = false;
            this.logger.info('RPA Engine shutdown completed');
        }
        catch (error) {
            this.logger.error('Failed to shutdown RPA Engine:', error);
            throw error;
        }
    }
    getConnectionManager() {
        return this.connectionManager;
    }
    getProcessManager() {
        return this.processManager;
    }
    isInitialized() {
        return this.initialized;
    }
}
exports.RPAEngine = RPAEngine;
exports.rpaEngine = new RPAEngine();
//# sourceMappingURL=index.js.map