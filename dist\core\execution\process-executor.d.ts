import { RPAProcess, RPAExecutionConfig, RPAExecutionResult, RPAProcessLog } from '../../types';
import EventEmitter from 'events';
export interface ExecutionEvent {
    type: 'started' | 'progress' | 'completed' | 'error' | 'cancelled';
    processId: string;
    executionId: string;
    timestamp: Date;
    data?: any;
    error?: string;
}
export interface ExecutionProgress {
    currentStep: number;
    totalSteps: number;
    percentage: number;
    currentActivity: string;
    estimatedTimeRemaining?: number;
}
export declare class RPAProcessExecutor extends EventEmitter {
    private logger;
    private activeExecutions;
    private executionHistory;
    constructor();
    executeProcess(config: RPAExecutionConfig, process: RPAProcess): Promise<RPAExecutionResult>;
    private runExecution;
    private executeStep;
    private executeInitializeStep;
    private executeProcessStep;
    private executeValidateStep;
    private executeFinalizeStep;
    cancelExecution(executionId: string): Promise<boolean>;
    getActiveExecutions(): ExecutionSession[];
    getExecutionHistory(processId?: string): ExecutionRecord[];
    getExecutionStatus(executionId: string): ExecutionSession | null;
    private generateExecutionId;
    private generateLogId;
    private estimateTotalSteps;
    private getExecutionSteps;
    private generateExecutionOutput;
    private addLog;
    private delay;
}
interface ExecutionSession {
    id: string;
    processId: string;
    config: RPAExecutionConfig;
    startTime: Date;
    endTime?: Date;
    status: 'running' | 'completed' | 'error' | 'cancelled';
    logs: RPAProcessLog[];
    progress: ExecutionProgress;
    error?: string;
}
interface ExecutionRecord {
    id: string;
    processId: string;
    startTime: Date;
    endTime?: Date;
    status: 'completed' | 'error' | 'cancelled';
    config: RPAExecutionConfig;
    logs: RPAProcessLog[];
}
export {};
//# sourceMappingURL=process-executor.d.ts.map