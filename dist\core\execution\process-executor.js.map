{"version": 3, "file": "process-executor.js", "sourceRoot": "", "sources": ["../../../src/core/execution/process-executor.ts"], "names": [], "mappings": ";;;;;;AAMA,uCAAsD;AACtD,+CAAkD;AAClD,oDAAkC;AAmBlC,MAAa,kBAAmB,SAAQ,gBAAY;IAKlD;QACE,KAAK,EAAE,CAAC;QALF,WAAM,GAAG,IAAA,qBAAY,EAAC,oBAAoB,CAAC,CAAC;QAC5C,qBAAgB,GAAkC,IAAI,GAAG,EAAE,CAAC;QAC5D,qBAAgB,GAAsB,EAAE,CAAC;IAIjD,CAAC;IAED,KAAK,CAAC,cAAc,CAClB,MAA0B,EAC1B,OAAmB;QAEnB,IAAI,CAAC;YACH,IAAA,+BAAuB,EAAC,MAAM,CAAC,CAAC;YAEhC,MAAM,WAAW,GAAG,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAC/C,MAAM,OAAO,GAAqB;gBAChC,EAAE,EAAE,WAAW;gBACf,SAAS,EAAE,OAAO,CAAC,EAAE;gBACrB,MAAM;gBACN,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,MAAM,EAAE,SAAS;gBACjB,IAAI,EAAE,EAAE;gBACR,QAAQ,EAAE;oBACR,WAAW,EAAE,CAAC;oBACd,UAAU,EAAE,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC;oBAC5C,UAAU,EAAE,CAAC;oBACb,eAAe,EAAE,iBAAiB;iBACnC;aACF,CAAC;YAEF,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC;YAEhD,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;gBACrB,IAAI,EAAE,SAAS;gBACf,SAAS,EAAE,OAAO,CAAC,EAAE;gBACrB,WAAW;gBACX,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC,CAAC;YAEH,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,sBAAsB,WAAW,gBAAgB,OAAO,CAAC,EAAE,EAAE,CAAC,CAAC;YAEhF,gCAAgC;YAChC,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;gBAChD,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,aAAa,WAAW,UAAU,EAAE,KAAK,CAAC,CAAC;YAC/D,CAAC,CAAC,CAAC;YAEH,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,aAAa,EAAE,CAAC;gBAChB,IAAI,EAAE,EAAE;aACT,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAE5E,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;gBACrB,IAAI,EAAE,OAAO;gBACb,SAAS,EAAE,OAAO,CAAC,EAAE;gBACrB,WAAW,EAAE,EAAE;gBACf,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,KAAK,EAAE,YAAY;aACpB,CAAC,CAAC;YAEH,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;YAEtD,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,YAAY;gBACnB,aAAa,EAAE,CAAC;gBAChB,IAAI,EAAE,EAAE;aACT,CAAC;QACJ,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,YAAY,CAAC,OAAyB,EAAE,OAAmB;QACvE,IAAI,CAAC;YACH,wBAAwB;YACxB,OAAO,CAAC,MAAM,GAAG,SAAS,CAAC;YAC3B,OAAO,CAAC,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC;YAE/B,2BAA2B;YAC3B,MAAM,KAAK,GAAG,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;YAE9C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;gBACtC,MAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;gBAEtB,mCAAmC;gBACnC,IAAI,OAAO,CAAC,MAAM,KAAK,WAAW,EAAE,CAAC;oBACnC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,aAAa,OAAO,CAAC,EAAE,gBAAgB,CAAC,CAAC;oBAC1D,MAAM;gBACR,CAAC;gBAED,kBAAkB;gBAClB,OAAO,CAAC,QAAQ,CAAC,WAAW,GAAG,CAAC,GAAG,CAAC,CAAC;gBACrC,OAAO,CAAC,QAAQ,CAAC,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC,GAAG,GAAG,CAAC,CAAC;gBACzE,OAAO,CAAC,QAAQ,CAAC,eAAe,GAAG,IAAI,CAAC,WAAW,CAAC;gBAEpD,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;oBACrB,IAAI,EAAE,UAAU;oBAChB,SAAS,EAAE,OAAO,CAAC,EAAE;oBACrB,WAAW,EAAE,OAAO,CAAC,EAAE;oBACvB,SAAS,EAAE,IAAI,IAAI,EAAE;oBACrB,IAAI,EAAE,EAAE,QAAQ,EAAE,OAAO,CAAC,QAAQ,EAAE;iBACrC,CAAC,CAAC;gBAEH,eAAe;gBACf,MAAM,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;gBAE/C,gBAAgB;gBAChB,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE;oBACnB,EAAE,EAAE,IAAI,CAAC,aAAa,EAAE;oBACxB,SAAS,EAAE,IAAI,IAAI,EAAE;oBACrB,KAAK,EAAE,MAAM;oBACb,OAAO,EAAE,QAAQ,CAAC,GAAG,CAAC,eAAe,IAAI,CAAC,WAAW,EAAE;iBACxD,CAAC,CAAC;gBAEH,2BAA2B;gBAC3B,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YACzB,CAAC;YAED,qBAAqB;YACrB,OAAO,CAAC,MAAM,GAAG,WAAW,CAAC;YAC7B,OAAO,CAAC,OAAO,GAAG,IAAI,IAAI,EAAE,CAAC;YAE7B,OAAO,CAAC,MAAM,GAAG,WAAW,CAAC;YAC7B,OAAO,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;YAElC,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;gBACrB,IAAI,EAAE,WAAW;gBACjB,SAAS,EAAE,OAAO,CAAC,EAAE;gBACrB,WAAW,EAAE,OAAO,CAAC,EAAE;gBACvB,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,IAAI,EAAE;oBACJ,aAAa,EAAE,OAAO,CAAC,OAAO,CAAC,OAAO,EAAE,GAAG,OAAO,CAAC,SAAS,CAAC,OAAO,EAAE;oBACtE,MAAM,EAAE,IAAI,CAAC,uBAAuB,CAAC,OAAO,CAAC;iBAC9C;aACF,CAAC,CAAC;YAEH,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,aAAa,OAAO,CAAC,EAAE,yBAAyB,CAAC,CAAC;QAErE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAE5E,OAAO,CAAC,MAAM,GAAG,OAAO,CAAC;YACzB,OAAO,CAAC,OAAO,GAAG,IAAI,IAAI,EAAE,CAAC;YAC7B,OAAO,CAAC,KAAK,GAAG,YAAY,CAAC;YAE7B,OAAO,CAAC,MAAM,GAAG,OAAO,CAAC;YACzB,OAAO,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;YAElC,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE;gBACnB,EAAE,EAAE,IAAI,CAAC,aAAa,EAAE;gBACxB,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,KAAK,EAAE,OAAO;gBACd,OAAO,EAAE,qBAAqB,YAAY,EAAE;gBAC5C,OAAO,EAAE,KAAK;aACf,CAAC,CAAC;YAEH,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;gBACrB,IAAI,EAAE,OAAO;gBACb,SAAS,EAAE,OAAO,CAAC,EAAE;gBACrB,WAAW,EAAE,OAAO,CAAC,EAAE;gBACvB,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,KAAK,EAAE,YAAY;aACpB,CAAC,CAAC;YAEH,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,aAAa,OAAO,CAAC,EAAE,UAAU,EAAE,KAAK,CAAC,CAAC;QAC9D,CAAC;gBAAS,CAAC;YACT,kBAAkB;YAClB,IAAI,OAAO,CAAC,MAAM,KAAK,SAAS,EAAE,CAAC;gBACjC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC;oBACzB,EAAE,EAAE,OAAO,CAAC,EAAE;oBACd,SAAS,EAAE,OAAO,CAAC,EAAE;oBACrB,SAAS,EAAE,OAAO,CAAC,SAAS;oBAC5B,OAAO,EAAE,OAAO,CAAC,OAAO;oBACxB,MAAM,EAAE,OAAO,CAAC,MAAM;oBACtB,MAAM,EAAE,OAAO,CAAC,MAAM;oBACtB,IAAI,EAAE,CAAC,GAAG,OAAO,CAAC,IAAI,CAAC;iBACxB,CAAC,CAAC;YACL,CAAC;YAED,gCAAgC;YAChC,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;QAC3C,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,WAAW,CAAC,OAAyB,EAAE,IAAmB,EAAE,OAAmB;QAC3F,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,mBAAmB,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC;QAEzD,QAAQ,IAAI,CAAC,IAAI,EAAE,CAAC;YAClB,KAAK,YAAY;gBACf,MAAM,IAAI,CAAC,qBAAqB,CAAC,OAAO,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;gBACzD,MAAM;YACR,KAAK,SAAS;gBACZ,MAAM,IAAI,CAAC,kBAAkB,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;gBAC7C,MAAM;YACR,KAAK,UAAU;gBACb,MAAM,IAAI,CAAC,mBAAmB,CAAC,OAAO,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;gBACvD,MAAM;YACR,KAAK,UAAU;gBACb,MAAM,IAAI,CAAC,mBAAmB,CAAC,OAAO,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;gBACvD,MAAM;YACR;gBACE,MAAM,IAAI,KAAK,CAAC,sBAAsB,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;QACvD,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,qBAAqB,CAAC,OAAyB,EAAE,IAAmB,EAAE,OAAmB;QACrG,+CAA+C;QAC/C,OAAO,CAAC,SAAS,GAAG,EAAE,GAAG,OAAO,CAAC,SAAS,EAAE,GAAG,OAAO,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC;QAE3E,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,qCAAqC,EAAE,OAAO,CAAC,SAAS,CAAC,CAAC;IAC9E,CAAC;IAEO,KAAK,CAAC,kBAAkB,CAAC,IAAmB,EAAE,OAAmB;QACvE,sCAAsC;QACtC,6DAA6D;QAE7D,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,oBAAoB,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC;IAC5D,CAAC;IAEO,KAAK,CAAC,mBAAmB,CAAC,OAAyB,EAAE,IAAmB,EAAE,OAAmB;QACnG,oCAAoC;QACpC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4BAA4B,CAAC,CAAC;IAClD,CAAC;IAEO,KAAK,CAAC,mBAAmB,CAAC,OAAyB,EAAE,IAAmB,EAAE,OAAmB;QACnG,+BAA+B;QAC/B,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,oBAAoB,CAAC,CAAC;IAC1C,CAAC;IAED,KAAK,CAAC,eAAe,CAAC,WAAmB;QACvC,MAAM,OAAO,GAAG,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;QACvD,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,OAAO,KAAK,CAAC;QACf,CAAC;QAED,OAAO,CAAC,MAAM,GAAG,WAAW,CAAC;QAC7B,OAAO,CAAC,OAAO,GAAG,IAAI,IAAI,EAAE,CAAC;QAE7B,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YACrB,IAAI,EAAE,WAAW;YACjB,SAAS,EAAE,OAAO,CAAC,SAAS;YAC5B,WAAW;YACX,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC,CAAC;QAEH,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,aAAa,WAAW,YAAY,CAAC,CAAC;QACvD,OAAO,IAAI,CAAC;IACd,CAAC;IAED,mBAAmB;QACjB,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,CAAC,CAAC;IACpD,CAAC;IAED,mBAAmB,CAAC,SAAkB;QACpC,IAAI,SAAS,EAAE,CAAC;YACd,OAAO,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,SAAS,KAAK,SAAS,CAAC,CAAC;QAChF,CAAC;QACD,OAAO,CAAC,GAAG,IAAI,CAAC,gBAAgB,CAAC,CAAC;IACpC,CAAC;IAED,kBAAkB,CAAC,WAAmB;QACpC,OAAO,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,WAAW,CAAC,IAAI,IAAI,CAAC;IACxD,CAAC;IAEO,mBAAmB;QACzB,OAAO,QAAQ,IAAI,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;IACzE,CAAC;IAEO,aAAa;QACnB,OAAO,OAAO,IAAI,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;IACxE,CAAC;IAEO,kBAAkB,CAAC,OAAmB;QAC5C,mDAAmD;QACnD,OAAO,CAAC,CAAC,CAAC,kBAAkB;IAC9B,CAAC;IAEO,iBAAiB,CAAC,OAAmB;QAC3C,OAAO;YACL,EAAE,IAAI,EAAE,YAAY,EAAE,WAAW,EAAE,gCAAgC,EAAE;YACrE,EAAE,IAAI,EAAE,SAAS,EAAE,WAAW,EAAE,4BAA4B,EAAE;YAC9D,EAAE,IAAI,EAAE,UAAU,EAAE,WAAW,EAAE,0BAA0B,EAAE;YAC7D,EAAE,IAAI,EAAE,UAAU,EAAE,WAAW,EAAE,sBAAsB,EAAE;SAC1D,CAAC;IACJ,CAAC;IAEO,uBAAuB,CAAC,OAAmB;QACjD,OAAO;YACL,SAAS,EAAE,OAAO,CAAC,EAAE;YACrB,SAAS,EAAE,OAAO,CAAC,SAAS;YAC5B,MAAM,EAAE,OAAO,CAAC,MAAM;YACtB,aAAa,EAAE,OAAO,CAAC,OAAO,IAAI,OAAO,CAAC,SAAS;gBACjD,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,EAAE,GAAG,OAAO,CAAC,SAAS,CAAC,OAAO,EAAE;gBACzD,CAAC,CAAC,CAAC;SACN,CAAC;IACJ,CAAC;IAEO,MAAM,CAAC,OAAyB,EAAE,GAAkB;QAC1D,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IACzB,CAAC;IAEO,KAAK,CAAC,EAAU;QACtB,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC;IACzD,CAAC;CACF;AApTD,gDAoTC"}