{"version": 3, "file": "cursor-integration.js", "sourceRoot": "", "sources": ["../../../src/plugin/activation/cursor-integration.ts"], "names": [], "mappings": ";;;AAuNA,kDA0CC;AAhQD,+CAAkD;AA6BlD,MAAa,iBAAiB;IAK5B,YAAY,SAAoB;QAHxB,WAAM,GAAG,IAAA,qBAAY,EAAC,mBAAmB,CAAC,CAAC;QAC3C,uBAAkB,GAA0B,IAAI,GAAG,EAAE,CAAC;QAG5D,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC;IAC1B,CAAC;IAED,KAAK,CAAC,UAAU;QACd,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,oCAAoC,CAAC,CAAC;QAEvD,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAC9B,MAAM,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAClC,MAAM,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAEjC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,gCAAgC,CAAC,CAAC;IACrD,CAAC;IAEO,KAAK,CAAC,gBAAgB;QAC5B,MAAM,QAAQ,GAAG;YACf;gBACE,EAAE,EAAE,2BAA2B;gBAC/B,QAAQ,EAAE,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC;aACzC;YACD;gBACE,EAAE,EAAE,2BAA2B;gBAC/B,QAAQ,EAAE,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC;aACzC;YACD;gBACE,EAAE,EAAE,0BAA0B;gBAC9B,QAAQ,EAAE,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC;aACxC;YACD;gBACE,EAAE,EAAE,yBAAyB;gBAC7B,QAAQ,EAAE,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC;aACvC;YACD;gBACE,EAAE,EAAE,yBAAyB;gBAC7B,QAAQ,EAAE,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC;aACvC;SACF,CAAC;QAEF,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE,CAAC;YAC/B,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,OAAO,CAAC,EAAE,EAAE,OAAO,CAAC,QAAQ,CAAC,CAAC;YACnE,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,EAAE,OAAO,CAAC,QAAQ,CAAC,CAAC;YAE1D,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,uBAAuB,OAAO,CAAC,EAAE,EAAE,CAAC,CAAC;QACzD,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,oBAAoB;QAChC,2BAA2B;QAC3B,MAAM,aAAa,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,mBAAmB,EAAE,CAAC;QAC/D,aAAa,CAAC,IAAI,GAAG,cAAc,CAAC;QACpC,aAAa,CAAC,OAAO,GAAG,wBAAwB,CAAC;QACjD,aAAa,CAAC,OAAO,GAAG,0BAA0B,CAAC;QACnD,aAAa,CAAC,IAAI,EAAE,CAAC;QAErB,2CAA2C;QAC3C,IAAI,CAAC,oBAAoB,EAAE,CAAC;IAC9B,CAAC;IAEO,KAAK,CAAC,mBAAmB;QAC/B,mCAAmC;QACnC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,wBAAwB,CAAC,GAAG,EAAE;YAClD,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,qCAAqC,CAAC,CAAC;YACxD,IAAI,CAAC,yBAAyB,EAAE,CAAC;QACnC,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,oBAAoB;QAC1B,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,kBAAkB,EAAE,CAAC;QAEtD,KAAK,CAAC,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;IAChD,CAAC;IAEO,iBAAiB;QACvB,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KA0DN,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,cAAc;QAC1B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;QAC9C,+DAA+D;QAC/D,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,uBAAuB,CAAC,CAAC;IACrE,CAAC;IAEO,KAAK,CAAC,cAAc,CAAC,SAAiB;QAC5C,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,sBAAsB,SAAS,EAAE,CAAC,CAAC;QACpD,kEAAkE;QAClE,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,sBAAsB,SAAS,EAAE,CAAC,CAAC;IAC/E,CAAC;IAEO,KAAK,CAAC,aAAa;QACzB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;QAC7C,yBAAyB;IAC3B,CAAC;IAEO,KAAK,CAAC,YAAY;QACxB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC;QAC5C,mBAAmB;IACrB,CAAC;IAEO,KAAK,CAAC,YAAY,CAAC,SAAiB;QAC1C,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,sBAAsB,SAAS,EAAE,CAAC,CAAC;QACpD,yDAAyD;IAC3D,CAAC;IAEO,yBAAyB;QAC/B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,kCAAkC,CAAC,CAAC;QACrD,6CAA6C;IAC/C,CAAC;IAED,KAAK,CAAC,OAAO;QACX,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,iCAAiC,CAAC,CAAC;QAEpD,+BAA+B;QAC/B,KAAK,MAAM,CAAC,SAAS,CAAC,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAClD,mDAAmD;YACnD,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,yBAAyB,SAAS,EAAE,CAAC,CAAC;QAC1D,CAAC;QAED,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,CAAC;IAClC,CAAC;CACF;AAtLD,8CAsLC;AAED,kCAAkC;AAClC,SAAgB,mBAAmB;IACjC,OAAO;QACL,MAAM,EAAE;YACN,sBAAsB,EAAE,CAAC,OAAe,EAAE,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,SAAS,OAAO,EAAE,CAAC;YAC5E,gBAAgB,EAAE,CAAC,OAAe,EAAE,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC,UAAU,OAAO,EAAE,CAAC;YACzE,kBAAkB,EAAE,CAAC,OAAe,EAAE,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,YAAY,OAAO,EAAE,CAAC;YAC5E,mBAAmB,EAAE,GAAG,EAAE,CAAC,CAAC;gBAC1B,IAAI,EAAE,EAAE;gBACR,OAAO,EAAE,EAAE;gBACX,OAAO,EAAE,EAAE;gBACX,IAAI,EAAE,GAAG,EAAE,GAAE,CAAC;aACf,CAAC;YACF,kBAAkB,EAAE,GAAG,EAAE,CAAC,CAAC;gBACzB,OAAO,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE;gBACrB,YAAY,EAAE,GAAG,EAAE,GAAE,CAAC;gBACtB,OAAO,EAAE,GAAG,EAAE,GAAE,CAAC;aAClB,CAAC;SACH;QAED,QAAQ,EAAE;YACR,eAAe,EAAE,CAAC,OAAe,EAAE,QAAkB,EAAE,EAAE;gBACvD,OAAO,CAAC,GAAG,CAAC,uBAAuB,OAAO,EAAE,CAAC,CAAC;YAChD,CAAC;YACD,cAAc,EAAE,KAAK,EAAE,OAAe,EAAE,GAAG,IAAW,EAAE,EAAE;gBACxD,OAAO,CAAC,GAAG,CAAC,sBAAsB,OAAO,EAAE,EAAE,IAAI,CAAC,CAAC;gBACnD,OAAO,IAAI,CAAC;YACd,CAAC;SACF;QAED,SAAS,EAAE;YACT,gBAAgB,EAAE,GAAG,EAAE,CAAC,CAAC,EAAE,CAAC;YAC5B,wBAAwB,EAAE,CAAC,QAAkB,EAAE,EAAE;gBAC/C,OAAO,CAAC,GAAG,CAAC,0CAA0C,CAAC,CAAC;YAC1D,CAAC;YACD,kBAAkB,EAAE,GAAG,EAAE,CAAC,IAAI;SAC/B;QAED,SAAS,EAAE;YACT,8BAA8B,EAAE,GAAG,EAAE,CAAC,CAAC,EAAE,CAAC;YAC1C,qBAAqB,EAAE,GAAG,EAAE,CAAC,CAAC,EAAE,CAAC;SAClC;KACF,CAAC;AACJ,CAAC"}