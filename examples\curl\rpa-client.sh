#!/bin/bash

# 影刀RPA引擎 curl 示例脚本
# 
# 这个脚本展示了如何使用curl命令行工具与影刀RPA引擎进行交互

set -e  # 遇到错误立即退出

# 配置
BASE_URL="http://localhost:8080"
TEMP_DIR="/tmp/rpa_test"
LOG_FILE="${TEMP_DIR}/rpa_test.log"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1" | tee -a "$LOG_FILE"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1" | tee -a "$LOG_FILE"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a "$LOG_FILE"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "$LOG_FILE"
}

# 检查依赖
check_dependencies() {
    log_info "检查依赖..."
    
    if ! command -v curl &> /dev/null; then
        log_error "curl 未安装"
        exit 1
    fi
    
    if ! command -v jq &> /dev/null; then
        log_warning "jq 未安装，JSON输出可能不够美观"
        JQ_AVAILABLE=false
    else
        JQ_AVAILABLE=true
    fi
    
    log_success "依赖检查完成"
}

# JSON格式化输出
format_json() {
    if [ "$JQ_AVAILABLE" = true ]; then
        jq . 2>/dev/null || cat
    else
        cat
    fi
}

# 等待服务就绪
wait_for_service() {
    log_info "等待引擎服务启动..."
    
    for i in {1..30}; do
        if curl -s "$BASE_URL/health" > /dev/null 2>&1; then
            log_success "引擎服务已就绪"
            return 0
        fi
        
        if [ $i -eq 30 ]; then
            log_error "引擎服务启动超时"
            exit 1
        fi
        
        sleep 1
        echo -n "."
    done
}

# 健康检查
health_check() {
    log_info "执行健康检查..."
    
    response=$(curl -s "$BASE_URL/health")
    status=$(echo "$response" | grep -o '"status":"[^"]*"' | cut -d'"' -f4)
    uptime=$(echo "$response" | grep -o '"uptime":[0-9.]*' | cut -d':' -f2)
    
    if [ "$status" = "healthy" ]; then
        log_success "引擎状态: $status, 运行时间: ${uptime}秒"
        echo "$response" | format_json
    else
        log_error "引擎状态异常: $status"
        return 1
    fi
}

# 获取引擎信息
get_engine_info() {
    log_info "获取引擎信息..."
    
    response=$(curl -s "$BASE_URL/info")
    name=$(echo "$response" | grep -o '"name":"[^"]*"' | cut -d'"' -f4)
    version=$(echo "$response" | grep -o '"version":"[^"]*"' | cut -d'"' -f4)
    
    log_success "引擎: $name v$version"
    echo "$response" | format_json
}

# 创建流程
create_process() {
    local name="$1"
    local description="$2"
    local variables="$3"
    
    log_info "创建流程: $name"
    
    local data="{\"name\":\"$name\",\"description\":\"$description\""
    if [ -n "$variables" ]; then
        data="$data,\"variables\":$variables"
    fi
    data="$data}"
    
    response=$(curl -s -X POST "$BASE_URL/processes" \
        -H "Content-Type: application/json" \
        -d "$data")
    
    process_id=$(echo "$response" | grep -o '"id":"[^"]*"' | cut -d'"' -f4)
    
    if [ -n "$process_id" ]; then
        log_success "流程创建成功: $process_id"
        echo "$response" | format_json
        echo "$process_id"
    else
        log_error "流程创建失败"
        echo "$response" | format_json
        return 1
    fi
}

# 获取流程列表
get_processes() {
    log_info "获取流程列表..."
    
    response=$(curl -s "$BASE_URL/processes")
    count=$(echo "$response" | grep -o '"id":"[^"]*"' | wc -l)
    
    log_success "找到 $count 个流程"
    echo "$response" | format_json
}

# 获取特定流程
get_process() {
    local process_id="$1"
    
    log_info "获取流程信息: $process_id"
    
    response=$(curl -s "$BASE_URL/processes/$process_id")
    name=$(echo "$response" | grep -o '"name":"[^"]*"' | cut -d'"' -f4)
    
    if [ -n "$name" ]; then
        log_success "流程: $name"
        echo "$response" | format_json
    else
        log_error "流程不存在: $process_id"
        return 1
    fi
}

# 更新流程变量
update_process_variables() {
    local process_id="$1"
    local variables="$2"
    
    log_info "更新流程变量: $process_id"
    
    response=$(curl -s -X PUT "$BASE_URL/processes/$process_id/variables" \
        -H "Content-Type: application/json" \
        -d "{\"variables\":$variables}")
    
    log_success "变量更新成功"
    echo "$response" | format_json
}

# 执行流程
execute_process() {
    local process_id="$1"
    local parameters="$2"
    local debug_mode="${3:-false}"
    
    log_info "执行流程: $process_id"
    
    local data="{\"parameters\":$parameters,\"debugMode\":$debug_mode}"
    
    response=$(curl -s -X POST "$BASE_URL/processes/$process_id/execute" \
        -H "Content-Type: application/json" \
        -d "$data")
    
    execution_id=$(echo "$response" | grep -o '"executionId":"[^"]*"' | cut -d'"' -f4)
    
    if [ -n "$execution_id" ]; then
        log_success "流程执行启动: $execution_id"
        echo "$response" | format_json
        echo "$execution_id"
    else
        log_error "流程执行启动失败"
        echo "$response" | format_json
        return 1
    fi
}

# 监控执行状态
monitor_execution() {
    local execution_id="$1"
    local max_attempts="${2:-15}"
    
    log_info "监控执行状态: $execution_id"
    
    for i in $(seq 1 $max_attempts); do
        response=$(curl -s "$BASE_URL/executions/$execution_id")
        status=$(echo "$response" | grep -o '"status":"[^"]*"' | cut -d'"' -f4)
        progress=$(echo "$response" | grep -o '"percentage":[0-9]*' | cut -d':' -f2)
        
        log_info "状态: $status, 进度: ${progress:-0}%"
        
        case "$status" in
            "completed")
                log_success "执行完成"
                echo "$response" | format_json
                return 0
                ;;
            "error"|"stopped")
                log_error "执行失败或停止: $status"
                echo "$response" | format_json
                return 1
                ;;
        esac
        
        sleep 2
    done
    
    log_warning "监控超时"
    return 1
}

# 停止执行
stop_execution() {
    local execution_id="$1"
    
    log_info "停止执行: $execution_id"
    
    response=$(curl -s -X POST "$BASE_URL/executions/$execution_id/stop")
    
    log_success "执行停止请求已发送"
    echo "$response" | format_json
}

# 删除流程
delete_process() {
    local process_id="$1"
    
    log_info "删除流程: $process_id"
    
    response=$(curl -s -X DELETE "$BASE_URL/processes/$process_id")
    
    if [ -z "$response" ]; then
        log_success "流程删除成功"
    else
        log_error "流程删除失败"
        echo "$response" | format_json
        return 1
    fi
}

# 清理函数
cleanup() {
    log_info "清理临时文件..."
    rm -f "$LOG_FILE"
    log_success "清理完成"
}

# 主函数
main() {
    log_info "=== 影刀RPA引擎 curl 示例脚本 ==="
    
    # 创建临时目录
    mkdir -p "$TEMP_DIR"
    
    # 设置清理陷阱
    trap cleanup EXIT
    
    # 检查依赖
    check_dependencies
    
    # 等待服务
    wait_for_service
    
    # 基础测试
    health_check
    echo
    get_engine_info
    echo
    
    # 创建测试流程
    log_info "创建测试流程..."
    
    # 流程1: Web数据提取
    process1_id=$(create_process "Web数据提取-curl" \
        "使用curl创建的Web数据提取流程" \
        '{"url":"https://example.com","selectors":["title","content"],"outputFormat":"json"}')
    echo
    
    # 流程2: 文件处理
    process2_id=$(create_process "文件处理-curl" \
        "使用curl创建的文件处理流程" \
        '{"inputPath":"./input","outputPath":"./output","fileTypes":["pdf","xlsx"]}')
    echo
    
    # 流程3: API同步
    process3_id=$(create_process "API同步-curl" \
        "使用curl创建的API同步流程" \
        '{"sourceApi":"https://api.example.com","targetApi":"https://internal.com","syncInterval":3600}')
    echo
    
    # 获取流程列表
    get_processes
    echo
    
    # 更新流程变量
    log_info "更新流程变量..."
    update_process_variables "$process1_id" '{"timeout":60000,"retryCount":3,"maxItems":100}'
    echo
    
    # 获取更新后的流程
    get_process "$process1_id"
    echo
    
    # 执行流程
    log_info "执行流程测试..."
    
    # 执行流程1
    exec1_id=$(execute_process "$process1_id" \
        '{"action":"scrape","saveToDatabase":true}' \
        "true")
    echo
    
    # 执行流程2
    exec2_id=$(execute_process "$process2_id" \
        '{"action":"process","validateOutput":true}' \
        "false")
    echo
    
    # 监控执行
    log_info "监控执行状态..."
    monitor_execution "$exec1_id" 10
    echo
    monitor_execution "$exec2_id" 10
    echo
    
    # 批量操作示例
    log_info "批量操作示例..."
    
    # 创建批量流程
    batch_processes=()
    for i in {1..3}; do
        batch_id=$(create_process "批量流程-$i-curl" \
            "批量创建的第$i个流程" \
            '{"batch":true,"priority":"normal"}')
        batch_processes+=("$batch_id")
        echo
    done
    
    log_success "批量创建完成，共 ${#batch_processes[@]} 个流程"
    
    # 并发执行
    log_info "并发执行测试..."
    
    # 后台执行多个流程
    pids=()
    for process_id in "${batch_processes[@]:0:2}"; do
        (
            exec_id=$(execute_process "$process_id" '{"test":true}' 'false')
            monitor_execution "$exec_id" 5
        ) &
        pids+=($!)
    done
    
    # 等待所有后台任务完成
    for pid in "${pids[@]}"; do
        wait $pid
    done
    
    echo
    
    # 错误处理测试
    log_info "错误处理测试..."
    
    # 尝试获取不存在的流程
    log_info "尝试获取不存在的流程..."
    if ! get_process "non_existent_process" 2>/dev/null; then
        log_success "正确处理了不存在的流程"
    fi
    echo
    
    # 尝试执行不存在的流程
    log_info "尝试执行不存在的流程..."
    if ! execute_process "non_existent_process" '{}' 'false' 2>/dev/null; then
        log_success "正确处理了执行失败"
    fi
    echo
    
    # 清理测试数据
    log_info "清理测试流程..."
    
    all_processes=("$process1_id" "$process2_id" "$process3_id" "${batch_processes[@]}")
    
    for process_id in "${all_processes[@]}"; do
        if delete_process "$process_id"; then
            log_success "删除流程: $process_id"
        fi
    done
    
    echo
    
    # 最终验证
    log_info "最终验证..."
    
    final_count=$(get_processes | grep -o '"id":"[^"]*"' | wc -l)
    log_success "剩余流程数量: $final_count"
    
    # 最终健康检查
    health_check
    
    log_success "🎉 所有测试完成！"
}

# 脚本参数处理
case "${1:-}" in
    "health")
        health_check
        ;;
    "info")
        get_engine_info
        ;;
    "processes")
        get_processes
        ;;
    "create")
        name="${2:-Test Process}"
        description="${3:-Created by curl}"
        create_process "$name" "$description" '{}'
        ;;
    "execute")
        process_id="${2:-}"
        if [ -z "$process_id" ]; then
            log_error "请指定流程ID"
            exit 1
        fi
        exec_id=$(execute_process "$process_id" '{}' 'true')
        monitor_execution "$exec_id"
        ;;
    "monitor")
        execution_id="${2:-}"
        if [ -z "$execution_id" ]; then
            log_error "请指定执行ID"
            exit 1
        fi
        monitor_execution "$execution_id"
        ;;
    "clean")
        log_info "清理所有流程..."
        processes=$(get_processes)
        process_ids=$(echo "$processes" | grep -o '"id":"[^"]*"' | cut -d'"' -f4)
        for id in $process_ids; do
            delete_process "$id"
        done
        ;;
    *)
        main
        ;;
esac