import { RPAProcess, RPAExecutionConfig, RPAExecutionResult } from '../../types';
import { RPAConnectionManager } from '../connection';
export interface ProcessDefinition {
    id: string;
    name: string;
    description: string;
    version: string;
    author: string;
    created: Date;
    modified: Date;
    tags: string[];
    inputParameters: ProcessParameter[];
    outputParameters: ProcessParameter[];
    steps: ProcessStep[];
    dependencies?: string[];
}
export interface ProcessParameter {
    name: string;
    type: 'string' | 'number' | 'boolean' | 'object' | 'array';
    required: boolean;
    defaultValue?: any;
    description?: string;
}
export interface ProcessStep {
    id: string;
    name: string;
    type: 'action' | 'condition' | 'loop' | 'parallel';
    config: any;
    nextSteps: string[];
    errorHandling?: {
        retryCount: number;
        retryDelay: number;
        onFailure: 'continue' | 'stop' | 'goto';
        gotoStep?: string;
    };
}
export declare class RPAProcessManager {
    private executor;
    private connectionManager;
    private logger;
    private processes;
    private processDefinitions;
    constructor(connectionManager: RPAConnectionManager);
    createProcess(definition: ProcessDefinition): Promise<RPAProcess>;
    executeProcess(processId: string, config?: Partial<RPAExecutionConfig>): Promise<RPAExecutionResult>;
    stopProcess(processId: string): Promise<boolean>;
    getProcess(processId: string): RPAProcess | undefined;
    getProcessDefinition(processId: string): ProcessDefinition | undefined;
    getAllProcesses(): RPAProcess[];
    getActiveProcesses(): RPAProcess[];
    loadProcessFromFile(filePath: string): Promise<RPAProcess>;
    saveProcessToFile(processId: string, filePath: string): Promise<void>;
    deleteProcess(processId: string): Promise<boolean>;
    getExecutionHistory(processId?: string): any[];
    getActiveExecutions(): any[];
    private validateProcessDefinition;
    private handleExecutionEvent;
    private handleExecutionStarted;
    private handleExecutionCompleted;
    private handleExecutionError;
    private handleExecutionCancelled;
}
//# sourceMappingURL=process-manager.d.ts.map