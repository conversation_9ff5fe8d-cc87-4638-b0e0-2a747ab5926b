# C#/.NET环境配置

## 3.1.1 Visual Studio项目设置

**创建AutoCAD .NET项目：**
1. 打开Visual Studio 2022
2. 创建新项目 -> 类库(Class Library)
3. 选择.NET Framework 4.8或更高版本
4. 项目命名规范：`AutoCAD.[功能名称].[版本]`

**项目属性配置：**
```xml
<!-- 项目文件(.csproj)配置 -->
<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net48</TargetFramework>
    <OutputType>Library</OutputType>
    <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
    <AutoCADVersion>2024</AutoCADVersion>
  </PropertyGroup>
  
  <ItemGroup>
    <!-- AutoCAD引用 -->
    <Reference Include="acdbmgd">
      <HintPath>C:\Program Files\Autodesk\AutoCAD 2024\acdbmgd.dll</HintPath>
    </Reference>
    <Reference Include="accoremgd">
      <HintPath>C:\Program Files\Autodesk\AutoCAD 2024\accoremgd.dll</HintPath>
    </Reference>
    <Reference Include="AcMgd">
      <HintPath>C:\Program Files\Autodesk\AutoCAD 2024\AcMgd.dll</HintPath>
    </Reference>
  </ItemGroup>
</Project>
```

**调试配置：**
```xml
<!-- app.config文件 -->
<?xml version="1.0" encoding="utf-8" ?>
<configuration>
  <startup>
    <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.8" />
  </startup>
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <probing privatePath="bin" />
    </assemblyBinding>
  </runtime>
</configuration>
```

## 3.1.2 AutoCAD .NET API引用

**核心DLL引用：**
- `acdbmgd.dll` - 数据库访问
- `accoremgd.dll` - 核心功能
- `AcMgd.dll` - 应用程序管理
- `AcWindows.dll` - Windows界面

**添加引用代码：**
```csharp
using Autodesk.AutoCAD.Runtime;
using Autodesk.AutoCAD.ApplicationServices;
using Autodesk.AutoCAD.DatabaseServices;
using Autodesk.AutoCAD.Geometry;
using Autodesk.AutoCAD.EditorInput;
using Autodesk.AutoCAD.Colors;
using Autodesk.AutoCAD.Windows;
```

**命名空间说明：**
```csharp
// 运行时支持
using Autodesk.AutoCAD.Runtime;

// 应用程序和文档管理
using Autodesk.AutoCAD.ApplicationServices;

// 数据库和实体操作
using Autodesk.AutoCAD.DatabaseServices;

// 几何计算
using Autodesk.AutoCAD.Geometry;

// 用户输入和编辑器
using Autodesk.AutoCAD.EditorInput;

// 颜色管理
using Autodesk.AutoCAD.Colors;

// Windows界面
using Autodesk.AutoCAD.Windows;
```

## 3.1.3 调试环境配置

**设置外部程序启动：**
1. 右键项目 -> 属性
2. 调试 -> 启动操作 -> 启动外部程序
3. 选择AutoCAD可执行文件路径

**AutoCAD路径示例：**
```
C:\Program Files\Autodesk\AutoCAD 2024\acad.exe
```

**调试配置代码：**
```csharp
using System;
using System.Diagnostics;
using Autodesk.AutoCAD.Runtime;

[assembly: ExtensionApplication(typeof(AutoCADPlugin.MyPlugin))]

namespace AutoCADPlugin
{
    public class MyPlugin : IExtensionApplication
    {
        public void Initialize()
        {
            // 插件初始化
            Debug.WriteLine("插件已加载");
            
            // 添加命令
            // 命令将通过CommandMethod属性注册
        }
        
        public void Terminate()
        {
            // 插件卸载
            Debug.WriteLine("插件已卸载");
        }
    }
}
```

**调试技巧：**
```csharp
[CommandMethod("DebugInfo")]
public void DebugInfo()
{
    Document doc = Application.DocumentManager.MdiActiveDocument;
    Editor ed = doc.Editor;
    
    try
    {
        // 输出调试信息
        ed.WriteMessage("\n当前文档: " + doc.Name);
        ed.WriteMessage("\n数据库版本: " + doc.Database.Version);
        ed.WriteMessage("\n工作目录: " + Environment.CurrentDirectory);
        
        // 测试代码
        // ...
    }
    catch (System.Exception ex)
    {
        ed.WriteMessage("\n错误: " + ex.Message);
        // 输出详细错误信息用于调试
        Debug.WriteLine(ex.ToString());
    }
}
```

## 3.1.4 插件打包和部署

**打包配置：**
```xml
<!-- 项目配置 -->
<PropertyGroup>
  <OutputPath>bin\Release\</OutputPath>
  <DocumentationFile>bin\Release\AutoCADPlugin.xml</DocumentationFile>
  <SignAssembly>true</SignAssembly>
  <AssemblyOriginatorKeyFile>AutoCADPlugin.snk</AssemblyOriginatorKeyFile>
</PropertyGroup>

<!-- Post-build事件 -->
<Target Name="PostBuild" AfterTargets="PostBuildEvent">
  <Exec Command="copy /Y &quot;$(TargetPath)&quot; &quot;C:\Program Files\Autodesk\AutoCAD 2024\&quot;" />
  <Exec Command="copy /Y &quot;$(TargetDir)*.dll&quot; &quot;C:\Program Files\Autodesk\AutoCAD 2024\&quot;" />
</Target>
```

**安装程序配置：**
```csharp
using Autodesk.AutoCAD.Runtime;
using Autodesk.AutoCAD.ApplicationServices;

[assembly: CommandClass(typeof(AutoCADPlugin.Installer))]

namespace AutoCADPlugin
{
    public class Installer
    {
        [CommandMethod("InstallPlugin")]
        public void InstallPlugin()
        {
            Document doc = Application.DocumentManager.MdiActiveDocument;
            Editor ed = doc.Editor;
            
            try
            {
                // 添加到应用程序加载列表
                string pluginPath = System.Reflection.Assembly.GetExecutingAssembly().Location;
                
                // 这里可以添加注册表操作或其他安装逻辑
                ed.WriteMessage("\n插件安装成功！");
                ed.WriteMessage("\n插件路径: " + pluginPath);
            }
            catch (System.Exception ex)
            {
                ed.WriteMessage("\n安装失败: " + ex.Message);
            }
        }
        
        [CommandMethod("UninstallPlugin")]
        public void UninstallPlugin()
        {
            Document doc = Application.DocumentManager.MdiActiveDocument;
            Editor ed = doc.Editor;
            
            try
            {
                // 移除插件
                ed.WriteMessage("\n插件卸载成功！");
            }
            catch (System.Exception ex)
            {
                ed.WriteMessage("\n卸载失败: " + ex.Message);
            }
        }
    }
}
```

**部署清单文件：**
```xml
<!-- AutoCADPlugin.manifest -->
<?xml version="1.0" encoding="utf-8"?>
<AssemblyManifest>
  <AssemblyIdentity Name="AutoCADPlugin" Version="*******" />
  <Description>AutoCAD自动化插件</Description>
  <Requirements>
    <Platform Name="AutoCAD" Version="2024" />
  </Requirements>
  <Components>
    <Component Type="Command" Name="DrawLine" />
    <Component Type="Command" Name="DrawCircle" />
    <Component Type="Command" Name="DebugInfo" />
  </Components>
</AssemblyManifest>
```

---

**CURSOR提示词模板：**
```
请为AutoCAD .NET开发配置Visual Studio项目，包括：
1. 项目创建和配置
2. 必要的引用和命名空间
3. 调试环境设置
4. 插件打包和部署方案
```