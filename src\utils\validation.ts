import Joi from 'joi';

export const rpaEngineSchema = Joi.object({
  id: Joi.string().required(),
  name: Joi.string().required(),
  version: Joi.string().required(),
  status: Joi.string().valid('running', 'stopped', 'error').required(),
  endpoint: Joi.string().uri().required(),
  capabilities: Joi.array().items(Joi.string()).required()
});

export const connectionConfigSchema = Joi.object({
  host: Joi.string().hostname().required(),
  port: Joi.number().port().required(),
  username: Joi.string().optional(),
  password: Joi.string().optional(),
  apiKey: Joi.string().optional(),
  timeout: Joi.number().min(1000).default(30000),
  retryAttempts: Joi.number().min(0).default(3)
});

export const executionConfigSchema = Joi.object({
  engineId: Joi.string().required(),
  processId: Joi.string().required(),
  parameters: Joi.object().default({}),
  timeout: Joi.number().min(1000).optional(),
  retryCount: Joi.number().min(0).default(0),
  debugMode: Joi.boolean().default(false)
});

export const validate = (schema: Joi.Schema, data: any) => {
  const { error, value } = schema.validate(data);
  if (error) {
    throw new Error(`Validation error: ${error.details[0].message}`);
  }
  return value;
};

export const validateRPAEngine = (data: any) => validate(rpaEngineSchema, data);
export const validateConnectionConfig = (data: any) => validate(connectionConfigSchema, data);
export const validateExecutionConfig = (data: any) => validate(executionConfigSchema, data);