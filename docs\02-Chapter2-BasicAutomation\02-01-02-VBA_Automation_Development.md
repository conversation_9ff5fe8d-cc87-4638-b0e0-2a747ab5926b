# VBA自动化开发

## 2.2.1 VBA编辑器使用

**打开VBA编辑器：**
- 在AutoCAD中按 `ALT+F11`
- 或者在命令行输入 `VBAIDE`

**VBA编辑器界面：**
- **项目浏览器**：显示VBA项目结构
- **代码窗口**：编写和编辑代码
- **属性窗口**：查看和修改对象属性
- **立即窗口**：调试和测试代码

**基本操作：**
```vba
' 插入模块：插入 -> 模块
' 插入用户窗体：插入 -> 用户窗体
' 插入类模块：插入 -> 类模块
' 运行代码：F5
' 调试代码：F8（逐语句）
' 设置断点：F9
```

## 2.2.2 AutoCAD对象模型

**AutoCAD对象层次结构：**
```
Application (应用程序)
  ├── Documents (文档集合)
  │   ├── Document (文档)
  │   │   ├── ModelSpace (模型空间)
  │   │   ├── PaperSpace (图纸空间)
  │   │   ├── Layers (图层集合)
  │   │   ├── Blocks (图块集合)
  │   │   └── ...
  │   └── ...
  ├── Preferences (首选项)
  └── ...
```

**主要对象：**
```vba
' 获取AutoCAD应用程序对象
Dim acadApp As AcadApplication
Set acadApp = GetObject(, "AutoCAD.Application")

' 获取当前文档
Dim acadDoc As AcadDocument
Set acadDoc = acadApp.ActiveDocument

' 获取模型空间
Dim modelSpace As AcadModelSpace
Set modelSpace = acadDoc.ModelSpace

' 获取图纸空间
Dim paperSpace As AcadPaperSpace
Set paperSpace = acadDoc.PaperSpace
```

## 2.2.3 基本图形操作

**创建基本图形：**
```vba
' 绘制直线
Sub DrawLine()
    Dim acadApp As AcadApplication
    Dim acadDoc As AcadDocument
    Dim startPoint(0 To 2) As Double
    Dim endPoint(0 To 2) As Double
    Dim lineObj As AcadLine
    
    Set acadApp = GetObject(, "AutoCAD.Application")
    Set acadDoc = acadApp.ActiveDocument
    
    startPoint(0) = 0: startPoint(1) = 0: startPoint(2) = 0
    endPoint(0) = 100: endPoint(1) = 100: endPoint(2) = 0
    
    Set lineObj = acadDoc.ModelSpace.AddLine(startPoint, endPoint)
    acadDoc.Regen True
End Sub

' 绘制圆
Sub DrawCircle()
    Dim acadApp As AcadApplication
    Dim acadDoc As AcadDocument
    Dim centerPoint(0 To 2) As Double
    Dim radius As Double
    Dim circleObj As AcadCircle
    
    Set acadApp = GetObject(, "AutoCAD.Application")
    Set acadDoc = acadApp.ActiveDocument
    
    centerPoint(0) = 50: centerPoint(1) = 50: centerPoint(2) = 0
    radius = 25
    
    Set circleObj = acadDoc.ModelSpace.AddCircle(centerPoint, radius)
    acadDoc.Regen True
End Sub

' 绘制矩形
Sub DrawRectangle()
    Dim acadApp As AcadApplication
    Dim acadDoc As AcadDocument
    Dim startPoint(0 To 2) As Double
    Dim endPoint(0 To 2) As Double
    Dim rectObj As AcadLWPolyline
    Dim vertices(0 To 7) As Double
    
    Set acadApp = GetObject(, "AutoCAD.Application")
    Set acadDoc = acadApp.ActiveDocument
    
    startPoint(0) = 0: startPoint(1) = 0: startPoint(2) = 0
    endPoint(0) = 100: endPoint(1) = 50: endPoint(2) = 0
    
    ' 定义矩形的四个顶点
    vertices(0) = startPoint(0): vertices(1) = startPoint(1)
    vertices(2) = endPoint(0): vertices(3) = startPoint(1)
    vertices(4) = endPoint(0): vertices(5) = endPoint(1)
    vertices(6) = startPoint(0): vertices(7) = endPoint(1)
    
    Set rectObj = acadDoc.ModelSpace.AddLightWeightPolyline(vertices)
    rectObj.Closed = True
    acadDoc.Regen True
End Sub
```

**图层操作：**
```vba
' 创建图层
Sub CreateLayer()
    Dim acadApp As AcadApplication
    Dim acadDoc As AcadDocument
    Dim layerObj As AcadLayer
    
    Set acadApp = GetObject(, "AutoCAD.Application")
    Set acadDoc = acadApp.ActiveDocument
    
    ' 检查图层是否已存在
    On Error Resume Next
    Set layerObj = acadDoc.Layers.Add("MyLayer")
    
    If Err.Number = 0 Then
        ' 设置图层属性
        layerObj.Color = acRed
        layerObj.LineType = "Continuous"
        layerObj.LayerOn = True
        layerObj.Plottable = True
        MsgBox "图层创建成功！"
    Else
        MsgBox "图层已存在！"
    End If
    
    On Error GoTo 0
End Sub

' 设置当前图层
Sub SetCurrentLayer()
    Dim acadApp As AcadApplication
    Dim acadDoc As AcadDocument
    
    Set acadApp = GetObject(, "AutoCAD.Application")
    Set acadDoc = acadApp.ActiveDocument
    
    acadDoc.ActiveLayer = acadDoc.Layers("MyLayer")
    MsgBox "当前图层已设置为MyLayer"
End Sub
```

## 2.2.4 用户界面交互

**创建用户窗体：**
```vba
' 在用户窗体中添加控件
' 窗体名称：frmDrawingTools
' 控件：按钮(btnDrawLine), 文本框(txtLength), 标签(lblLength)

Private Sub btnDrawLine_Click()
    Dim acadApp As AcadApplication
    Dim acadDoc As AcadDocument
    Dim length As Double
    Dim startPoint(0 To 2) As Double
    Dim endPoint(0 To 2) As Double
    Dim lineObj As AcadLine
    
    ' 验证输入
    If Not IsNumeric(txtLength.Text) Then
        MsgBox "请输入有效的数字！"
        Exit Sub
    End If
    
    length = CDbl(txtLength.Text)
    
    ' 绘制直线
    Set acadApp = GetObject(, "AutoCAD.Application")
    Set acadDoc = acadApp.ActiveDocument
    
    startPoint(0) = 0: startPoint(1) = 0: startPoint(2) = 0
    endPoint(0) = length: endPoint(1) = 0: endPoint(2) = 0
    
    Set lineObj = acadDoc.ModelSpace.AddLine(startPoint, endPoint)
    acadDoc.Regen True
    
    MsgBox "直线绘制完成！长度：" & length
End Sub

' 显示窗体
Sub ShowDrawingTools()
    frmDrawingTools.Show
End Sub
```

**输入框和消息框：**
```vba
' 获取用户输入
Sub GetUserInput()
    Dim acadApp As AcadApplication
    Dim acadDoc As AcadDocument
    Dim length As Double
    Dim response As VbMsgBoxResult
    
    ' 使用输入框获取数据
    length = InputBox("请输入直线长度：", "绘制直线", "100")
    
    If length = "" Then
        Exit Sub ' 用户取消
    End If
    
    If Not IsNumeric(length) Then
        MsgBox "请输入有效的数字！", vbExclamation
        Exit Sub
    End If
    
    ' 确认操作
    response = MsgBox("确定要绘制长度为 " & length & " 的直线吗？", 
                     vbQuestion + vbYesNo, "确认")
    
    If response = vbYes Then
        ' 绘制直线
        Set acadApp = GetObject(, "AutoCAD.Application")
        Set acadDoc = acadApp.ActiveDocument
        
        Dim startPoint(0 To 2) As Double
        Dim endPoint(0 To 2) As Double
        Dim lineObj As AcadLine
        
        startPoint(0) = 0: startPoint(1) = 0: startPoint(2) = 0
        endPoint(0) = CDbl(length): endPoint(1) = 0: endPoint(2) = 0
        
        Set lineObj = acadDoc.ModelSpace.AddLine(startPoint, endPoint)
        acadDoc.Regen True
        
        MsgBox "直线绘制完成！", vbInformation
    End If
End Sub
```

---

**CURSOR提示词模板：**
```
请生成一个VBA宏，实现[具体功能]，要求：
1. 使用AutoCAD VBA对象模型
2. 包含错误处理
3. 提供用户界面交互
4. 符合VBA编程规范
```