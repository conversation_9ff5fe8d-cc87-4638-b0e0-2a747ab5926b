import { RPAEngine, ConnectionConfig } from '../../types';
import { RPAConnection, RPAEngineDetector } from './engine-detector';
import { createLogger } from '../../utils/logger';
import EventEmitter from 'events';

export interface ConnectionEvent {
  type: 'connected' | 'disconnected' | 'error';
  engineId: string;
  error?: string;
}

export class RPAConnectionManager extends EventEmitter {
  private connections: Map<string, RPAConnection> = new Map();
  private engines: Map<string, RPAEngine> = new Map();
  private detector = new RPAEngineDetector();
  private logger = createLogger('RPAConnectionManager');
  private reconnectTimers: Map<string, NodeJS.Timeout> = new Map();

  constructor() {
    super();
  }

  async initialize(): Promise<void> {
    this.logger.info('Initializing RPA connection manager...');
    
    try {
      const localEngines = await this.detector.detectLocalEngines();
      
      for (const engine of localEngines) {
        await this.addEngine(engine);
      }
      
      this.logger.info(`Initialized with ${localEngines.length} engines`);
    } catch (error) {
      this.logger.error('Failed to initialize connection manager:', error);
      throw error;
    }
  }

  async addEngine(engine: RPAEngine): Promise<void> {
    try {
      const isValid = await this.detector.validateEngine(engine);
      if (!isValid) {
        throw new Error(`Engine ${engine.id} is not valid or accessible`);
      }

      const config: ConnectionConfig = {
        host: new URL(engine.endpoint).hostname,
        port: parseInt(new URL(engine.endpoint).port) || 80,
        timeout: 30000,
        retryAttempts: 3
      };

      const connection = new RPAConnection(config);
      this.connections.set(engine.id, connection);
      this.engines.set(engine.id, engine);

      this.emit('connection', {
        type: 'connected',
        engineId: engine.id
      });

      this.logger.info(`Added engine: ${engine.name} (${engine.id})`);
    } catch (error) {
      this.logger.error(`Failed to add engine ${engine.id}:`, error);
      this.emit('connection', {
        type: 'error',
        engineId: engine.id,
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  async removeEngine(engineId: string): Promise<void> {
    const connection = this.connections.get(engineId);
    if (connection) {
      this.clearReconnectTimer(engineId);
      this.connections.delete(engineId);
      this.engines.delete(engineId);

      this.emit('connection', {
        type: 'disconnected',
        engineId: engineId
      });

      this.logger.info(`Removed engine: ${engineId}`);
    }
  }

  getConnection(engineId: string): RPAConnection | undefined {
    return this.connections.get(engineId);
  }

  getEngine(engineId: string): RPAEngine | undefined {
    return this.engines.get(engineId);
  }

  getAllEngines(): RPAEngine[] {
    return Array.from(this.engines.values());
  }

  getAvailableEngines(): RPAEngine[] {
    return this.getAllEngines().filter(engine => engine.status === 'running');
  }

  async testEngine(engineId: string): Promise<boolean> {
    const connection = this.connections.get(engineId);
    if (!connection) {
      return false;
    }

    try {
      const isConnected = await connection.testConnection();
      const engine = this.engines.get(engineId);
      
      if (engine) {
        engine.status = isConnected ? 'running' : 'error';
        this.engines.set(engineId, engine);
      }

      if (!isConnected) {
        this.scheduleReconnect(engineId);
      }

      return isConnected;
    } catch (error) {
      this.logger.error(`Engine test failed for ${engineId}:`, error);
      this.scheduleReconnect(engineId);
      return false;
    }
  }

  async refreshEngines(): Promise<void> {
    this.logger.info('Refreshing engine list...');
    
    try {
      const localEngines = await this.detector.detectLocalEngines();
      const currentEngineIds = new Set(this.engines.keys());
      
      for (const engine of localEngines) {
        if (!currentEngineIds.has(engine.id)) {
          await this.addEngine(engine);
        }
      }
      
      for (const engineId of currentEngineIds) {
        const exists = localEngines.some(e => e.id === engineId);
        if (!exists) {
          await this.removeEngine(engineId);
        }
      }
    } catch (error) {
      this.logger.error('Failed to refresh engines:', error);
    }
  }

  private scheduleReconnect(engineId: string): void {
    this.clearReconnectTimer(engineId);
    
    const timer = setTimeout(async () => {
      try {
        await this.testEngine(engineId);
      } catch (error) {
        this.logger.error(`Reconnect failed for ${engineId}:`, error);
      }
    }, 30000); // 30 seconds
    
    this.reconnectTimers.set(engineId, timer);
  }

  private clearReconnectTimer(engineId: string): void {
    const timer = this.reconnectTimers.get(engineId);
    if (timer) {
      clearTimeout(timer);
      this.reconnectTimers.delete(engineId);
    }
  }

  dispose(): void {
    for (const timer of this.reconnectTimers.values()) {
      clearTimeout(timer);
    }
    this.reconnectTimers.clear();
    this.connections.clear();
    this.engines.clear();
    this.removeAllListeners();
  }
}