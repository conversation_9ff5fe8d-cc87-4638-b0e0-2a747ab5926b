#!/usr/bin/env python3
"""
影刀RPA引擎 Python 客户端示例

这个示例展示了如何使用Python与影刀RPA引擎进行交互
"""

import requests
import json
import time
import websocket
import threading
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from urllib.parse import urljoin

@dataclass
class ProcessInfo:
    id: str
    name: str
    description: str
    status: str
    created: str
    modified: str
    variables: Dict[str, Any]

@dataclass
class ExecutionInfo:
    id: str
    process_id: str
    status: str
    start_time: str
    parameters: Dict[str, Any]
    debug_mode: bool
    progress: Dict[str, Any]
    logs: List[Dict[str, Any]]

class RPAEngineClient:
    def __init__(self, base_url: str = "http://localhost:8080"):
        self.base_url = base_url
        self.ws = None
        self.event_handlers = {}
        self.ws_thread = None

    def _request(self, method: str, endpoint: str, **kwargs) -> Dict[str, Any]:
        """发送HTTP请求"""
        url = urljoin(self.base_url, endpoint)
        headers = kwargs.get('headers', {})
        headers['Content-Type'] = 'application/json'
        kwargs['headers'] = headers

        try:
            response = requests.request(method, url, **kwargs)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            print(f"请求失败: {e}")
            raise

    def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        return self._request('GET', '/health')

    def get_engine_info(self) -> Dict[str, Any]:
        """获取引擎信息"""
        return self._request('GET', '/info')

    def get_processes(self, **filters) -> Dict[str, Any]:
        """获取所有流程"""
        params = '&'.join([f"{k}={v}" for k, v in filters.items()])
        endpoint = f"/processes?{params}" if params else "/processes"
        return self._request('GET', endpoint)

    def create_process(self, name: str, description: str = "", variables: Dict[str, Any] = None) -> ProcessInfo:
        """创建流程"""
        data = {
            "name": name,
            "description": description,
            "variables": variables or {}
        }
        response = self._request('POST', '/processes', json=data)
        return ProcessInfo(**response)

    def get_process(self, process_id: str) -> ProcessInfo:
        """获取特定流程"""
        response = self._request('GET', f'/processes/{process_id}')
        return ProcessInfo(**response)

    def update_process(self, process_id: str, **update_data) -> ProcessInfo:
        """更新流程"""
        response = self._request('PUT', f'/processes/{process_id}', json=update_data)
        return ProcessInfo(**response)

    def delete_process(self, process_id: str) -> None:
        """删除流程"""
        self._request('DELETE', f'/processes/{process_id}')

    def execute_process(self, process_id: str, parameters: Dict[str, Any] = None, 
                       debug_mode: bool = False, timeout: int = 300000) -> Dict[str, Any]:
        """执行流程"""
        data = {
            "parameters": parameters or {},
            "debugMode": debug_mode,
            "timeout": timeout
        }
        return self._request('POST', f'/processes/{process_id}/execute', json=data)

    def get_execution_status(self, execution_id: str) -> ExecutionInfo:
        """获取执行状态"""
        response = self._request('GET', f'/executions/{execution_id}')
        return ExecutionInfo(**response)

    def stop_execution(self, execution_id: str) -> Dict[str, Any]:
        """停止执行"""
        return self._request('POST', f'/executions/{execution_id}/stop')

    def get_process_variables(self, process_id: str) -> Dict[str, Any]:
        """获取流程变量"""
        return self._request('GET', f'/processes/{process_id}/variables')

    def update_process_variables(self, process_id: str, variables: Dict[str, Any]) -> Dict[str, Any]:
        """更新流程变量"""
        data = {"variables": variables}
        return self._request('PUT', f'/processes/{process_id}/variables', json=data)

    def connect_websocket(self):
        """连接WebSocket"""
        ws_url = f"ws://{self.base_url.replace('http://', '')}"
        
        def on_message(ws, message):
            try:
                data = json.loads(message)
                self._handle_websocket_message(data)
            except json.JSONDecodeError as e:
                print(f"解析WebSocket消息失败: {e}")

        def on_error(ws, error):
            print(f"WebSocket错误: {error}")

        def on_close(ws, close_status_code, close_msg):
            print("WebSocket连接关闭")

        def on_open(ws):
            print("WebSocket连接已建立")

        self.ws = websocket.WebSocketApp(
            ws_url,
            on_message=on_message,
            on_error=on_error,
            on_close=on_close,
            on_open=on_open
        )

        # 在单独的线程中运行WebSocket
        self.ws_thread = threading.Thread(target=self.ws.run_forever)
        self.ws_thread.daemon = True
        self.ws_thread.start()

    def disconnect_websocket(self):
        """断开WebSocket连接"""
        if self.ws:
            self.ws.close()
            self.ws = None

    def send_websocket_message(self, message: Dict[str, Any]):
        """发送WebSocket消息"""
        if self.ws:
            self.ws.send(json.dumps(message))
        else:
            print("WebSocket未连接")

    def on(self, event_type: str, handler):
        """注册事件处理器"""
        if event_type not in self.event_handlers:
            self.event_handlers[event_type] = []
        self.event_handlers[event_type].append(handler)

    def _handle_websocket_message(self, data: Dict[str, Any]):
        """处理WebSocket消息"""
        event_type = data.get('type')
        if event_type in self.event_handlers:
            for handler in self.event_handlers[event_type]:
                try:
                    handler(data)
                except Exception as e:
                    print(f"事件处理器错误: {e}")

def main():
    """主示例函数"""
    client = RPAEngineClient()

    print("=== 影刀RPA引擎 Python 客户端示例 ===\n")

    try:
        # 1. 健康检查
        print("1. 健康检查...")
        health = client.health_check()
        print(f"✅ 引擎状态: {health['status']}")
        print(f"✅ 运行时间: {health['uptime']:.1f} 秒\n")

        # 2. 获取引擎信息
        print("2. 获取引擎信息...")
        info = client.get_engine_info()
        print(f"✅ 引擎名称: {info['name']}")
        print(f"✅ 引擎版本: {info['version']}")
        print(f"✅ 支持功能: {', '.join(info['capabilities'])}\n")

        # 3. 创建流程
        print("3. 创建新流程...")
        
        # Web数据提取流程
        web_process = client.create_process(
            name="Web数据提取示例",
            description="从目标网站提取数据的示例流程",
            variables={
                "targetUrl": "https://example.com",
                "selectors": ["title", "content", "price"],
                "outputFormat": "json",
                "maxItems": 50
            }
        )
        print(f"✅ 创建流程: {web_process.name} ({web_process.id})")

        # 文件处理流程
        file_process = client.create_process(
            name="文件处理示例",
            description="批量处理文件的示例流程",
            variables={
                "inputPath": "./input",
                "outputPath": "./output",
                "fileTypes": ["pdf", "xlsx", "csv"],
                "ocrEnabled": True
            }
        )
        print(f"✅ 创建流程: {file_process.name} ({file_process.id})")

        # API数据同步流程
        api_process = client.create_process(
            name="API数据同步示例",
            description="同步API数据的示例流程",
            variables={
                "sourceApi": "https://api.example.com/data",
                "targetApi": "https://internal.company.com/data",
                "syncInterval": 3600,
                "apiKey": "demo_key"
            }
        )
        print(f"✅ 创建流程: {api_process.name} ({api_process.id})\n")

        # 4. 获取所有流程
        print("4. 获取所有流程...")
        processes = client.get_processes()
        print(f"✅ 当前流程数量: {len(processes['processes'])}")
        for process in processes['processes']:
            print(f"   - {process['name']} ({process['id']}) - 状态: {process['status']}")
        print()

        # 5. 更新流程变量
        print("5. 更新流程变量...")
        client.update_process_variables(
            web_process.id,
            {
                "timeout": 60000,
                "retryCount": 3,
                "userAgent": "Mozilla/5.0 (compatible; RPA-Bot/1.0)"
            }
        )
        print("✅ 流程变量更新成功")

        updated_vars = client.get_process_variables(web_process.id)
        print(f"✅ 更新后的变量: {list(updated_vars.keys())}\n")

        # 6. 连接WebSocket
        print("6. 连接WebSocket...")
        client.connect_websocket()
        time.sleep(1)  # 等待连接建立

        # 设置事件监听器
        def on_execution_started(data):
            print(f"🚀 执行开始: {data['executionId']}")

        def on_execution_progress(data):
            progress = data['progress']['percentage']
            activity = data['progress']['currentActivity']
            print(f"📊 执行进度: {progress}% - {activity}")

        def on_execution_completed(data):
            result = data['result']
            print("✅ 执行完成!")
            print(f"   执行时间: {result['executionTime']}ms")
            if 'output' in result:
                print(f"   处理结果: {result['output']}")

        def on_execution_error(data):
            print(f"❌ 执行错误: {data['error']}")

        client.on('execution_started', on_execution_started)
        client.on('execution_progress', on_execution_progress)
        client.on('execution_completed', on_execution_completed)
        client.on('execution_error', on_execution_error)

        # 7. 执行流程
        print("7. 执行流程...")
        execution = client.execute_process(
            web_process.id,
            parameters={
                "action": "scrape",
                "saveToDatabase": True,
                "maxPages": 10
            },
            debug_mode=True
        )
        print(f"✅ 流程执行启动: {execution['executionId']}")

        # 8. 监控执行状态
        print("8. 监控执行状态...")
        execution_id = execution['executionId']
        
        for i in range(15):  # 最多等待30秒
            time.sleep(2)
            status = client.get_execution_status(execution_id)
            print(f"   状态: {status.status}, 进度: {status.progress.get('percentage', 0)}%")
            
            if status.status in ['completed', 'error', 'stopped']:
                break
        
        print()

        # 9. 批量操作示例
        print("9. 批量操作示例...")
        
        # 创建多个相似流程
        batch_templates = [
            ("价格监控-Amazon", "监控Amazon商品价格", {"site": "amazon", "currency": "USD"}),
            ("价格监控-eBay", "监控eBay商品价格", {"site": "ebay", "currency": "USD"}),
            ("价格监控-AliExpress", "监控AliExpress商品价格", {"site": "aliexpress", "currency": "USD"}),
        ]
        
        batch_processes = []
        for name, desc, vars in batch_templates:
            process = client.create_process(
                name=name,
                description=desc,
                variables={
                    **vars,
                    "checkInterval": 3600,
                    "notificationEmail": "<EMAIL>"
                }
            )
            batch_processes.append(process)
            print(f"✅ 批量创建: {process.name}")
        
        print(f"✅ 批量创建完成，共 {len(batch_processes)} 个流程\n")

        # 10. 并发执行示例
        print("10. 并发执行示例...")
        import concurrent.futures
        
        def execute_process_async(process_info):
            try:
                execution = client.execute_process(
                    process_info.id,
                    parameters={"test": True},
                    debug_mode=False
                )
                return execution['executionId']
            except Exception as e:
                print(f"执行失败 {process_info.name}: {e}")
                return None

        # 使用线程池并发执行
        with concurrent.futures.ThreadPoolExecutor(max_workers=3) as executor:
            future_to_process = {
                executor.submit(execute_process_async, process): process 
                for process in batch_processes[:2]  # 只执行前2个作为示例
            }
            
            executions = []
            for future in concurrent.futures.as_completed(future_to_process):
                process = future_to_process[future]
                try:
                    exec_id = future.result()
                    if exec_id:
                        executions.append(exec_id)
                        print(f"✅ 并发执行启动: {process.name} -> {exec_id}")
                except Exception as e:
                    print(f"❌ 并发执行失败: {process.name}")
        
        print(f"✅ 并发执行完成，共启动 {len(executions)} 个执行\n")

        # 11. 清理资源
        print("11. 清理测试流程...")
        
        all_process_ids = [web_process.id, file_process.id, api_process.id]
        all_process_ids.extend([p.id for p in batch_processes])
        
        for process_id in all_process_ids:
            try:
                client.delete_process(process_id)
                print(f"✅ 删除流程: {process_id}")
            except Exception as e:
                print(f"❌ 删除失败 {process_id}: {e}")

        # 断开WebSocket连接
        client.disconnect_websocket()
        print("✅ WebSocket连接已关闭\n")

        print("🎉 所有示例执行完成!")

    except Exception as e:
        print(f"❌ 示例执行失败: {e}")
        import traceback
        traceback.print_exc()

def monitor_execution(client: RPAEngineClient, execution_id: str, timeout: int = 30):
    """监控执行状态的辅助函数"""
    start_time = time.time()
    
    while time.time() - start_time < timeout:
        try:
            status = client.get_execution_status(execution_id)
            
            if status.status == 'completed':
                print(f"✅ 执行完成! 耗时: {status.progress.get('percentage', 0)}%")
                return True
            elif status.status in ['error', 'stopped']:
                print(f"❌ 执行失败: {status.status}")
                return False
            
            print(f"   监控中: {status.status} - {status.progress.get('percentage', 0)}%")
            time.sleep(2)
            
        except Exception as e:
            print(f"监控失败: {e}")
            return False
    
    print("⏰ 监控超时")
    return False

def batch_create_processes() -> List[ProcessInfo]:
    """批量创建流程的示例"""
    client = RPAEngineClient()
    
    templates = [
        {
            "name": "电商价格监控",
            "description": "监控电商网站商品价格变化",
            "variables": {
                "urls": ["https://example.com/product1", "https://example.com/product2"],
                "priceSelector": ".price",
                "notifyThreshold": 0.1
            }
        },
        {
            "name": "社交媒体数据收集",
            "description": "收集社交媒体平台数据",
            "variables": {
                "platforms": ["twitter", "facebook", "instagram"],
                "keywords": ["AI", "automation", "RPA"],
                "maxPosts": 100
            }
        },
        {
            "name": "财务报表处理",
            "description": "自动化处理财务报表",
            "variables": {
                "inputFolder": "./financial_reports",
                "outputFolder": "./processed_reports",
                "template": "standard_template"
            }
        }
    ]
    
    created_processes = []
    
    try:
        print("批量创建流程...")
        for template in templates:
            process = client.create_process(**template)
            created_processes.append(process)
            print(f"✅ 创建流程: {process.name}")
        
        print(f"✅ 批量创建完成，共创建 {len(created_processes)} 个流程")
        return created_processes
        
    except Exception as e:
        print(f"批量创建失败: {e}")
        raise

if __name__ == "__main__":
    main()