# CURSOR辅助开发

## 2.3.1 使用CURSOR生成AutoLISP代码

**CURSOR提示词模板：**
```
请生成一个AutoLISP函数，用于[具体功能描述]，要求：
1. 函数名：[函数名]
2. 参数：[参数列表]
3. 功能：[详细功能描述]
4. 返回值：[返回值描述]
5. 错误处理：[错误处理要求]
```

**实际示例：**
```
请生成一个AutoLISP函数，用于批量绘制圆孔，要求：
1. 函数名：draw-multiple-holes
2. 参数：中心点列表、半径、数量
3. 功能：在指定位置绘制指定数量的圆孔
4. 返回值：绘制的圆孔数量
5. 错误处理：检查输入参数有效性
```

**生成的代码示例：**
```lisp
(defun draw-multiple-holes (center-points radius hole-count / i count)
  ; 错误检查
  (if (or (null center-points) (null radius) (null hole-count))
    (progn
      (princ "\n错误：参数不能为空")
      (exit)
    )
  )
  
  (if (<= radius 0)
    (progn
      (princ "\n错误：半径必须大于0")
      (exit)
    )
  )
  
  (if (<= hole-count 0)
    (progn
      (princ "\n错误：数量必须大于0")
      (exit)
    )
  )
  
  ; 设置图层
  (command "LAYER" "M" "HOLES" "C" "1" "" "")
  
  ; 绘制圆孔
  (setq i 0)
  (setq count 0)
  (while (< i hole-count)
    (if (nth i center-points)
      (progn
        (command "CIRCLE" (nth i center-points) radius)
        (setq count (1+ count))
      )
    )
    (setq i (1+ i))
  )
  
  (princ (strcat "\n成功绘制 " (itoa count) " 个圆孔"))
  (princ)
)
```

## 2.3.2 CURSOR优化VBA脚本

**CURSOR优化提示词：**
```
请优化以下VBA代码，提高性能和可读性：
[原始代码]

优化要求：
1. 减少重复代码
2. 添加错误处理
3. 提高执行效率
4. 改善代码结构
```

**优化示例：**
```vba
' 原始代码
Sub DrawMultipleLines()
    Dim acadApp As AcadApplication
    Dim acadDoc As AcadDocument
    Dim lineObj As AcadLine
    
    Set acadApp = GetObject(, "AutoCAD.Application")
    Set acadDoc = acadApp.ActiveDocument
    
    ' 重复的代码
    Set lineObj = acadDoc.ModelSpace.AddLine(Array(0, 0, 0), Array(100, 0, 0))
    Set lineObj = acadDoc.ModelSpace.AddLine(Array(0, 100, 0), Array(100, 100, 0))
    Set lineObj = acadDoc.ModelSpace.AddLine(Array(0, 200, 0), Array(100, 200, 0))
    Set lineObj = acadDoc.ModelSpace.AddLine(Array(0, 300, 0), Array(100, 300, 0))
    
    acadDoc.Regen True
End Sub

' CURSOR优化后的代码
Sub DrawMultipleLines_Optimized()
    On Error GoTo ErrorHandler
    
    Dim acadApp As AcadApplication
    Dim acadDoc As AcadDocument
    Dim points As Variant
    Dim i As Integer
    Dim lineObj As AcadLine
    
    ' 定义线条起点和终点
    points = Array( _
        Array(Array(0, 0, 0), Array(100, 0, 0)), _
        Array(Array(0, 100, 0), Array(100, 100, 0)), _
        Array(Array(0, 200, 0), Array(100, 200, 0)), _
        Array(Array(0, 300, 0), Array(100, 300, 0)) _
    )
    
    Set acadApp = GetObject(, "AutoCAD.Application")
    Set acadDoc = acadApp.ActiveDocument
    
    ' 批量绘制线条
    For i = LBound(points) To UBound(points)
        Set lineObj = acadDoc.ModelSpace.AddLine(points(i)(0), points(i)(1))
    Next i
    
    acadDoc.Regen True
    MsgBox "成功绘制 " & (UBound(points) - LBound(points) + 1) & " 条直线", vbInformation
    Exit Sub
    
ErrorHandler:
    MsgBox "错误 " & Err.Number & ": " & Err.Description, vbCritical
End Sub
```

## 2.3.3 代码调试和错误处理

**CURSOR调试提示词：**
```
请为以下代码添加调试功能：
[原始代码]

要求：
1. 添加变量监视
2. 添加断点标记
3. 添加调试输出
4. 添加错误处理
```

**调试增强示例：**
```lisp
(defun draw-polygon-with-debug (center radius sides / angle i points)
  ; 调试信息
  (princ "\n=== 开始绘制多边形 ===")
  (princ (strcat "\n中心点: " (rtos (car center) 2 2) "," (rtos (cadr center) 2 2)))
  (princ (strcat "\n半径: " (rtos radius 2 2)))
  (princ (strcat "\n边数: " (itoa sides)))
  
  ; 参数验证
  (if (or (null center) (null radius) (null sides))
    (progn
      (princ "\n错误：参数不能为空")
      (exit)
    )
  )
  
  (if (<= radius 0)
    (progn
      (princ "\n错误：半径必须大于0")
      (exit)
    )
  )
  
  (if (< sides 3)
    (progn
      (princ "\n错误：边数必须大于等于3")
      (exit)
    )
  )
  
  ; 计算多边形顶点
  (setq angle (/ (* 2 pi) sides))
  (setq i 0)
  (setq points '())
  
  (while (< i sides)
    (setq x (+ (car center) (* radius (cos (* i angle)))))
    (setq y (+ (cadr center) (* radius (sin (* i angle)))))
    (setq points (cons (list x y 0) points))
    (setq i (1+ i))
    
    ; 调试输出
    (princ (strcat "\n顶点 " (itoa i) ": (" (rtos x 2 2) "," (rtos y 2 2) ")"))
  )
  
  ; 反转列表以保持正确的顺序
  (setq points (reverse points))
  
  ; 绘制多边形
  (command "PLINE")
  (foreach pt points
    (command pt)
  )
  (command "C")
  
  (princ "\n=== 多边形绘制完成 ===")
  (princ)
)
```

## 2.3.4 最佳实践和规范

**CURSOR代码规范提示词：**
```
请为以下代码添加规范注释和优化结构：
[原始代码]

要求：
1. 添加函数文档
2. 规范变量命名
3. 添加代码注释
4. 优化代码结构
```

**规范代码示例：**
```lisp
;;; =========================================================================
;;; 函数名: create-standard-titleblock
;;; 描述:   创建标准A3图框和标题栏
;;; 参数:   insertion-point - 插入点坐标 (x y z)
;;;         scale          - 缩放比例
;;;         drawing-title  - 图纸标题
;;;         drawing-number - 图号
;;; 返回值: 成功返回T，失败返回nil
;;; 示例:   (create-standard-titleblock (list 0 0 0) 1.0 "建筑平面图" "A-001")
;;; =========================================================================

(defun create-standard-titleblock (insertion-point scale drawing-title drawing-number 
                                  / titleblock-layer title-layer border-layer 
                                  frame-width frame-height text-height)
  ; 变量初始化
  (setq frame-width (* 420 scale))    ; A3图纸宽度
  (setq frame-height (* 297 scale))   ; A3图纸高度
  (setq text-height (* 3.5 scale))   ; 标准字高
  
  ; 创建图层
  (if (not (tblsearch "LAYER" "TITLEBLOCK"))
    (command "LAYER" "N" "TITLEBLOCK" "C" "7" "TITLEBLOCK" "")
  )
  
  (if (not (tblsearch "LAYER" "TITLE"))
    (command "LAYER" "N" "TITLE" "C" "3" "TITLE" "")
  )
  
  (if (not (tblsearch "LAYER" "BORDER"))
    (command "LAYER" "N" "BORDER" "C" "1" "BORDER" "")
  )
  
  ; 设置当前图层
  (setvar "CLAYER" "TITLEBLOCK")
  
  ; 绘制外边框
  (command "RECTANG" insertion-point 
           (list (+ (car insertion-point) frame-width) 
                 (+ (cadr insertion-point) frame-height)))
  
  ; 绘制内边框
  (command "LAYER" "S" "BORDER" "")
  (command "RECTANG" 
           (list (+ (car insertion-point) (* 25 scale)) 
                 (+ (cadr insertion-point) (* 10 scale)))
           (list (+ (car insertion-point) frame-width (* -10 scale)) 
                 (+ (cadr insertion-point) frame-height (* -10 scale))))
  
  ; 绘制标题栏
  (command "LAYER" "S" "TITLE" "")
  (command "RECTANG" 
           (list (+ (car insertion-point) frame-width (* -140 scale)) 
                 (+ (cadr insertion-point) (* 10 scale)))
           (list (+ (car insertion-point) frame-width (* -10 scale)) 
                 (+ (cadr insertion-point) (* 40 scale))))
  
  ; 添加标题文本
  (command "TEXT" "J" "C" 
           (list (+ (car insertion-point) frame-width (* -75 scale)) 
                 (+ (cadr insertion-point) (* 25 scale))) 
           text-height 0 drawing-title)
  
  ; 添加图号文本
  (command "TEXT" "J" "C" 
           (list (+ (car insertion-point) frame-width (* -30 scale)) 
                 (+ (cadr insertion-point) (* 15 scale))) 
           (* text-height 0.7) 0 drawing-number)
  
  ; 恢复当前图层
  (command "LAYER" "S" "0" "")
  
  (princ "\n标准图框创建完成")
  (princ)
)
```

---

**CURSOR提示词模板：**
```
请为AutoCAD自动化开发提供以下帮助：
1. 代码生成：根据需求生成AutoLISP/VBA代码
2. 代码优化：优化现有代码结构和性能
3. 调试支持：添加调试功能和错误处理
4. 最佳实践：提供代码规范和最佳实践建议
```