import axios from 'axios';

const ENGINE_BASE_URL = 'http://localhost:8080';

async function testEngineAPI() {
  console.log('Testing Yingdao RPA Engine API...\n');

  try {
    // Test health check
    console.log('1. Testing health check...');
    const healthResponse = await axios.get(`${ENGINE_BASE_URL}/health`);
    console.log('   ✅ Health check:', healthResponse.data);

    // Test engine info
    console.log('\n2. Testing engine info...');
    const infoResponse = await axios.get(`${ENGINE_BASE_URL}/info`);
    console.log('   ✅ Engine info:', infoResponse.data);

    // Test getting processes
    console.log('\n3. Testing get processes...');
    const processesResponse = await axios.get(`${ENGINE_BASE_URL}/processes`);
    console.log('   ✅ Processes:', processesResponse.data.processes.length, 'processes found');

    // Test creating a process
    console.log('\n4. Testing create process...');
    const createResponse = await axios.post(`${ENGINE_BASE_URL}/processes`, {
      name: 'Test Process',
      description: 'A test process created via API',
      variables: {
        testVar: 'testValue',
        count: 42
      }
    });
    console.log('   ✅ Process created:', createResponse.data.id);

    const processId = createResponse.data.id;

    // Test getting specific process
    console.log('\n5. Testing get specific process...');
    const getProcessResponse = await axios.get(`${ENGINE_BASE_URL}/processes/${processId}`);
    console.log('   ✅ Process details:', getProcessResponse.data.name);

    // Test executing process
    console.log('\n6. Testing process execution...');
    const executeResponse = await axios.post(`${ENGINE_BASE_URL}/processes/${processId}/execute`, {
      parameters: {
        inputData: 'test data'
      },
      debugMode: true
    });
    console.log('   ✅ Process execution started:', executeResponse.data.executionId);

    const executionId = executeResponse.data.executionId;

    // Wait a bit and check execution status
    console.log('\n7. Testing execution status...');
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    const executionResponse = await axios.get(`${ENGINE_BASE_URL}/executions/${executionId}`);
    console.log('   ✅ Execution status:', executionResponse.data.status);
    console.log('   ✅ Execution progress:', executionResponse.data.progress?.percentage || 0, '%');

    // Test getting process variables
    console.log('\n8. Testing process variables...');
    const variablesResponse = await axios.get(`${ENGINE_BASE_URL}/processes/${processId}/variables`);
    console.log('   ✅ Process variables:', variablesResponse.data);

    // Test updating process variables
    console.log('\n9. Testing update process variables...');
    await axios.put(`${ENGINE_BASE_URL}/processes/${processId}/variables`, {
      variables: {
        updatedVar: 'updatedValue',
        timestamp: new Date().toISOString()
      }
    });
    console.log('   ✅ Variables updated');

    // Test getting updated variables
    const updatedVariablesResponse = await axios.get(`${ENGINE_BASE_URL}/processes/${processId}/variables`);
    console.log('   ✅ Updated variables:', updatedVariablesResponse.data);

    // Test deleting process
    console.log('\n10. Testing delete process...');
    await axios.delete(`${ENGINE_BASE_URL}/processes/${processId}`);
    console.log('   ✅ Process deleted');

    // Verify process is deleted
    try {
      await axios.get(`${ENGINE_BASE_URL}/processes/${processId}`);
      console.log('   ❌ Process should have been deleted');
    } catch (error: any) {
      if (error.response?.status === 404) {
        console.log('   ✅ Process successfully deleted');
      } else {
        throw error;
      }
    }

    console.log('\n🎉 All tests passed! Yingdao RPA Engine API is working correctly.');

  } catch (error: any) {
    console.error('\n❌ Test failed:', error.response?.data || error.message);
    process.exit(1);
  }
}

// Run the tests
testEngineAPI();