"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CursorIntegration = exports.PluginLifecycleManager = exports.pluginActivator = exports.PluginActivator = void 0;
const lifecycle_manager_1 = require("./lifecycle-manager");
Object.defineProperty(exports, "PluginLifecycleManager", { enumerable: true, get: function () { return lifecycle_manager_1.PluginLifecycleManager; } });
const cursor_integration_1 = require("./cursor-integration");
Object.defineProperty(exports, "CursorIntegration", { enumerable: true, get: function () { return cursor_integration_1.CursorIntegration; } });
const logger_1 = require("../../utils/logger");
class PluginActivator {
    constructor() {
        this.logger = (0, logger_1.createLogger)('PluginActivator');
        this.lifecycleManager = new lifecycle_manager_1.PluginLifecycleManager();
        // For development, use mock API
        // In production, this would be the real Cursor API
        const cursorAPI = (0, cursor_integration_1.createMockCursorAPI)();
        this.cursorIntegration = new cursor_integration_1.CursorIntegration(cursorAPI);
    }
    async activate(context) {
        try {
            this.logger.info('Starting plugin activation...');
            // Activate the plugin lifecycle
            await this.lifecycleManager.activate(context);
            // Initialize Cursor integration
            await this.cursorIntegration.initialize();
            this.logger.info('Plugin activated successfully');
        }
        catch (error) {
            this.logger.error('Plugin activation failed:', error);
            throw error;
        }
    }
    async deactivate() {
        try {
            this.logger.info('Starting plugin deactivation...');
            // Clean up Cursor integration
            await this.cursorIntegration.dispose();
            // Deactivate the plugin lifecycle
            await this.lifecycleManager.deactivate();
            this.logger.info('Plugin deactivated successfully');
        }
        catch (error) {
            this.logger.error('Plugin deactivation failed:', error);
            throw error;
        }
    }
    getLifecycleManager() {
        return this.lifecycleManager;
    }
    getCursorIntegration() {
        return this.cursorIntegration;
    }
    getState() {
        return this.lifecycleManager.getState();
    }
    isActive() {
        return this.lifecycleManager.isActive();
    }
}
exports.PluginActivator = PluginActivator;
// Export singleton instance
exports.pluginActivator = new PluginActivator();
//# sourceMappingURL=index.js.map