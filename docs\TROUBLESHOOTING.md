# 故障排除指南

## 常见问题及解决方案

### 🚀 启动问题

#### 1. 端口占用

**问题**: 启动时出现 "EADDRINUSE: address already in use :::8080" 错误

**解决方案**:
```bash
# 检查端口占用
netstat -an | grep 8080
# 或
lsof -i :8080

# 更改端口（在config.json中）
{
  "settings": {
    "rpaEngine": {
      "port": 8081
    }
  }
}

# 或停止占用端口的进程
kill -9 <PID>
```

#### 2. 依赖安装失败

**问题**: npm install 时出现依赖错误

**解决方案**:
```bash
# 清理缓存
npm cache clean --force

# 删除 node_modules 和 package-lock.json
rm -rf node_modules package-lock.json

# 重新安装
npm install

# 如果仍有问题，使用 --legacy-peer-deps
npm install --legacy-peer-deps
```

#### 3. 权限问题

**问题**: 文件权限相关错误

**解决方案**:
```bash
# 检查文件权限
ls -la

# 设置正确的权限
chmod 755 src/
chmod 644 config.json

# 创建必要的目录
mkdir -p logs data temp
chmod 755 logs data temp
```

### 🔌 连接问题

#### 1. 引擎连接失败

**问题**: 无法连接到RPA引擎服务

**诊断步骤**:
```bash
# 检查引擎服务状态
curl http://localhost:8080/health

# 检查服务是否运行
ps aux | grep node

# 检查网络连接
telnet localhost 8080
```

**解决方案**:
```bash
# 启动引擎服务
npm run dev:engine

# 或检查配置
cat config.json
```

#### 2. WebSocket连接失败

**问题**: WebSocket无法建立连接

**解决方案**:
```javascript
// 检查WebSocket URL
const ws = new WebSocket('ws://localhost:8080');

// 添加错误处理
ws.onerror = (error) => {
  console.error('WebSocket错误:', error);
};

// 添加重连逻辑
function connectWebSocket() {
  const ws = new WebSocket('ws://localhost:8080');
  
  ws.onclose = () => {
    console.log('连接关闭，3秒后重连...');
    setTimeout(connectWebSocket, 3000);
  };
  
  return ws;
}
```

### 📊 执行问题

#### 1. 流程执行超时

**问题**: 流程执行时间过长，导致超时

**解决方案**:
```javascript
// 增加超时时间
const execution = await client.executeProcess(processId, {
  timeout: 600000, // 10分钟
  parameters: yourParameters
});

// 或分批处理大量数据
async function processLargeData(data, batchSize = 100) {
  for (let i = 0; i < data.length; i += batchSize) {
    const batch = data.slice(i, i + batchSize);
    await processBatch(batch);
    await delay(1000); // 批次间延迟
  }
}
```

#### 2. 流程卡住

**问题**: 流程状态一直是 "running"，但实际没有进展

**诊断步骤**:
```bash
# 检查执行状态
curl http://localhost:8080/executions/{executionId}

# 查看引擎日志
tail -f logs/combined.log

# 检查系统资源
top
# 或
htop
```

**解决方案**:
```javascript
// 停止卡住的执行
await client.stopExecution(executionId);

// 添加超时和重试机制
async function executeWithRetry(processId, parameters, maxRetries = 3) {
  for (let i = 0; i < maxRetries; i++) {
    try {
      const execution = await client.executeProcess(processId, {
        parameters,
        timeout: 300000
      });
      
      const result = await monitorExecution(execution.executionId, 600000);
      return result;
      
    } catch (error) {
      if (i === maxRetries - 1) throw error;
      
      console.log(`重试 ${i + 1}/${maxRetries}...`);
      await delay(5000 * (i + 1)); // 指数退避
    }
  }
}
```

#### 3. 内存不足

**问题**: 系统内存不足导致执行失败

**解决方案**:
```bash
# 检查内存使用
free -h
# 或
vmstat

# 增加Node.js内存限制
node --max-old-space-size=4096 dist/index.js

# 在package.json中配置
{
  "scripts": {
    "start": "node --max-old-space-size=4096 dist/index.js"
  }
}
```

### 📝 配置问题

#### 1. 配置文件错误

**问题**: 配置文件格式错误或缺少必需字段

**解决方案**:
```bash
# 验证JSON格式
cat config.json | python -m json.tool

# 或使用jq
cat config.json | jq .

# 恢复默认配置
cp config.example.json config.json
```

**示例配置**:
```json
{
  "id": "cursor-rpa-integration",
  "name": "Cursor RPA Integration",
  "version": "1.0.0",
  "enabled": true,
  "settings": {
    "rpaEngine": {
      "host": "localhost",
      "port": 8080,
      "timeout": 30000,
      "retryAttempts": 3
    },
    "ai": {
      "model": "gpt-3.5-turbo",
      "temperature": 0.7,
      "maxTokens": 1000,
      "timeout": 30000
    },
    "debug": {
      "enabled": false,
      "logLevel": "info"
    }
  }
}
```

#### 2. 环境变量问题

**问题**: 环境变量未正确设置

**解决方案**:
```bash
# 创建.env文件
cat > .env << EOF
RPA_ENGINE_HOST=localhost
RPA_ENGINE_PORT=8080
RPA_ENGINE_TIMEOUT=30000
LOG_LEVEL=info
DEBUG_MODE=false
EOF

# 加载环境变量
source .env

# 验证环境变量
echo $RPA_ENGINE_PORT
```

### 🔍 调试技巧

#### 1. 启用调试模式

```json
{
  "settings": {
    "debug": {
      "enabled": true,
      "logLevel": "debug"
    }
  }
}
```

#### 2. 使用开发工具

```bash
# 使用Node.js调试器
node --inspect dist/index.js

# 或使用VS Code调试
# 在.vscode/launch.json中配置
{
  "version": "0.2.0",
  "configurations": [
    {
      "type": "node",
      "request": "launch",
      "name": "Launch Program",
      "program": "${workspaceFolder}/dist/index.js"
    }
  ]
}
```

#### 3. 日志分析

```bash
# 实时查看日志
tail -f logs/combined.log

# 过滤错误日志
tail -f logs/combined.log | grep ERROR

# 搜索特定关键词
grep "execution_failed" logs/combined.log

# 统计错误类型
grep "ERROR" logs/combined.log | awk '{print $5}' | sort | uniq -c
```

### 🚨 性能问题

#### 1. 响应时间过长

**解决方案**:
```javascript
// 使用性能监控
const perf = {
  start: (label) => console.time(label),
  end: (label) => console.timeEnd(label)
};

// 优化数据库查询
async function getProcessWithCache(processId) {
  const cacheKey = `process_${processId}`;
  
  // 检查缓存
  if (cache.has(cacheKey)) {
    return cache.get(cacheKey);
  }
  
  perf.start('db_query');
  const process = await db.getProcess(processId);
  perf.end('db_query');
  
  // 设置缓存
  cache.set(cacheKey, process, 300000); // 5分钟缓存
  
  return process;
}
```

#### 2. 并发处理问题

**解决方案**:
```javascript
// 使用队列控制并发
class Queue {
  constructor(concurrency = 5) {
    this.concurrency = concurrency;
    this.running = 0;
    this.queue = [];
  }

  async add(task) {
    return new Promise((resolve, reject) => {
      this.queue.push({ task, resolve, reject });
      this.run();
    });
  }

  async run() {
    while (this.running < this.concurrency && this.queue.length > 0) {
      this.running++;
      const { task, resolve, reject } = this.queue.shift();
      
      try {
        const result = await task();
        resolve(result);
      } catch (error) {
        reject(error);
      } finally {
        this.running--;
        this.run();
      }
    }
  }
}

// 使用队列
const queue = new Queue(3); // 最大并发数

async function safeExecute(processId, parameters) {
  return queue.add(async () => {
    return await client.executeProcess(processId, parameters);
  });
}
```

### 🔄 数据恢复

#### 1. 流程数据丢失

**解决方案**:
```bash
# 备份流程数据
curl -s http://localhost:8080/processes > processes_backup.json

# 恢复流程数据
while read -r process; do
  echo "恢复流程: $(echo $process | jq -r '.name')"
  curl -X POST http://localhost:8080/processes \
    -H "Content-Type: application/json" \
    -d "$process"
done < processes_backup.json
```

#### 2. 配置文件恢复

```bash
# 备份配置
cp config.json config.json.backup

# 恢复配置
cp config.json.backup config.json
```

### 🌐 网络问题

#### 1. 代理设置

**解决方案**:
```bash
# 设置代理
export HTTP_PROXY=http://proxy.example.com:8080
export HTTPS_PROXY=http://proxy.example.com:8080

# 或在npm配置中设置
npm config set proxy http://proxy.example.com:8080
npm config set https-proxy http://proxy.example.com:8080
```

#### 2. 防火墙问题

**解决方案**:
```bash
# 检查防火墙状态
sudo ufw status

# 开放端口
sudo ufw allow 8080

# 或临时关闭防火墙
sudo ufw disable
```

### 📞 获取帮助

#### 1. 生成诊断报告

```bash
#!/bin/bash
# 诊断脚本

echo "=== 影刀RPA引擎诊断报告 ==="
echo "生成时间: $(date)"
echo

echo "1. 系统信息:"
uname -a
echo

echo "2. Node.js版本:"
node --version
npm --version
echo

echo "3. 磁盘使用:"
df -h
echo

echo "4. 内存使用:"
free -h
echo

echo "5. 端口占用:"
netstat -an | grep 8080
echo

echo "6. 进程状态:"
ps aux | grep node
echo

echo "7. 最近错误日志:"
tail -n 20 logs/error.log
echo

echo "8. 引擎健康状态:"
curl -s http://localhost:8080/health | jq .
echo
```

#### 2. 日志收集

```bash
# 收集所有日志
tar -czf logs_$(date +%Y%m%d_%H%M%S).tar.gz logs/

# 收集系统信息
system_profiler SPSoftwareDataType > system_info.txt  # macOS
# 或
dmesg > system_info.txt  # Linux

# 收集网络信息
netstat -an > network_info.txt
```

#### 3. 常见错误代码

| 错误代码 | 说明 | 解决方案 |
|---------|------|----------|
| ECONNREFUSED | 连接被拒绝 | 检查服务是否启动 |
| ETIMEDOUT | 连接超时 | 检查网络和防火墙 |
| EADDRINUSE | 地址正在使用 | 更改端口或停止占用进程 |
| ENOMEM | 内存不足 | 增加内存或优化代码 |
| EACCES | 权限不足 | 检查文件权限 |
| ENOENT | 文件不存在 | 检查文件路径 |

### 🎯 预防措施

#### 1. 定期维护

```bash
# 创建维护脚本
#!/bin/bash

# 清理旧日志
find logs -name "*.log" -mtime +7 -delete

# 清理临时文件
find temp -type f -mtime +1 -delete

# 重启服务
npm restart

# 发送维护报告
echo "维护完成于 $(date)" | mail -s "RPA引擎维护报告" <EMAIL>
```

#### 2. 监控设置

```javascript
// 健康检查监控
setInterval(async () => {
  try {
    const health = await fetch('http://localhost:8080/health');
    const data = await health.json();
    
    if (data.status !== 'healthy') {
      // 发送告警
      sendAlert('引擎状态异常: ' + data.status);
    }
  } catch (error) {
    sendAlert('引擎健康检查失败: ' + error.message);
  }
}, 60000); // 每分钟检查一次
```

通过以上故障排除指南，您应该能够解决大多数常见问题。如果问题仍然存在，请参考获取帮助部分生成诊断报告并联系技术支持。