import express from 'express';
import { createServer } from 'http';
import { Server } from 'ws';
import { createLogger } from '../../utils/logger';

export interface YingdaoEngineInfo {
  name: string;
  version: string;
  capabilities: string[];
  status: 'running' | 'stopped' | 'error';
}

export interface ProcessInfo {
  id: string;
  name: string;
  description: string;
  status: 'idle' | 'running' | 'completed' | 'error';
  created: Date;
  modified: Date;
  variables: Record<string, any>;
}

export class YingdaoRPAServer {
  private app: express.Application;
  private server: any;
  private wss: Server;
  private logger = createLogger('YingdaoRPAServer');
  private port: number;
  private processes: Map<string, ProcessInfo> = new Map();
  private activeExecutions: Map<string, any> = new Map();

  constructor(port: number = 8080) {
    this.port = port;
    this.app = express();
    this.server = createServer(this.app);
    this.wss = new Server({ server: this.server });
    
    this.setupMiddleware();
    this.setupRoutes();
    this.setupWebSocket();
    this.initializeSampleProcesses();
  }

  private setupMiddleware(): void {
    this.app.use(express.json());
    this.app.use(express.urlencoded({ extended: true }));
    
    // CORS headers
    this.app.use((req, res, next) => {
      res.header('Access-Control-Allow-Origin', '*');
      res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
      res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization');
      if (req.method === 'OPTIONS') {
        res.sendStatus(200);
      } else {
        next();
      }
    });

    // Request logging
    this.app.use((req, res, next) => {
      this.logger.debug(`${req.method} ${req.path}`, { body: req.body });
      next();
    });
  }

  private setupRoutes(): void {
    // Health check
    this.app.get('/health', (req, res) => {
      res.json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        uptime: process.uptime()
      });
    });

    // Engine info
    this.app.get('/info', (req, res) => {
      const engineInfo: YingdaoEngineInfo = {
        name: 'Yingdao RPA Engine',
        version: '2.0.0',
        capabilities: [
          'process_execution',
          'debug_mode',
          'variable_management',
          'logging',
          'web_automation',
          'data_extraction',
          'file_operations',
          'api_integration'
        ],
        status: 'running'
      };
      
      res.json(engineInfo);
    });

    // Process management
    this.app.get('/processes', (req, res) => {
      const processes = Array.from(this.processes.values());
      res.json({ processes });
    });

    this.app.post('/processes', (req, res) => {
      try {
        const { name, description, variables } = req.body;
        
        if (!name) {
          return res.status(400).json({ error: 'Process name is required' });
        }

        const process: ProcessInfo = {
          id: `process_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          name,
          description: description || '',
          status: 'idle',
          created: new Date(),
          modified: new Date(),
          variables: variables || {}
        };

        this.processes.set(process.id, process);
        
        this.logger.info(`Created process: ${process.name} (${process.id})`);
        
        res.status(201).json(process);
      } catch (error) {
        this.logger.error('Failed to create process:', error);
        res.status(500).json({ error: 'Failed to create process' });
      }
    });

    this.app.get('/processes/:id', (req, res) => {
      const process = this.processes.get(req.params.id);
      if (!process) {
        return res.status(404).json({ error: 'Process not found' });
      }
      res.json(process);
    });

    this.app.put('/processes/:id', (req, res) => {
      const process = this.processes.get(req.params.id);
      if (!process) {
        return res.status(404).json({ error: 'Process not found' });
      }

      const { name, description, variables } = req.body;
      
      if (name) process.name = name;
      if (description) process.description = description;
      if (variables) process.variables = { ...process.variables, ...variables };
      
      process.modified = new Date();
      
      this.processes.set(req.params.id, process);
      
      res.json(process);
    });

    this.app.delete('/processes/:id', (req, res) => {
      if (!this.processes.has(req.params.id)) {
        return res.status(404).json({ error: 'Process not found' });
      }

      this.processes.delete(req.params.id);
      res.status(204).send();
    });

    // Process execution
    this.app.post('/processes/:id/execute', async (req, res) => {
      try {
        const process = this.processes.get(req.params.id);
        if (!process) {
          return res.status(404).json({ error: 'Process not found' });
        }

        const { parameters, debugMode = false } = req.body;
        
        const executionId = `exec_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        
        // Update process status
        process.status = 'running';
        this.processes.set(req.params.id, process);

        // Start execution
        this.executeProcess(req.params.id, executionId, parameters || {}, debugMode);
        
        res.json({
          executionId,
          processId: req.params.id,
          status: 'started',
          message: 'Process execution started'
        });

      } catch (error) {
        this.logger.error('Failed to start process execution:', error);
        res.status(500).json({ error: 'Failed to start process execution' });
      }
    });

    this.app.get('/executions/:id', (req, res) => {
      const execution = this.activeExecutions.get(req.params.id);
      if (!execution) {
        return res.status(404).json({ error: 'Execution not found' });
      }
      res.json(execution);
    });

    this.app.post('/executions/:id/stop', (req, res) => {
      const execution = this.activeExecutions.get(req.params.id);
      if (!execution) {
        return res.status(404).json({ error: 'Execution not found' });
      }

      execution.status = 'stopped';
      execution.endTime = new Date();
      
      res.json({ message: 'Execution stopped' });
    });

    // Variables
    this.app.get('/processes/:id/variables', (req, res) => {
      const process = this.processes.get(req.params.id);
      if (!process) {
        return res.status(404).json({ error: 'Process not found' });
      }
      res.json(process.variables);
    });

    this.app.put('/processes/:id/variables', (req, res) => {
      const process = this.processes.get(req.params.id);
      if (!process) {
        return res.status(404).json({ error: 'Process not found' });
      }

      const { variables } = req.body;
      if (variables) {
        process.variables = { ...process.variables, ...variables };
        process.modified = new Date();
        this.processes.set(req.params.id, process);
      }

      res.json(process.variables);
    });

    // Error handling
    this.app.use((err: any, req: express.Request, res: express.Response, next: express.NextFunction) => {
      this.logger.error('Unhandled error:', err);
      res.status(500).json({ error: 'Internal server error' });
    });

    this.app.use((req, res) => {
      res.status(404).json({ error: 'Not found' });
    });
  }

  private setupWebSocket(): void {
    this.wss.on('connection', (ws) => {
      this.logger.info('WebSocket client connected');
      
      ws.on('message', (message) => {
        try {
          const data = JSON.parse(message.toString());
          this.handleWebSocketMessage(ws, data);
        } catch (error) {
          this.logger.error('Invalid WebSocket message:', error);
          ws.send(JSON.stringify({ error: 'Invalid message format' }));
        }
      });

      ws.on('close', () => {
        this.logger.info('WebSocket client disconnected');
      });

      // Send initial data
      ws.send(JSON.stringify({
        type: 'connected',
        message: 'Connected to Yingdao RPA Engine'
      }));
    });
  }

  private handleWebSocketMessage(ws: any, data: any): void {
    switch (data.type) {
      case 'subscribe':
        // Handle subscription to execution events
        break;
      case 'get_status':
        ws.send(JSON.stringify({
          type: 'status',
          data: {
            processes: Array.from(this.processes.values()),
            executions: Array.from(this.activeExecutions.values())
          }
        }));
        break;
      default:
        this.logger.warn('Unknown WebSocket message type:', data.type);
    }
  }

  private async executeProcess(
    processId: string, 
    executionId: string, 
    parameters: Record<string, any>, 
    debugMode: boolean
  ): Promise<void> {
    const process = this.processes.get(processId);
    if (!process) return;

    const execution: any = {
      id: executionId,
      processId,
      status: 'running',
      startTime: new Date(),
      parameters,
      debugMode,
      progress: {
        currentStep: 0,
        totalSteps: 5,
        percentage: 0,
        currentActivity: 'Initializing...'
      },
      logs: [] as any[]
    };

    this.activeExecutions.set(executionId, execution);

    // Broadcast start event
    this.broadcastEvent({
      type: 'execution_started',
      executionId,
      processId,
      timestamp: new Date()
    });

    try {
      // Simulate process execution
      const steps = [
        { name: 'Initialize', duration: 1000, activity: 'Initializing process environment...' },
        { name: 'Load Data', duration: 2000, activity: 'Loading input data...' },
        { name: 'Process', duration: 3000, activity: 'Processing data...' },
        { name: 'Validate', duration: 1500, activity: 'Validating results...' },
        { name: 'Finalize', duration: 1000, activity: 'Finalizing process...' }
      ];

      for (let i = 0; i < steps.length; i++) {
        const step = steps[i];
        
        // Update progress
        execution.progress.currentStep = i + 1;
        execution.progress.percentage = Math.round(((i + 1) / steps.length) * 100);
        execution.progress.currentActivity = step.activity;

        // Add log
        execution.logs.push({
          id: `log_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          timestamp: new Date(),
          level: 'info',
          message: `Step ${i + 1}: ${step.name}`
        });

        // Broadcast progress
        this.broadcastEvent({
          type: 'execution_progress',
          executionId,
          processId,
          progress: execution.progress,
          timestamp: new Date()
        });

        // Simulate processing time
        await this.delay(step.duration);
      }

      // Complete execution
      execution.status = 'completed';
      execution.endTime = new Date();
      execution.progress.percentage = 100;
      execution.progress.currentActivity = 'Completed';

      // Update process status
      process.status = 'idle';
      this.processes.set(processId, process);

      // Broadcast completion
      this.broadcastEvent({
        type: 'execution_completed',
        executionId,
        processId,
        result: {
          success: true,
          executionTime: execution.endTime.getTime() - execution.startTime.getTime(),
          output: {
            message: 'Process completed successfully',
            processedItems: Math.floor(Math.random() * 100) + 1,
            variables: process.variables
          }
        },
        timestamp: new Date()
      });

      this.logger.info(`Process execution completed: ${executionId}`);

    } catch (error) {
      execution.status = 'error';
      execution.endTime = new Date();
      execution.error = error instanceof Error ? error.message : String(error);

      process.status = 'error';
      this.processes.set(processId, process);

      this.broadcastEvent({
        type: 'execution_error',
        executionId,
        processId,
        error: execution.error,
        timestamp: new Date()
      });

      this.logger.error(`Process execution failed: ${executionId}`, error);
    }

    // Keep execution in history for a while
    setTimeout(() => {
      this.activeExecutions.delete(executionId);
    }, 300000); // 5 minutes
  }

  private broadcastEvent(event: any): void {
    const message = JSON.stringify(event);
    this.wss.clients.forEach((client: any) => {
      if (client.readyState === 1) { // WebSocket.OPEN
        client.send(message);
      }
    });
  }

  private initializeSampleProcesses(): void {
    const sampleProcesses: ProcessInfo[] = [
      {
        id: 'sample_web_automation',
        name: 'Web Data Extraction',
        description: 'Extract data from web pages automatically',
        status: 'idle',
        created: new Date(),
        modified: new Date(),
        variables: {
          url: 'https://example.com',
          selectors: ['title', 'content'],
          outputFormat: 'json'
        }
      },
      {
        id: 'sample_file_processing',
        name: 'File Processing Workflow',
        description: 'Process and transform files',
        status: 'idle',
        created: new Date(),
        modified: new Date(),
        variables: {
          inputPath: './input',
          outputPath: './output',
          fileTypes: ['csv', 'xlsx', 'txt']
        }
      },
      {
        id: 'sample_api_integration',
        name: 'API Data Sync',
        description: 'Synchronize data between systems via API',
        status: 'idle',
        created: new Date(),
        modified: new Date(),
        variables: {
          apiUrl: 'https://api.example.com',
          apiKey: 'demo_key',
          syncInterval: 300
        }
      }
    ];

    sampleProcesses.forEach(process => {
      this.processes.set(process.id, process);
    });
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  async start(): Promise<void> {
    return new Promise((resolve, reject) => {
      this.server.listen(this.port, () => {
        this.logger.info(`Yingdao RPA Engine server started on port ${this.port}`);
        resolve();
      }).on('error', (error: any) => {
        this.logger.error('Failed to start server:', error);
        reject(error);
      });
    });
  }

  async stop(): Promise<void> {
    return new Promise((resolve) => {
      this.server.close(() => {
        this.logger.info('Yingdao RPA Engine server stopped');
        resolve();
      });
      
      // Close WebSocket connections
      this.wss.close();
    });
  }

  getPort(): number {
    return this.port;
  }

  getActiveProcesses(): ProcessInfo[] {
    return Array.from(this.processes.values()).filter(p => p.status === 'running');
  }

  getActiveExecutions(): any[] {
    return Array.from(this.activeExecutions.values());
  }
}