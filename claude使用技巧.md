# Claude Code 使用技巧

## 基础使用技巧

### 1. 简洁高效的沟通
- **直接提问**：直接提出你的问题，不需要多余的客套话
- **具体明确**：提供清晰的指令和具体的要求
- **简洁回复**：Claude会自动保持回复简洁，专注于解决问题

### 2. 文件操作
- **绝对路径**：所有文件操作都使用绝对路径
- **先读后写**：编辑文件前先读取文件内容
- **批量操作**：使用MultiEdit工具进行多处修改

### 3. 代码开发
- **遵循约定**：遵循现有代码的命名约定和风格
- **安全第一**：只进行防御性安全任务
- **测试验证**：完成后运行测试和类型检查

## 高级功能

### 1. 任务管理
- **TodoWrite工具**：自动跟踪复杂任务的进度
- **任务分解**：将大任务分解为可管理的小步骤
- **进度追踪**：实时更新任务状态

### 2. 搜索和导航
- **Grep工具**：强大的正则表达式搜索
- **Glob工具**：文件模式匹配
- **Task工具**：复杂的代码搜索和分析

### 3. 网络功能
- **WebFetch**：获取和分析网页内容
- **WebSearch**：搜索最新信息
- **MCP工具**：使用第三方集成的工具

## 最佳实践

### 1. 代码质量
- **不要添加注释**：除非明确要求
- **保持一致性**：遵循项目现有模式
- **安全性**：不要提交敏感信息

### 2. 效率优化
- **并行操作**：同时执行多个工具调用
- **批量处理**：一次性完成多个相关操作
- **缓存利用**：利用内置缓存机制

### 3. 错误处理
- **检查状态**：验证文件和目录存在
- **处理异常**：添加适当的错误处理
- **用户确认**：重要操作前请求确认

## 常用命令

### 文件操作
```bash
# 读取文件
Read file_path="D:\path\to\file.txt"

# 编辑文件
Edit file_path="D:\path\to\file.txt" old_string="旧文本" new_string="新文本"

# 批量编辑
MultiEdit file_path="D:\path\to\file.txt" edits=[{"old_string": "...", "new_string": "..."}]
```

### 搜索功能
```bash
# 搜索文件内容
Grep pattern="function.*name" glob="*.js"

# 搜索文件名
Glob pattern="**/*.py"
```

### 系统操作
```bash
# 执行命令
Bash command="npm install" description="安装依赖"

# 创建任务
Task description="代码审查" prompt="审查代码质量" subagent_type="code-reviewer"
```

## 故障排除

### 常见问题
1. **文件权限**：确保有足够的文件访问权限
2. **路径问题**：使用绝对路径，避免相对路径
3. **工具超时**：大文件操作可能需要调整超时设置

### 获取帮助
- 使用 `/help` 命令获取使用帮助
- 访问官方文档：https://docs.anthropic.com/en/docs/claude-code
- 报告问题：https://github.com/anthropics/claude-code/issues

## 专业提示

### 1. 代码审查
- 使用专门的代码审查代理
- 检查代码风格和最佳实践
- 验证安全性和性能

### 2. 项目管理
- 使用任务列表跟踪复杂项目
- 定期更新进度状态
- 分阶段完成大任务

### 3. 学习和改进
- 查看Claude的编辑建议
- 理解代码修改的原因
- 逐步提高代码质量

---

*最后更新：2025年7月*