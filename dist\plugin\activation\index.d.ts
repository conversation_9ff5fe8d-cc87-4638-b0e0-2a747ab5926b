import { PluginLifecycleManager } from './lifecycle-manager';
import { CursorIntegration } from './cursor-integration';
import { PluginContext } from '../../types';
export declare class PluginActivator {
    private lifecycleManager;
    private cursorIntegration;
    private logger;
    constructor();
    activate(context?: Partial<PluginContext>): Promise<void>;
    deactivate(): Promise<void>;
    getLifecycleManager(): PluginLifecycleManager;
    getCursorIntegration(): CursorIntegration;
    getState(): import("../../types").PluginState;
    isActive(): boolean;
}
export declare const pluginActivator: PluginActivator;
export { PluginLifecycleManager, CursorIntegration };
//# sourceMappingURL=index.d.ts.map