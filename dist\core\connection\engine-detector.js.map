{"version": 3, "file": "engine-detector.js", "sourceRoot": "", "sources": ["../../../src/core/connection/engine-detector.ts"], "names": [], "mappings": ";;;AACA,uCAA0E;AAC1E,+CAAkD;AAElD,MAAa,iBAAiB;IAA9B;QACU,WAAM,GAAG,IAAA,qBAAY,EAAC,mBAAmB,CAAC,CAAC;IAuErD,CAAC;IArEC,KAAK,CAAC,kBAAkB;QACtB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,gCAAgC,CAAC,CAAC;QAEnD,MAAM,OAAO,GAAgB,EAAE,CAAC;QAEhC,IAAI,CAAC;YACH,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,mBAAmB,EAAE,CAAC;YACvD,IAAI,aAAa,EAAE,CAAC;gBAClB,OAAO,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YAC9B,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAC;QAC9D,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAEO,KAAK,CAAC,mBAAmB;QAC/B,MAAM,YAAY,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;QAE9C,KAAK,MAAM,IAAI,IAAI,YAAY,EAAE,CAAC;YAChC,IAAI,CAAC;gBACH,MAAM,MAAM,GAAqB;oBAC/B,IAAI,EAAE,WAAW;oBACjB,IAAI;oBACJ,OAAO,EAAE,IAAI;oBACb,aAAa,EAAE,CAAC;iBACjB,CAAC;gBAEF,MAAM,UAAU,GAAG,IAAI,aAAa,CAAC,MAAM,CAAC,CAAC;gBAC7C,MAAM,WAAW,GAAG,MAAM,UAAU,CAAC,cAAc,EAAE,CAAC;gBAEtD,IAAI,WAAW,EAAE,CAAC;oBAChB,MAAM,UAAU,GAAG,MAAM,UAAU,CAAC,aAAa,EAAE,CAAC;oBACpD,OAAO;wBACL,EAAE,EAAE,WAAW,IAAI,EAAE;wBACrB,IAAI,EAAE,oBAAoB;wBAC1B,OAAO,EAAE,UAAU,CAAC,OAAO,IAAI,SAAS;wBACxC,MAAM,EAAE,SAAS;wBACjB,QAAQ,EAAE,oBAAoB,IAAI,EAAE;wBACpC,YAAY,EAAE,UAAU,CAAC,YAAY,IAAI,EAAE;qBAC5C,CAAC;gBACJ,CAAC;YACH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,6BAA6B,IAAI,GAAG,EAAE,KAAK,CAAC,CAAC;YACjE,CAAC;QACH,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED,KAAK,CAAC,cAAc,CAAC,MAAiB;QACpC,IAAI,CAAC;YACH,IAAA,yBAAiB,EAAC,MAAM,CAAC,CAAC;YAE1B,MAAM,MAAM,GAAqB;gBAC/B,IAAI,EAAE,IAAI,GAAG,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,QAAQ;gBACvC,IAAI,EAAE,QAAQ,CAAC,IAAI,GAAG,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE;gBACnD,OAAO,EAAE,KAAK;gBACd,aAAa,EAAE,CAAC;aACjB,CAAC;YAEF,MAAM,UAAU,GAAG,IAAI,aAAa,CAAC,MAAM,CAAC,CAAC;YAC7C,OAAO,MAAM,UAAU,CAAC,cAAc,EAAE,CAAC;QAC3C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;YACtD,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;CACF;AAxED,8CAwEC;AAED,MAAa,aAAa;IAKxB,YAAY,MAAwB;QAH5B,WAAM,GAAG,IAAA,qBAAY,EAAC,eAAe,CAAC,CAAC;QAI7C,IAAI,CAAC,MAAM,GAAG,IAAA,gCAAwB,EAAC,MAAM,CAAC,CAAC;QAC/C,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC;IACxC,CAAC;IAED,KAAK,CAAC,cAAc;QAClB,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,UAAU,EAAE,SAAS,EAAE;gBACnE,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,OAAO;aAC7B,CAAC,CAAC;YAEH,OAAO,QAAQ,CAAC,MAAM,KAAK,GAAG,CAAC;QACjC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,yBAAyB,EAAE,KAAK,CAAC,CAAC;YACpD,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED,KAAK,CAAC,aAAa;QACjB,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,UAAU,EAAE,OAAO,EAAE;gBACjE,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,OAAO;aAC7B,CAAC,CAAC;YAEH,OAAO,QAAQ,CAAC,IAAI,CAAC;QACvB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;YACvD,MAAM,IAAI,KAAK,CAAC,kCAAkC,CAAC,CAAC;QACtD,CAAC;IACH,CAAC;IAED,KAAK,CAAC,cAAc,CAAC,QAAgB,EAAE,IAAS;QAC9C,IAAI,SAAc,CAAC;QAEnB,KAAK,IAAI,OAAO,GAAG,CAAC,EAAE,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,aAAa,EAAE,OAAO,EAAE,EAAE,CAAC;YACrE,IAAI,CAAC;gBACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,CACpC,GAAG,IAAI,CAAC,UAAU,EAAE,IAAI,QAAQ,EAAE,EAClC,IAAI,EACJ;oBACE,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,OAAO;oBAC5B,OAAO,EAAE;wBACP,cAAc,EAAE,kBAAkB;wBAClC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,IAAI,EAAE,eAAe,EAAE,UAAU,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,EAAE,CAAC;qBAC/E;iBACF,CACF,CAAC;gBAEF,OAAO,QAAQ,CAAC,IAAI,CAAC;YACvB,CAAC;YAAC,OAAO,KAAU,EAAE,CAAC;gBACpB,SAAS,GAAG,KAAK,CAAC;gBAClB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,mBAAmB,OAAO,GAAG,CAAC,UAAU,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;gBAE1E,IAAI,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,aAAa,GAAG,CAAC,EAAE,CAAC;oBAC5C,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,CAAC,OAAO,GAAG,CAAC,CAAC,CAAC,CAAC;gBACzC,CAAC;YACH,CAAC;QACH,CAAC;QAED,MAAM,SAAS,CAAC;IAClB,CAAC;IAEO,UAAU;QAChB,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ;YACvD,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,GAAG;YACpD,CAAC,CAAC,EAAE,CAAC;QAEP,OAAO,UAAU,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;IACjE,CAAC;IAEO,KAAK,CAAC,EAAU;QACtB,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC;IACzD,CAAC;CACF;AA9ED,sCA8EC"}