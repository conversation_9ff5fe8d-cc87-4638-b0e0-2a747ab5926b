export interface PluginConfig {
  id: string;
  name: string;
  version: string;
  enabled: boolean;
  settings: Record<string, any>;
}

export interface PluginContext {
  workspace: string;
  configuration: PluginConfig;
  logger: any;
}

export interface PluginState {
  status: 'inactive' | 'activating' | 'active' | 'deactivating' | 'error';
  lastError?: string;
  activationTime?: Date;
}

export interface UIComponent {
  id: string;
  type: 'sidebar' | 'panel' | 'statusbar' | 'menu';
  title: string;
  visible: boolean;
  position?: number;
}

export interface PluginEvent {
  type: string;
  payload: any;
  timestamp: Date;
}